<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R74I-PROPOSAL-ID"/>
					<param name="R74I-FUNCTION"/>
					<param name="R74I-USER-ID"/>
					<param name="R74I-QUESTIONS-LIST"/>
					<param name="R74I-DTQ1L09-N1"/>
					<param name="R74I-DTQ1L09-N2"/>
					<param name="R74I-DTQ1L09-N3"/>
					<param name="R74I-DTQ1L09-N4"/>
					<param name="R74I-DTQ1L09-N5"/>
					<param name="R74I-DTQ1L09-N6"/>
					<param name="R74I-DTQ1L10-N1"/>
					<param name="R74I-DTQ1L10-N2"/>
					<param name="R74I-DTQ1L10-N3"/>
					<param name="R74I-DTQ1L10-N4"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB74-INPUT</hostService>
            <applBankNumber>99</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB74</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
