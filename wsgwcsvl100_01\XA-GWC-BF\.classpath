<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="ejbModule"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.module.container"/>
	<classpathentry kind="con" path="org.eclipse.jdt.USER_LIBRARY/x10s-eba-core.libraries"/>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/com.ibm.etools.portal.runtime.provider.v80/wps.base.v80">
		<attributes>
			<attribute name="owner.project.facets" value="jst.ejb"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/WebSphere Portal v8.0 JRE">
		<attributes>
			<attribute name="owner.project.facets" value="java"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="ejbModule"/>
</classpath>
