<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<HTML>
	<HEAD>
		<link rel="stylesheet" href="/XA-UTL-PF/public/css/utl_stylesheet.css" type="text/css"/>
		<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
		<TITLE>Start BIOS V2</TITLE>
	</HEAD>
	<BODY>

	<script type="text/javascript">
	/* function redirectToBiosOnEdge() {
  		var biosUrl = 'https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication';

 		var bankCode = '<c:out value="${requestScope.bankCode}" />';
 		var branchCode = '<c:out value="${requestScope.branchCode}" />';
 		var profile = '<c:out value="${requestScope.profile}" />';
  		var userId = '<c:out value="${requestScope.userId}" />';
  		var hash = '<c:out value="${requestScope.hash}" />';
  		var timestamp = '<c:out value="${requestScope.timestamp}" />';

  		var biosJson = '{ codBanca: \'' + bankCode + '\', codFiliale: \'' + branchCode + '\', profilo: \'' + profile
  		               + '\', userId: \'' + userId + '\', hash: \'' + hash + '\', timestamp: \'' + timestamp + '\''  + '}';

  		alert('Bios Json: ' + biosJson);

  		window.location.href = "microsoft-edge:" + biosUrl;
	}
	redirectToBiosOnEdge();
	*/

	// test




 /* function redirectToBiosOnEdge() {
    var biosUrl = 'https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication';
    var bankCode = '<c:out value="${requestScope.bankCode}" />';
    var branchCode = '<c:out value="${requestScope.branchCode}" />';
    var profile = '<c:out value="${requestScope.profile}" />';
    var userId = '<c:out value="${requestScope.userId}" />';
    var hash = '<c:out value="${requestScope.hash}" />';
    var timestamp = '<c:out value="${requestScope.timestamp}" />';

    var biosJson = {
      codBanca: bankCode,
      codFiliale: branchCode,
      profilo: profile,
      userId: userId,
      hash: hash,
      timestamp: timestamp
    };

    console.log("Redirecting with BIOS JSON:", biosJson);
    redirectToSilos(biosJson);
  }

  async function redirectToSilos(dataSilos) {
    var url = "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";

    var headers = {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
      "Pragma": "no-cache"
    };

    try {
    const response = await fetch(url, {
  method: "POST",
  headers: {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Cache-Control": "no-cache",
    "Pragma": "no-cache"
  },
  credentials: "include",
  body: JSON.stringify(dataSilos)

  });

      if (response.status === 200) {
      //  window.location.replace(response.url || url);
        window.location.href = "microsoft-edge:" + (response.url || url)
        alert('Bios Json: ' + biosJson);
      } else {
        console.error("Authentication failed with status:", response.status);
        msgService.showError("Authentication failed", "Error");
      }
    } catch (error) {

      msgService.showError("Authentication failed", "Error");
    }
  }

*/

function redirectToBiosOnEdge() {
    var bankCode = '<c:out value="${requestScope.bankCode}" />';
    var branchCode = '<c:out value="${requestScope.branchCode}" />';
    var profile = '<c:out value="${requestScope.profile}" />';
    var userId = '<c:out value="${requestScope.userId}" />';
    var hash = '<c:out value="${requestScope.hash}" />';
    var timestamp = '<c:out value="${requestScope.timestamp}" />';

    var biosJson = {
        codBanca: bankCode,
        codFiliale: branchCode,
        profilo: profile,
        userId: userId,
        hash: hash,
        timestamp: timestamp
    };

    console.log("Redirecting with BIOS JSON:", biosJson);
    redirectToSilos(biosJson);
}

async function redirectToSilos(dataSilos) {
    var url = "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";

    try {
        const response = await fetch(url, {
            method: "POST",
            headers: {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache"
            },
            credentials: "include",
            body: JSON.stringify(dataSilos)
        });

        if (response.ok) {
            const result = await response.json();
            console.log("Authentication response:", result);

            if (result && result.silosUrl) {
                // Check if we're in Internet Explorer
                var isIE = /MSIE|Trident/.test(navigator.userAgent);

                if (isIE) {
                    // For IE, try to open in Edge using the microsoft-edge protocol
                    try {
                        window.location.href = "microsoft-edge:" + result.silosUrl;
                        // Fallback: if Edge protocol doesn't work, show message
                        setTimeout(function() {
                            alert('Please open the following URL in Microsoft Edge:\n' + result.silosUrl);
                        }, 1000);
                    } catch (e) {
                        alert('Please open the following URL in Microsoft Edge:\n' + result.silosUrl);
                    }
                } else {
                    // For other browsers, redirect normally
                    window.location.href = result.silosUrl;
                }
            } else {
                console.error("No Silos URL in response:", result);
                alert('Authentication succeeded, but no Silos URL returned.');
            }
        } else {
            console.error("Authentication failed with status:", response.status);
            const errorText = await response.text();
            console.error("Error response:", errorText);

            if (typeof msgService !== "undefined") {
                msgService.showError("Authentication failed: " + response.status, "Error");
            } else {
                alert("Authentication failed with status: " + response.status);
            }
        }
    } catch (error) {
        console.error("Authentication error:", error);
        if (typeof msgService !== "undefined") {
            msgService.showError("Authentication failed: " + error.message, "Error");
        } else {
            alert("Authentication failed: " + error.message);
        }
    }
}

// Start the redirect process
redirectToBiosOnEdge();


// Call main to start the logic







	</script>

	</BODY>
</HTML>