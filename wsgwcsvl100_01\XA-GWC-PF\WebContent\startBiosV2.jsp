<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<HTML>
	<HEAD>
		<link rel="stylesheet" href="/XA-UTL-PF/public/css/utl_stylesheet.css" type="text/css"/>
		<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
		<TITLE>Start BIOS V2</TITLE>
	</HEAD>
	<BODY>
	
	<script type="text/javascript">
	/* function redirectToBiosOnEdge() {
  		var biosUrl = 'https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication';
 		
 		var bankCode = '<c:out value="${requestScope.bankCode}" />';
 		var branchCode = '<c:out value="${requestScope.branchCode}" />';
 		var profile = '<c:out value="${requestScope.profile}" />';
  		var userId = '<c:out value="${requestScope.userId}" />';
  		var hash = '<c:out value="${requestScope.hash}" />';
  		var timestamp = '<c:out value="${requestScope.timestamp}" />';
  
  		var biosJson = '{ codBanca: \'' + bankCode + '\', codFiliale: \'' + branchCode + '\', profilo: \'' + profile 
  		               + '\', userId: \'' + userId + '\', hash: \'' + hash + '\', timestamp: \'' + timestamp + '\''  + '}';
  
  		alert('Bios Json: ' + biosJson);
  
  		window.location.href = "microsoft-edge:" + biosUrl;
	}
	redirectToBiosOnEdge();
	*/
	
	// test
	
	
	

 /* function redirectToBiosOnEdge() {
    var biosUrl = 'https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication';
    var bankCode = '<c:out value="${requestScope.bankCode}" />';
    var branchCode = '<c:out value="${requestScope.branchCode}" />';
    var profile = '<c:out value="${requestScope.profile}" />';
    var userId = '<c:out value="${requestScope.userId}" />';
    var hash = '<c:out value="${requestScope.hash}" />';
    var timestamp = '<c:out value="${requestScope.timestamp}" />';
 
    var biosJson = {
      codBanca: bankCode,
      codFiliale: branchCode,
      profilo: profile,
      userId: userId,
      hash: hash,
      timestamp: timestamp
    };
 
    console.log("Redirecting with BIOS JSON:", biosJson);
    redirectToSilos(biosJson);
  }
 
  async function redirectToSilos(dataSilos) {
    var url = "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";
 
    var headers = {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
      "Pragma": "no-cache"
    };
 
    try {
    const response = await fetch(url, {
  method: "POST",
  headers: {
    "Accept": "application/json",
    "Content-Type": "application/json",
    "Cache-Control": "no-cache",
    "Pragma": "no-cache"
  },
  credentials: "include",
  body: JSON.stringify(dataSilos)
 
  });
 
      if (response.status === 200) {
      //  window.location.replace(response.url || url);
        window.location.href = "microsoft-edge:" + (response.url || url)
        alert('Bios Json: ' + biosJson);
      } else {
        console.error("Authentication failed with status:", response.status);
        msgService.showError("Authentication failed", "Error");
      }
    } catch (error) {
    
      msgService.showError("Authentication failed", "Error");
    }
  }
 
*/

function redirectToBiosOnEdge() {
    var biosUrl = 'https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication';
    var bankCode = '<c:out value="${requestScope.bankCode}" />';
    var branchCode = '<c:out value="${requestScope.branchCode}" />';
    var profile = '<c:out value="${requestScope.profile}" />';
    var userId = '<c:out value="${requestScope.userId}" />';
    var hash = '<c:out value="${requestScope.hash}" />';
    var timestamp = '<c:out value="${requestScope.timestamp}" />';

    var biosJson = {
        codBanca: bankCode,
        codFiliale: branchCode,
        profilo: profile,
        userId: userId,
        hash: hash,
        timestamp: timestamp
    };

    console.log("Redirecting with BIOS JSON:", biosJson);
    redirectToSilos(biosJson);
}

async function redirectToSilos(dataSilos) {
    var url = "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";

    try {
        const response = await fetch(url, {
            method: "POST",
            headers: {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache"
            },
            credentials: "include",
            body: JSON.stringify(dataSilos)
        });

        if (response.status === 200) {
            const result = await response.json();
          
            if (result && result.silosUrl) {
                window.location.href = "microsoft-edge:" + result.silosUrl;
                 
            } else {
                alert('Authentication succeeded, but no Silos URL returned.');
            }
        } else {
            console.error("Authentication failed with status:", response.status);
            if (typeof msgService !== "undefined") {
                msgService.showError("Authentication failed", "Error");
            }
        }
    } catch (error) {
        console.error("Authentication error:", error);
        if (typeof msgService !== "undefined") {
            msgService.showError("Authentication failed", "Error");
        }
    }
}
  redirectToBiosOnEdge();
 

// Call main to start the logic

	
	
	
	
	
	
	</script>

	</BODY>
</HTML>