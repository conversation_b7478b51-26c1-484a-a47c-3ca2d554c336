<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Avocatura">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_AvocaturaResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_AvocaturaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_AvocaturaResponse">

      <wsdl:part element="impl:nprFidi_AvocaturaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_AvocaturaRequest">

      <wsdl:part element="impl:nprFidi_Avocatura" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Avocatura_SEI">

      <wsdl:operation name="nprFidi_Avocatura">

         <wsdl:input message="impl:nprFidi_AvocaturaRequest" name="nprFidi_AvocaturaRequest"/>

         <wsdl:output message="impl:nprFidi_AvocaturaResponse" name="nprFidi_AvocaturaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_AvocaturaSoapBinding" type="impl:NPR_Fidi_Avocatura_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Avocatura">

         <wsdlsoap:operation soapAction="nprFidi_Avocatura"/>

         <wsdl:input name="nprFidi_AvocaturaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_AvocaturaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_AvocaturaService">

      <wsdl:port binding="impl:NPR_Fidi_AvocaturaSoapBinding" name="NPR_Fidi_Avocatura">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Avocatura"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
