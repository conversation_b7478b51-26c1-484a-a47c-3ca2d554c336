<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprPfa">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="n001" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="n002" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x95" nillable="true" type="xsd:string"/>
      <element name="x96" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprPfaResponse">
    <complexType>
     <sequence>
      <element name="nprPfaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprPfaRequest">

      <wsdl:part element="impl:nprPfa" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprPfaResponse">

      <wsdl:part element="impl:nprPfaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Pfa_SEI">

      <wsdl:operation name="nprPfa">

         <wsdl:input message="impl:nprPfaRequest" name="nprPfaRequest"/>

         <wsdl:output message="impl:nprPfaResponse" name="nprPfaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_PfaSoapBinding" type="impl:NPR_Pfa_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprPfa">

         <wsdlsoap:operation soapAction="nprPfa"/>

         <wsdl:input name="nprPfaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprPfaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_PfaService">

      <wsdl:port binding="impl:NPR_PfaSoapBinding" name="NPR_Pfa">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Pfa"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
