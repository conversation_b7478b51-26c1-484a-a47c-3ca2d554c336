<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411999268806">
      <package-mapping id="PackageMapping_1411999268806">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999268806">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411999268806">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411999268806">
         <service-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_LogonService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411999268806">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_LogonService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411999268806">
            <port-name>NPR_Fidi_Logon</port-name>
            <java-port-name>NPR_Fidi_Logon</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411999268806">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Logon_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411999268806">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_Logon_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411999268806">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_LogonSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999268806">
            <java-method-name>nprFidi_Logon</java-method-name>
            <wsdl-operation>nprFidi_Logon</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999268806">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999268806">
                  <wsdl-message id="WSDLMessage_1411999268806">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_LogonRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x00</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999268807">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999268807">
                  <wsdl-message id="WSDLMessage_1411999268807">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_LogonRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x01</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999268808">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999268808">
                  <wsdl-message id="WSDLMessage_1411999268808">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_LogonRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x02</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999268806">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411999268809">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>nprFidi_LogonResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>nprFidi_LogonReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
