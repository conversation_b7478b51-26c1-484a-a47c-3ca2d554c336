<%@page contentType="text/html;charset=UTF-8"%><HTML>
<HEAD>
<TITLE>Result</TITLE>
</HEAD>
<BODY>
<H1>Result</H1>

<jsp:useBean id="CallServiceProxyid" scope="session" class="it.usi.xframe.gwc.wsutil.CallServiceProxy" />
<jsp:useBean id="CallServiceFreeProxyid" scope="session" class="it.usi.xframe.gwc.wsutil.CallServiceFreeProxy" />
<jsp:useBean id="CallServiceTokenProxyid" scope="session" class="it.usi.xframe.gwc.wsutil.CallServiceTokenProxy" />

<%!
public static String markup(String text) {
    if (text == null) {
        return null;
    }

    StringBuffer buffer = new StringBuffer();
    for (int i = 0; i < text.length(); i++) {
        char c = text.charAt(i);
        switch (c) {
            case '<':
                buffer.append("&lt;");
                break;
            case '&':
                buffer.append("&amp;");
                break;
            case '>':
                buffer.append("&gt;");
                break;
            case '"':
                buffer.append("&quot;");
                break;
            default:
                buffer.append(c);
                break;
        }
    }
    return buffer.toString();
}
%>
<%!
public static java.lang.String domWriter(org.w3c.dom.Node node,java.lang.StringBuffer buffer)
{
    if ( node == null ) {
        return "";
    }
    int type = node.getNodeType();
    switch ( type ) {
        case org.w3c.dom.Node.DOCUMENT_NODE: {
            buffer.append(markup("<?xml version=\"1.0\" encoding=\"UTF-8\"?>") + "<br>");
            domWriter(((org.w3c.dom.Document)node).getDocumentElement(),buffer);
            break;
        }
        case org.w3c.dom.Node.ELEMENT_NODE: {
             buffer.append(markup("<" + node.getNodeName()));
            org.w3c.dom.Attr attrs[] = sortAttributes(node.getAttributes());
            for ( int i = 0; i < attrs.length; i++ ) {
                org.w3c.dom.Attr attr = attrs[i];
                buffer.append(" " + attr.getNodeName() + "=\"" + markup(attr.getNodeValue()) + "\"");
            }
             buffer.append(markup(">"));
            org.w3c.dom.NodeList children = node.getChildNodes();
            if ( children != null ) {
                int len = children.getLength();
                for ( int i = 0; i < len; i++ ) {
                if(((org.w3c.dom.Node)children.item(i)).getNodeType() == org.w3c.dom.Node.ELEMENT_NODE)
                buffer.append("<br>");
                domWriter(children.item(i),buffer);
                }
            }
            buffer.append(markup("</" + node.getNodeName() + ">"));
            break;
        }
        case org.w3c.dom.Node.ENTITY_REFERENCE_NODE: {
            org.w3c.dom.NodeList children = node.getChildNodes();
            if ( children != null ) {
                int len = children.getLength();
                for ( int i = 0; i < len; i++ )
                {
                buffer.append(children.item(i));
                }
            }
            break;
        }
        case org.w3c.dom.Node.CDATA_SECTION_NODE: {
            buffer.append(markup(node.getNodeValue()));
            break;
        }
        case org.w3c.dom.Node.TEXT_NODE:{
            buffer.append(markup(node.getNodeValue()));
            break;
        }
        case org.w3c.dom.Node.PROCESSING_INSTRUCTION_NODE:{
            buffer.append(markup("<?"));
            buffer.append(node.getNodeName());
            String data = node.getNodeValue();
            if ( data != null && data.length() > 0 ){
                buffer.append(" ");
                buffer.append(data);
            }
            buffer.append(markup("?>"));
            break;
        }
        }
    return buffer.toString();
}
%>
<%!
public static org.w3c.dom.Attr[] sortAttributes(org.w3c.dom.NamedNodeMap attrs)
{
    int len = (attrs != null) ? attrs.getLength() : 0;
    org.w3c.dom.Attr array[] = new org.w3c.dom.Attr[len];
    for ( int i = 0; i < len; i++ ){
        array[i] = (org.w3c.dom.Attr)attrs.item(i);
    }
    for ( int i = 0; i < len - 1; i++ ) {
        String name  = array[i].getNodeName();
        int    index = i;
        for ( int j = i + 1; j < len; j++ ) {
            String curName = array[j].getNodeName();
            if ( curName.compareTo(name) < 0 ) {
                name  = curName;
                index = j;
            }
        }
        if ( index != i ) {
            org.w3c.dom.Attr temp    = array[i];
            array[i]     = array[index];
            array[index] = temp;
        }
    }
    return (array);
}
%>


<%
String method = request.getParameter("method");
int methodID = 0;
if (method == null) methodID = -1;

if(methodID != -1) methodID = Integer.parseInt(method);
boolean gotMethod = false;

try {
switch (methodID){ 
case 2:
        gotMethod = true;
        String useJNDI_0id=  markup(request.getParameter("useJNDI5"));
        boolean useJNDI_0idTemp  = Boolean.valueOf(useJNDI_0id).booleanValue();
        CallServiceProxyid.useJNDI(useJNDI_0idTemp);
break;
case 3:
        gotMethod = true;
        String useJNDI_1id=  markup(request.getParameter("useJNDI5"));
        boolean useJNDI_1idTemp  = Boolean.valueOf(useJNDI_1id).booleanValue();
        CallServiceTokenProxyid.useJNDI(useJNDI_1idTemp);
break;
case 4:
        gotMethod = true;
        String useJNDI_2id=  markup(request.getParameter("useJNDI5"));
        boolean useJNDI_2idTemp  = Boolean.valueOf(useJNDI_2id).booleanValue();
        CallServiceFreeProxyid.useJNDI(useJNDI_2idTemp);
break;
case 7:
        gotMethod = true;
        java.lang.String getEndpoint7mtemp = CallServiceProxyid.getEndpoint();
if(getEndpoint7mtemp == null){
%>
<%=getEndpoint7mtemp %>
<%
}else{
        String tempResultreturnp8 = markup(String.valueOf(getEndpoint7mtemp));
        %>
        <%= tempResultreturnp8 %>
        <%
}
break;
case 8:
        gotMethod = true;
        java.lang.String getEndpoint7bmtemp = CallServiceTokenProxyid.getEndpoint();
if(getEndpoint7bmtemp == null){
%>
<%=getEndpoint7bmtemp %>
<%
}else{
        String tempResultreturnp8b = markup(String.valueOf(getEndpoint7bmtemp));
        %>
        <%= tempResultreturnp8b %>
        <%
}
break;
case 9:
        gotMethod = true;
        java.lang.String getEndpoint7cmtemp = CallServiceFreeProxyid.getEndpoint();
if(getEndpoint7cmtemp == null){
%>
<%=getEndpoint7cmtemp %>
<%
}else{
        String tempResultreturnp8c = markup(String.valueOf(getEndpoint7cmtemp));
        %>
        <%= tempResultreturnp8c %>
        <%
}
break;
case 10:
        gotMethod = true;
        String endpoint_1id=  markup(request.getParameter("endpoint13"));
        java.lang.String endpoint_1idTemp  = endpoint_1id;
        CallServiceProxyid.setEndpoint(endpoint_1idTemp);
break;
case 11:
        gotMethod = true;
        String endpoint_1bid=  markup(request.getParameter("endpoint13"));
        java.lang.String endpoint_1bidTemp  = endpoint_1bid;
        CallServiceTokenProxyid.setEndpoint(endpoint_1bidTemp);
break;
case 12:
        gotMethod = true;
        String endpoint_1cid=  markup(request.getParameter("endpoint13"));
        java.lang.String endpoint_1cidTemp  = endpoint_1cid;
        CallServiceFreeProxyid.setEndpoint(endpoint_1cidTemp);
break;
case 15:
        gotMethod = true;
        it.usi.xframe.gwc.wsutil.CallService getCallService15mtemp = CallServiceProxyid.getCallService();
if(getCallService15mtemp == null){
%>
<%=getCallService15mtemp %>
<%
}else{
        if(getCallService15mtemp!= null){
        String tempreturnp16 = getCallService15mtemp.toString();
        %>
        <%=tempreturnp16%>
        <%
        }}
break;
case 16:
        gotMethod = true;
        it.usi.xframe.gwc.wsutil.CallServiceToken getCallService15bmtemp = CallServiceTokenProxyid.getCallServiceToken();
if(getCallService15bmtemp == null){
%>
<%=getCallService15bmtemp %>
<%
}else{
        if(getCallService15bmtemp!= null){
        String tempreturnp16b = getCallService15bmtemp.toString();
        %>
        <%=tempreturnp16b%>
        <%
        }}
break;
case 17:
        gotMethod = true;
        it.usi.xframe.gwc.wsutil.CallServiceFree getCallService15cmtemp = CallServiceFreeProxyid.getCallServiceFree();
if(getCallService15cmtemp == null){
%>
<%=getCallService15cmtemp %>
<%
}else{
        if(getCallService15cmtemp!= null){
        String tempreturnp16c = getCallService15cmtemp.toString();
        %>
        <%=tempreturnp16c%>
        <%
        }}
break;
case 18:
        gotMethod = true;
        String service_2id=  markup(request.getParameter("service21"));
        java.lang.String service_2idTemp  = service_2id;
        String params_3id=  markup(request.getParameter("params23"));
        java.lang.String params_3idTemp  = params_3id;
        String sep1_4id=  markup(request.getParameter("sep125"));
        java.lang.String sep1_4idTemp  = sep1_4id;
        String sep2_5id=  markup(request.getParameter("sep227"));
        java.lang.String sep2_5idTemp  = sep2_5id;
        String userid=  markup(request.getParameter("userid"));
        String pwd=  markup(request.getParameter("pwd"));
        
        java.lang.String callService18mtemp = CallServiceProxyid.callServicePWD(service_2idTemp,params_3idTemp,sep1_4idTemp,sep2_5idTemp, userid, pwd);
if(callService18mtemp == null){
%>
<%=callService18mtemp %>
<%
}else{
        String tempResultreturnp19 = markup(String.valueOf(callService18mtemp));
        %>
        <%= tempResultreturnp19 %>
        <%
}
break;
case 20:
        gotMethod = true;
        String serviceName=  markup(request.getParameter("serviceName"));
        java.lang.String serviceNameTemp  = serviceName;
        String paramsList=  markup(request.getParameter("paramsList"));
        java.lang.String paramsListTemp  = paramsList;
        String separatore1=  markup(request.getParameter("separatore1"));
        java.lang.String separatore1Temp  = separatore1;
        String separatore2=  markup(request.getParameter("separatore2"));
        java.lang.String separatore2Temp  = separatore2;
        String userid2=  markup(request.getParameter("userid2"));
        String pwdToken=  markup(request.getParameter("pwdToken"));
        
        java.lang.String callService20mtemp = CallServiceTokenProxyid.callServiceTokenPWD(serviceNameTemp,paramsListTemp,separatore1Temp,separatore2Temp, userid2, pwdToken);
if(callService20mtemp == null){
%>
<%=callService20mtemp %>
<%
}else{
        String tempResultreturnp21 = markup(String.valueOf(callService20mtemp));
        %>
        <%= tempResultreturnp21 %>
        <%
}
break;
case 22:
        gotMethod = true;
        String service_freeid=  markup(request.getParameter("serviceFree"));
        java.lang.String service_freeidTemp  = service_freeid;
        String params_freeid=  markup(request.getParameter("paramsFree"));
        java.lang.String params_freeidTemp  = params_freeid;
        String sep1_freeid=  markup(request.getParameter("sep1Free"));
        java.lang.String sep1_freeidTemp  = sep1_freeid;
        String sep2_freeid=  markup(request.getParameter("sep2Free"));
        java.lang.String sep2_freeidTemp  = sep2_freeid;
        String userid3=  markup(request.getParameter("userid3"));
        
        java.lang.String callService22mtemp = CallServiceFreeProxyid.callServiceFreePWD(service_freeidTemp,params_freeidTemp,sep1_freeidTemp,sep2_freeidTemp,userid3);
if(callService22mtemp == null){
%>
<%=callService22mtemp %>
<%
}else{
        String tempResultreturnp23 = markup(String.valueOf(callService22mtemp));
        %>
        <%= tempResultreturnp23 %>
        <%
}
break;
}
} catch (Exception e) { 
%>
exception: <%= e %>
<%
return;
}
if(!gotMethod){
%>
result: N/A
<%
}
%>
</BODY>
</HTML>