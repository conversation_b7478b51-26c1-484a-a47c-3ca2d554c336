package it.usi.xframe.gwc.wsutil.dto;

import java.io.Serializable;

public class ExperianX033OutputMap implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String nome = "";
	private String via = "";
	private String luogo = "";
	private String provincia = "";
	private String indiDato = "";
	private String dataRegis = "";
	private String importo = "";
	private String soggFavore = "";

	public ExperianX033OutputMap() {
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public String getVia() {
		return via;
	}

	public void setVia(String via) {
		this.via = via;
	}

	public String getLuogo() {
		return luogo;
	}

	public void setLuogo(String luogo) {
		this.luogo = luogo;
	}

	public String getProvincia() {
		return provincia;
	}

	public void setProvincia(String provincia) {
		this.provincia = provincia;
	}

	public String getIndiDato() {
		return indiDato;
	}

	public void setIndiDato(String indiDato) {
		this.indiDato = indiDato;
	}

	public String getDataRegis() {
		return dataRegis;
	}

	public void setDataRegis(String dataRegis) {
		this.dataRegis = dataRegis;
	}

	public String getImporto() {
		return importo;
	}

	public void setImporto(String importo) {
		this.importo = importo;
	}

	public String getSoggFavore() {
		return soggFavore;
	}

	public void setSoggFavore(String soggFavore) {
		this.soggFavore = soggFavore;
	}
}
