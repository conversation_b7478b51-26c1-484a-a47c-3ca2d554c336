/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_Rating {
	
//	Default Constructor - RSA8 migration prerequisites	
	public NPR_Rating(){
			
	}
	
	public String nprRating(String n001, String x97, String n003, String x01, String x02, String x03, String x04, String n004, String n005, String x05, String x06) throws Exception {
		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprRating(n001, x97, n003, x01, x02, x03, x04, n004, n005, x05, x06);
	}     
}