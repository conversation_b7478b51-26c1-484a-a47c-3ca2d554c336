package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.wsutil.dto.ExperianX030OutputMap;
import it.usi.xframe.gwc.wsutil.dto.ExperianX031OutputMap;
import it.usi.xframe.gwc.wsutil.dto.ExperianX032OutputMap;
import it.usi.xframe.gwc.wsutil.dto.ExperianX033OutputMap;
import it.usi.xframe.gwc.wsutil.dto.GetExperianDataInput;
import it.usi.xframe.gwc.wsutil.dto.GetExperianDataOutput;

public class Rating_CreditBureau {

	public Rating_CreditBureau() {
	}
	
	public GetExperianDataOutput getExperianData(GetExperianDataInput input) {
		if (input == null) {
			return null;
		}
		
		// X030
		ExperianX030OutputMap x030 = new ExperianX030OutputMap();
		
		// Set values here for X030...
		
		// X031
		ExperianX031OutputMap x031 = new ExperianX031OutputMap();
		
		// Set values here for X031...
		
		// X032
		ExperianX032OutputMap x032 = new ExperianX032OutputMap();
		
		// Set values here for X032...
		
		// X033
		ExperianX033OutputMap x033 = new ExperianX033OutputMap();
		
		// Set values here for X033...
		
		// output finale
		GetExperianDataOutput daOutput = new GetExperianDataOutput();
		daOutput.setX030(x030);
		daOutput.setX031(x031);
		daOutput.setX032(x032);
		daOutput.setX033(x033);
		
		return daOutput;
	}
}
