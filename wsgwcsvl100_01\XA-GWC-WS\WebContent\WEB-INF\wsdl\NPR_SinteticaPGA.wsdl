<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprSinteticaPGA">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x98" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprSinteticaPGAResponse">
    <complexType>
     <sequence>
      <element name="nprSinteticaPGAReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprSinteticaPGARequest">

      <wsdl:part element="impl:nprSinteticaPGA" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprSinteticaPGAResponse">

      <wsdl:part element="impl:nprSinteticaPGAResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_SinteticaPGA_SEI">

      <wsdl:operation name="nprSinteticaPGA">

         <wsdl:input message="impl:nprSinteticaPGARequest" name="nprSinteticaPGARequest"/>

         <wsdl:output message="impl:nprSinteticaPGAResponse" name="nprSinteticaPGAResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_SinteticaPGASoapBinding" type="impl:NPR_SinteticaPGA_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprSinteticaPGA">

         <wsdlsoap:operation soapAction="nprSinteticaPGA"/>

         <wsdl:input name="nprSinteticaPGARequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprSinteticaPGAResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_SinteticaPGAService">

      <wsdl:port binding="impl:NPR_SinteticaPGASoapBinding" name="NPR_SinteticaPGA">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_SinteticaPGA"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
