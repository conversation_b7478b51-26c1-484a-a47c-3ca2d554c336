<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411999458972">
      <package-mapping id="PackageMapping_1411999458972">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999458972">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411999458972">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411999458972">
         <service-interface>it.usi.xframe.gwc.wsutil.NPR_FidiIterPropostaNewService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411999458972">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_FidiIterPropostaNewService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411999458972">
            <port-name>NPR_FidiIterPropostaNew</port-name>
            <java-port-name>NPR_FidiIterPropostaNew</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411999458972">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_FidiIterPropostaNew_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411999458972">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_FidiIterPropostaNew_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411999458972">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_FidiIterPropostaNewSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999458972">
            <java-method-name>nprFidiIterPropostaNew</java-method-name>
            <wsdl-operation>nprFidiIterPropostaNew</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999458972">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999458972">
                  <wsdl-message id="WSDLMessage_1411999458972">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidiIterPropostaNewRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x00</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999458973">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999458973">
                  <wsdl-message id="WSDLMessage_1411999458973">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidiIterPropostaNewRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x01</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999458974">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999458974">
                  <wsdl-message id="WSDLMessage_1411999458974">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidiIterPropostaNewRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x02</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999458975">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999458975">
                  <wsdl-message id="WSDLMessage_1411999458975">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidiIterPropostaNewRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x03</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999458972">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411999458976">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>nprFidiIterPropostaNewResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>nprFidiIterPropostaNewReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
