/*
 * Created on Feb 17, 2010
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import javax.xml.namespace.QName;
import javax.xml.rpc.handler.GenericHandler;
import javax.xml.rpc.handler.Handler;
import javax.xml.rpc.handler.HandlerInfo;
import javax.xml.rpc.handler.MessageContext;
import javax.xml.rpc.handler.soap.SOAPMessageContext;
import javax.xml.soap.Name;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPFactory;
import javax.xml.soap.SOAPHeader;
import javax.xml.soap.SOAPHeaderElement;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class SoapFreeRequestResponseHandler extends GenericHandler implements Handler {
	private static final Log log                  = LogFactory.getLog(SoapFreeRequestResponseHandler.class);
	private HandlerInfo handlerInfo               = null;
	
	public void init(HandlerInfo handlerInfo){
		  this.handlerInfo = handlerInfo;
	}
	
	public boolean handleRequest(MessageContext messageContext){
     	log.info("<<<INSIDE HANDLE REQUEST>>> ");
		try{
			SOAPMessageContext sctx = (SOAPMessageContext)messageContext;
			SOAPMessage message     = sctx.getMessage();
			SOAPPart sp             = message.getSOAPPart();
			SOAPEnvelope senv       = sp.getEnvelope();
			
			log.info("HEADER FREE: " + senv.getHeader().toString());            
			log.info("BODY: " + senv.getBody().toString());
			
		}catch (Exception e){
			log.error(e, e.getCause());
		}
			 return true;
	}
	   
	public boolean handleResponse(MessageContext messageContext){
		log.info("<<<INSIDE HANDLE RESPONSE>>> ");
		try{
			SOAPMessageContext sctx = (SOAPMessageContext)messageContext;
			SOAPMessage message     = sctx.getMessage();
			SOAPPart sp             = message.getSOAPPart();
			SOAPEnvelope senv       = sp.getEnvelope();
			
			System.out.println("HEADER: " + senv.getHeader().toString());
			System.out.println("BPDY: " + senv.getBody().toString());
				
		  }
		  catch (Exception e)
		  {
			
			log.error(e, e.getCause());
			
		  }
		return true;
   }

	public QName[] getHeaders() {
		// TODO Auto-generated method stub
		  return handlerInfo.getHeaders();
	}

	public boolean handleFault(SOAPMessageContext smc) {
		 log.error("<<<INSIDE SECOND HANDLE FAULT>>> ");
		return true;
	}
	
	
}
