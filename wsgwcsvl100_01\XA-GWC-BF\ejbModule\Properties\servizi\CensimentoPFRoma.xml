<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="2.2.167" utente="pipinato" Timestamp="04/02/2002 16:56:46" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="UANK04CE-STATUS-AGG"/>
					<param name="UANK04CE-TELEFONI-AGG"/>
					<param name="UANK04CE-INDIR-CORR-AGG"/>
					<param name="UANK04CE-ALTRI-DATI-AGG"/>
					<param name="UANK04CE-CODICI-VARI-AGG"/>
					<param name="UANK04CE-SEDE-FISCALE-AGG"/>
					<param name="UANK04CE-DATI-PRIVACY-AGG"/>
					<param name="UANK04CE-DATI-GENERALI-AGG"/>
					<param name="UANK04CE-DATI-AZIENDALI-AGG"/>
					<param name="UANK04CE-PROFESSIONI-ALT-AGG"/>
					<param name="UANK04CE-INTESTAZIONE-LONG-AGG"/>
					<param name="UANK04CE-CLASSIFICAZIONE-ALT-A"/>
					<param name="UANK04CE-NDG"/>
					<param name="UANK04CE-CONTROLLO-INT"/>
					<param name="UANK04CE-CODICE-FISCALE"/>
					<param name="UANK04CE-PARTITA-IVA"/>
					<param name="UANK04CE-SPORTELLO-CAPOFILA"/>
					<param name="UANK04CE-INTESTAZIONE-A"/>
					<param name="UANK04CE-INTESTAZIONE-B"/>
					<param name="UANK04CE-TIPO-NDG"/>
					<param name="UANK04CE-SETTORISTA"/>
					<param name="UANK04CE-PSEUDONIMO"/>
					<param name="UANK04CE-STATO-RES"/>
					<param name="UANK04CE-COMUNE-RES"/>
					<param name="UANK04CE-LOCALITA-RES"/>
					<param name="UANK04CE-PROVINCIA-RES"/>
					<param name="UANK04CE-CAP-RES"/>
					<param name="UANK04CE-VIA-RES"/>
					<param name="UANK04CE-PRESSO-RES"/>
					<param name="UANK04CE-INTESTAZIONE-CORR"/>
					<param name="UANK04CE-STATO-CORR"/>
					<param name="UANK04CE-COMUNE-CORR"/>
					<param name="UANK04CE-LOCALITA-CORR"/>
					<param name="UANK04CE-PROVINCIA-CORR"/>
					<param name="UANK04CE-CAP-CORR"/>
					<param name="UANK04CE-VIA-CORR"/>
					<param name="UANK04CE-PRESSO-CORR"/>
					<param name="UANK04CE-UFFICIO-CORR"/>
					<param name="UANK04CE-CASELLARIO-CORR"/>
					<param name="UANK04CE-CODICE-FISCALE-ESTERO"/>
					<param name="UANK04CE-RAMO"/>
					<param name="UANK04CE-SETTORE"/>
					<param name="UANK04CE-RAMO-ALT"/>
					<param name="UANK04CE-SETTORE-ALT"/>
					<param name="UANK04CE-COMUNE-ALT"/>
					<param name="UANK04CE-PROVINCIA-ALT"/>
					<param name="UANK04CE-STATO-ALT"/>
					<param name="UANK04CE-SEGMENTO"/>
					<param name="UANK04CE-SIT-GIURIDICA"/>
					<param name="UANK04CE-STATUS"/>
					<param name="UANK04CE-UBIC-DOC-RIFERIM"/>
					<param name="UANK04CE-UBIC-DOC-SPORTELLO"/>
					<param name="UANK04CE-TEL-OPERAZIONE-1"/>
					<param name="UANK04CE-TEL-PROGR-1"/>
					<param name="UANK04CE-TIPO-TELEFONO-1"/>
					<param name="UANK04CE-NUMERO-TELEFONO-1"/>
					<param name="UANK04CE-TEL-OPERAZIONE-2"/>
					<param name="UANK04CE-TEL-PROGR-2"/>
					<param name="UANK04CE-TIPO-TELEFONO-2"/>
					<param name="UANK04CE-NUMERO-TELEFONO-2"/>
					<param name="UANK04CE-TEL-OPERAZIONE-3"/>
					<param name="UANK04CE-TEL-PROGR-3"/>
					<param name="UANK04CE-TIPO-TELEFONO-3"/>
					<param name="UANK04CE-NUMERO-TELEFONO-3"/>
					<param name="UANK04CE-TEL-OPERAZIONE-4"/>
					<param name="UANK04CE-TEL-PROGR-4"/>
					<param name="UANK04CE-TIPO-TELEFONO-4"/>
					<param name="UANK04CE-NUMERO-TELEFONO-4"/>
					<param name="UANK04CE-TEL-OPERAZIONE-5"/>
					<param name="UANK04CE-TEL-PROGR-5"/>
					<param name="UANK04CE-TIPO-TELEFONO-5"/>
					<param name="UANK04CE-NUMERO-TELEFONO-5"/>
					<param name="UANK04CE-TEL-OPERAZIONE-6"/>
					<param name="UANK04CE-TEL-PROGR-6"/>
					<param name="UANK04CE-TIPO-TELEFONO-6"/>
					<param name="UANK04CE-NUMERO-TELEFONO-6"/>
					<param name="UANK04CE-TEL-OPERAZIONE-7"/>
					<param name="UANK04CE-TEL-PROGR-7"/>
					<param name="UANK04CE-TIPO-TELEFONO-7"/>
					<param name="UANK04CE-NUMERO-TELEFONO-7"/>
					<param name="UANK04CE-TEL-OPERAZIONE-8"/>
					<param name="UANK04CE-TEL-PROGR-8"/>
					<param name="UANK04CE-TIPO-TELEFONO-8"/>
					<param name="UANK04CE-NUMERO-TELEFONO-8"/>
					<param name="UANK04CE-TEL-OPERAZIONE-9"/>
					<param name="UANK04CE-TEL-PROGR-9"/>
					<param name="UANK04CE-TIPO-TELEFONO-9"/>
					<param name="UANK04CE-NUMERO-TELEFONO-9"/>
					<param name="UANK04CE-TEL-OPERAZIONE-10"/>
					<param name="UANK04CE-TEL-PROGR-10"/>
					<param name="UANK04CE-TIPO-TELEFONO-10"/>
					<param name="UANK04CE-NUMERO-TELEFONO-10"/>
					<param name="UANK04CE-CONSENSO-1"/>
					<param name="UANK04CE-DT-CONSENSO-1"/>
					<param name="UANK04CE-CONSENSO-2"/>
					<param name="UANK04CE-DT-CONSENSO-2"/>
					<param name="UANK04CE-CONSENSO-3"/>
					<param name="UANK04CE-DT-CONSENSO-3"/>
					<param name="UANK04CE-CONSENSO-4"/>
					<param name="UANK04CE-DT-CONSENSO-4"/>
					<param name="UANK04CE-CONSENSO-5"/>
					<param name="UANK04CE-DT-CONSENSO-5"/>
					<param name="UANK04CE-CONSENSO-6"/>
					<param name="UANK04CE-DT-CONSENSO-6"/>
					<param name="UANK04CE-INVIO-INFORMATIVA"/>
					<param name="UANK04CE-ESENZ-FISC-DATA-EM"/>
					<param name="UANK04CE-ESENZ-FISC-DATA-SCAD"/>
					<param name="UANK04CE-ESENZ-FISC-TIPO"/>
					<param name="UANK04CE-OPERATORE"/>
					<param name="UANK04PF-DOCUMENTI-AGG"/>
					<param name="UANK04PF-PREF-COGN-ACQUIS"/>
					<param name="UANK04PF-COGNOME-ACQUISITO"/>
					<param name="UANK04PF-TITOLI"/>
					<param name="UANK04PF-SESSO"/>
					<param name="UANK04PF-DATA-NASCITA"/>
					<param name="UANK04PF-COMUNE-NASCITA"/>
					<param name="UANK04PF-PROVINCIA-NASC"/>
					<param name="UANK04PF-STATO-NASCITA"/>
					<param name="UANK04PF-DATA-DECESSO"/>
					<param name="UANK04PF-EREDITA"/>
					<param name="UANK04PF-PROFESSIONE"/>
					<param name="UANK04PF-PROF-ALT-1"/>
					<param name="UANK04PF-PROF-ALT-2"/>
					<param name="UANK04PF-PROF-ALT-3"/>
					<param name="UANK04PF-PROF-ALT-4"/>
					<param name="UANK04PF-PROF-ALT-5"/>
					<param name="UANK04PF-DOC-OPERAZIONE-1"/>
					<param name="UANK04PF-TIPO-DOC-1"/>
					<param name="UANK04PF-NUMERO-DOC-1"/>
					<param name="UANK04PF-DATA-RIL-DOC-1"/>
					<param name="UANK04PF-PROV-RIL-DOC-1"/>
					<param name="UANK04PF-COMUNE-RIL-DOC-1"/>
					<param name="UANK04PF-STATO-RIL-DOC-1"/>
					<param name="UANK04PF-ENTE-RILAS-DOC-1"/>
					<param name="UANK04PF-DATA-SCA-DOC-1"/>
					<param name="UANK04PF-DOC-OPERAZIONE-2"/>
					<param name="UANK04PF-TIPO-DOC-2"/>
					<param name="UANK04PF-NUMERO-DOC-2"/>
					<param name="UANK04PF-DATA-RIL-DOC-2"/>
					<param name="UANK04PF-PROV-RIL-DOC-2"/>
					<param name="UANK04PF-COMUNE-RIL-DOC-2"/>
					<param name="UANK04PF-STATO-RIL-DOC-2"/>
					<param name="UANK04PF-ENTE-RILAS-DOC-2"/>
					<param name="UANK04PF-DATA-SCA-DOC-2"/>
					<param name="UANK04PF-DOC-OPERAZIONE-3"/>
					<param name="UANK04PF-TIPO-DOC-3"/>
					<param name="UANK04PF-NUMERO-DOC-3"/>
					<param name="UANK04PF-DATA-RIL-DOC-3"/>
					<param name="UANK04PF-PROV-RIL-DOC-3"/>
					<param name="UANK04PF-COMUNE-RIL-DOC-3"/>
					<param name="UANK04PF-STATO-RIL-DOC-3"/>
					<param name="UANK04PF-ENTE-RILAS-DOC-3"/>
					<param name="UANK04PF-DATA-SCA-DOC-3"/>
					<param name="UANK04PF-DOC-OPERAZIONE-4"/>
					<param name="UANK04PF-TIPO-DOC-4"/>
					<param name="UANK04PF-NUMERO-DOC-4"/>
					<param name="UANK04PF-DATA-RIL-DOC-4"/>
					<param name="UANK04PF-PROV-RIL-DOC-4"/>
					<param name="UANK04PF-COMUNE-RIL-DOC-4"/>
					<param name="UANK04PF-STATO-RIL-DOC-4"/>
					<param name="UANK04PF-ENTE-RILAS-DOC-4"/>
					<param name="UANK04PF-DATA-SCA-DOC-4"/>
					<param name="UANK04PF-DOC-OPERAZIONE-5"/>
					<param name="UANK04PF-TIPO-DOC-5"/>
					<param name="UANK04PF-NUMERO-DOC-5"/>
					<param name="UANK04PF-DATA-RIL-DOC-5"/>
					<param name="UANK04PF-PROV-RIL-DOC-5"/>
					<param name="UANK04PF-COMUNE-RIL-DOC-5"/>
					<param name="UANK04PF-STATO-RIL-DOC-5"/>
					<param name="UANK04PF-ENTE-RILAS-DOC-5"/>
					<param name="UANK04PF-DATA-SCA-DOC-5"/>
				</input>
				<output>
					<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request">
						<xsl:output method="xml"/>
						<xsl:template match="/">
							<UANKA004>
								<ROCH-HEADER>
								    <ROCH-STRUCID>ROCH</ROCH-STRUCID>
								    <ROCH-VERSION>0002</ROCH-VERSION>
                                    <ROCH-BSNAME>RBS-XX-EGU-CENSIMENTO-ANAG-NORTF</ROCH-BSNAME>
								    <ROCH-RETURNCODE>0000</ROCH-RETURNCODE>
								    <ROCH-UOWCONTROL>0000</ROCH-UOWCONTROL>
								    <ROCH-ABEND-CODE/>
								    <ROCH-AREA-FREE/>
								</ROCH-HEADER>
								<UANK04-SYS>
									<UANK04-SYS-CO-TERMINALE/>
									<UANK04-SYS-CO-OPERATORE/>
									<UANK04-SYS-CO-FIL-OPER/>
									<UANK04-SYS-CO-BANCA>41</UANK04-SYS-CO-BANCA>
									<UANK04-SYS-FIL/>
									<UANK04-SYS-CO-APPL>AG</UANK04-SYS-CO-APPL>
									<UANK04-SYS-SERVIZIO>AFA-CENSIMENTO-PF</UANK04-SYS-SERVIZIO>
									<UANK04-SYS-FILLER/>
								</UANK04-SYS>
								<UANK04-ERR>
									<UANK04-ERR-RC/>
									<UANK04-ERR-RC-DESC>0000</UANK04-ERR-RC-DESC>
									<UANK04-ERR-RC-DESC-ERRORE/>
									<UANK04-ERR-CODICE-ABEND/>
									<UANK04-ERR-SQLCODE>000000000</UANK04-ERR-SQLCODE>
									<UANK04-ERR-SQLERRMC/>
									<UANK04-ERR-LABEL/>
									<UANK04-ERR-TABEL/>
									<UANK04-ERR-FUNZIONE/>
									<UANK04-ERR-PGM/>
									<UANK04-ERR-CAMPI/>
								</UANK04-ERR>
								<UANK04-SYS-LU-DATI>06659</UANK04-SYS-LU-DATI>
								<UANK04-DATI>
									<UANK04-INPUT>
										<UANK04-AREACENS>
											<UANK04CE-STATUS-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-STATUS-AGG']/text()"/>
											</UANK04CE-STATUS-AGG>
											<UANK04CE-TELEFONI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TELEFONI-AGG']/text()"/>
											</UANK04CE-TELEFONI-AGG>
											<UANK04CE-INDIR-CORR-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-INDIR-CORR-AGG']/text()"/>
											</UANK04CE-INDIR-CORR-AGG>
											<UANK04CE-ALTRI-DATI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UUANK04CE-ALTRI-DATI-AGG']/text()"/>
											</UANK04CE-ALTRI-DATI-AGG>
											<UANK04CE-CODICI-VARI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CODICI-VARI-AGG']/text()"/>
											</UANK04CE-CODICI-VARI-AGG>
											<UANK04CE-SEDE-FISCALE-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-SEDE-FISCALE-AGG']/text()"/>
											</UANK04CE-SEDE-FISCALE-AGG>
											<UANK04CE-DATI-PRIVACY-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DATI-PRIVACY-AGG']/text()"/>
											</UANK04CE-DATI-PRIVACY-AGG>
											<UANK04CE-DATI-GENERALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DATI-GENERALI-AGG']/text()"/>
											</UANK04CE-DATI-GENERALI-AGG>
											<UANK04CE-DATI-AZIENDALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DATI-AZIENDALI-AGG']/text()"/>
											</UANK04CE-DATI-AZIENDALI-AGG>
											<UANK04CE-PROFESSIONI-ALT-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PROFESSIONI-ALT-AGG']/text()"/>
											</UANK04CE-PROFESSIONI-ALT-AGG>
											<UANK04CE-INTESTAZIONE-LONG-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-INTESTAZIONE-LONG-AGG']/text()"/>
											</UANK04CE-INTESTAZIONE-LONG-AGG>
											<UANK04CE-CLASSIFICAZIONE-ALT-A>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CLASSIFICAZIONE-ALT-A']/text()"/>
											</UANK04CE-CLASSIFICAZIONE-ALT-A>
											<UANK04CE-FUNZIONE>I</UANK04CE-FUNZIONE>
											<UANK04CE-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NDG']/text()"/>
											</UANK04CE-NDG>
											<UANK04CE-CONTROLLO-INT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CONTROLLO-INT']/text()"/>
											</UANK04CE-CONTROLLO-INT>
											<UANK04CE-CODICE-FISCALE>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CODICE-FISCALE']/text()"/>
											</UANK04CE-CODICE-FISCALE>
											<UANK04CE-PARTITA-IVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PARTITA-IVA']/text()"/>
											</UANK04CE-PARTITA-IVA>
											<UANK04CE-SPORTELLO-CAPOFILA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-SPORTELLO-CAPOFILA']/text()"/>
											</UANK04CE-SPORTELLO-CAPOFILA>
											<UANK04CE-INTESTAZIONE-A>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-INTESTAZIONE-A']/text()"/>
											</UANK04CE-INTESTAZIONE-A>
											<UANK04CE-INTESTAZIONE-B>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-INTESTAZIONE-B']/text()"/>
											</UANK04CE-INTESTAZIONE-B>
											<UANK04CE-TIPO-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-NDG']/text()"/>
											</UANK04CE-TIPO-NDG>
											<UANK04CE-SETTORISTA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-SETTORISTA']/text()"/>
											</UANK04CE-SETTORISTA>
											<UANK04CE-PSEUDONIMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PSEUDONIMO']/text()"/>
											</UANK04CE-PSEUDONIMO>
											<UANK04CE-STATO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-STATO-RES']/text()"/>
											</UANK04CE-STATO-RES>
											<UANK04CE-COMUNE-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-COMUNE-RES']/text()"/>
											</UANK04CE-COMUNE-RES>
											<UANK04CE-LOCALITA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-LOCALITA-RES']/text()"/>
											</UANK04CE-LOCALITA-RES>
											<UANK04CE-PROVINCIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PROVINCIA-RES']/text()"/>
											</UANK04CE-PROVINCIA-RES>
											<UANK04CE-CAP-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CAP-RES']/text()"/>
											</UANK04CE-CAP-RES>
											<UANK04CE-VIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-VIA-RES']/text()"/>
											</UANK04CE-VIA-RES>
											<UANK04CE-PRESSO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PRESSO-RES']/text()"/>
											</UANK04CE-PRESSO-RES>
											<UANK04CE-INTESTAZIONE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-INTESTAZIONE-CORR']/text()"/>
											</UANK04CE-INTESTAZIONE-CORR>
											<UANK04CE-STATO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-STATO-CORR']/text()"/>
											</UANK04CE-STATO-CORR>
											<UANK04CE-COMUNE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-COMUNE-CORR']/text()"/>
											</UANK04CE-COMUNE-CORR>
											<UANK04CE-LOCALITA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-LOCALITA-CORR']/text()"/>
											</UANK04CE-LOCALITA-CORR>
											<UANK04CE-PROVINCIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PROVINCIA-CORR']/text()"/>
											</UANK04CE-PROVINCIA-CORR>
											<UANK04CE-CAP-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CAP-CORR']/text()"/>
											</UANK04CE-CAP-CORR>
											<UANK04CE-VIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-VIA-CORR']/text()"/>
											</UANK04CE-VIA-CORR>
											<UANK04CE-PRESSO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PRESSO-CORR']/text()"/>
											</UANK04CE-PRESSO-CORR>
											<UANK04CE-UFFICIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-UFFICIO-CORR']/text()"/>
											</UANK04CE-UFFICIO-CORR>
											<UANK04CE-CASELLARIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CASELLARIO-CORR']/text()"/>
											</UANK04CE-CASELLARIO-CORR>
											<UANK04CE-CODICE-FISCALE-ESTERO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CODICE-FISCALE-ESTERO']/text()"/>
											</UANK04CE-CODICE-FISCALE-ESTERO>
											<UANK04CE-RAMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-RAMO']/text()"/>
											</UANK04CE-RAMO>
											<UANK04CE-SETTORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-SETTORE']/text()"/>
											</UANK04CE-SETTORE>
											<UANK04CE-RAMO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-RAMO-ALT']/text()"/>
											</UANK04CE-RAMO-ALT>
											<UANK04CE-SETTORE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-SETTORE-ALT']/text()"/>
											</UANK04CE-SETTORE-ALT>
											<UANK04CE-COMUNE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-COMUNE-ALT']/text()"/>
											</UANK04CE-COMUNE-ALT>
											<UANK04CE-PROVINCIA-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-PROVINCIA-ALT']/text()"/>
											</UANK04CE-PROVINCIA-ALT>
											<UANK04CE-STATO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-STATO-ALT']/text()"/>
											</UANK04CE-STATO-ALT>
											<UANK04CE-SEGMENTO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-SEGMENTO']/text()"/>
											</UANK04CE-SEGMENTO>
											<UANK04CE-SIT-GIURIDICA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-SIT-GIURIDICA']/text()"/>
											</UANK04CE-SIT-GIURIDICA>
											<UANK04CE-STATUS>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-STATUS']/text()"/>
											</UANK04CE-STATUS>
											<UANK04CE-UBIC-DOC-RIFERIM>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-UBIC-DOC-RIFERIM']/text()"/>
											</UANK04CE-UBIC-DOC-RIFERIM>
											<UANK04CE-UBIC-DOC-SPORTELLO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-UBIC-DOC-SPORTELLO']/text()"/>
											</UANK04CE-UBIC-DOC-SPORTELLO>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-1']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-1']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-1']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-1']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-2']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-2']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-2']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-2']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-3']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-3']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-3']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-3']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONEC-4']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-4']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-4']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-4']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-5']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-5']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-5']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-5']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-6']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-6']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-6']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-6']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-7']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-7']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-7']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-7']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-8']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-8']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-8']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-8']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-9']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-9']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-9']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-9']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-TELEFONI>
												<UANK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-OPERAZIONE-10']/text()"/>
												</UANK04CE-TEL-OPERAZIONE>
												<UANK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TEL-PROGR-10']/text()"/>
												</UANK04CE-TEL-PROGR>
												<UANK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-TIPO-TELEFONO-10']/text()"/>
												</UANK04CE-TIPO-TELEFONO>
												<UANK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-NUMERO-TELEFONO-10']/text()"/>
												</UANK04CE-NUMERO-TELEFONO>
											</UANK04CE-TELEFONI>
											<UANK04CE-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CONSENSO-1']/text()"/>
											</UANK04CE-CONSENSO-1>
											<UANK04CE-DT-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DT-CONSENSO-1']/text()"/>
											</UANK04CE-DT-CONSENSO-1>
											<UANK04CE-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CONSENSO-2']/text()"/>
											</UANK04CE-CONSENSO-2>
											<UANK04CE-DT-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DT-CONSENSO-2']/text()"/>
											</UANK04CE-DT-CONSENSO-2>
											<UANK04CE-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CONSENSO-3']/text()"/>
											</UANK04CE-CONSENSO-3>
											<UANK04CE-DT-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DT-CONSENSO-3']/text()"/>
											</UANK04CE-DT-CONSENSO-3>
											<UANK04CE-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CONSENSO-4']/text()"/>
											</UANK04CE-CONSENSO-4>
											<UANK04CE-DT-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DT-CONSENSO-4']/text()"/>
											</UANK04CE-DT-CONSENSO-4>
											<UANK04CE-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CONSENSO-5']/text()"/>
											</UANK04CE-CONSENSO-5>
											<UANK04CE-DT-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DT-CONSENSO-5']/text()"/>
											</UANK04CE-DT-CONSENSO-5>
											<UANK04CE-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-CONSENSO-6']/text()"/>
											</UANK04CE-CONSENSO-6>
											<UANK04CE-DT-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-DT-CONSENSO-6']/text()"/>
											</UANK04CE-DT-CONSENSO-6>
											<UANK04CE-INVIO-INFORMATIVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-INVIO-INFORMATIVA']/text()"/>
											</UANK04CE-INVIO-INFORMATIVA>
											<UANK04CE-ESENZ-FISC-DATA-EM>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-ESENZ-FISC-DATA-EM']/text()"/>
											</UANK04CE-ESENZ-FISC-DATA-EM>
											<UANK04CE-ESENZ-FISC-DATA-SCAD>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-ESENZ-FISC-DATA-SCAD']/text()"/>
											</UANK04CE-ESENZ-FISC-DATA-SCAD>
											<UANK04CE-ESENZ-FISC-TIPO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-ESENZ-FISC-TIPO']/text()"/>
											</UANK04CE-ESENZ-FISC-TIPO>
											<UANK04CE-OPERATORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04CE-OPERATORE']/text()"/>
											</UANK04CE-OPERATORE>
											<FILLER1/>
										</UANK04-AREACENS>
										<UANK04-AREACEPF>
											<UANK04PF-DOCUMENTI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DOCUMENTI-AGG']/text()"/>
											</UANK04PF-DOCUMENTI-AGG>
											<UANK04PF-PREF-COGN-ACQUIS>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PREF-COGN-ACQUIS']/text()"/>
											</UANK04PF-PREF-COGN-ACQUIS>
											<UANK04PF-COGNOME-ACQUISITO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-COGNOME-ACQUISITO']/text()"/>
											</UANK04PF-COGNOME-ACQUISITO>
											<UANK04PF-TITOLI>
												<xsl:value-of select="xgen:request/xgen:param[@name='UUANK04PF-TITOLI']/text()"/>
											</UANK04PF-TITOLI>
											<UANK04PF-SESSO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-SESSO']/text()"/>
											</UANK04PF-SESSO>
											<UANK04PF-DATA-NASCITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-NASCITA']/text()"/>
											</UANK04PF-DATA-NASCITA>
											<UANK04PF-COMUNE-NASCITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-COMUNE-NASCITA']/text()"/>
											</UANK04PF-COMUNE-NASCITA>
											<UANK04PF-PROVINCIA-NASC>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROVINCIA-NASC']/text()"/>
											</UANK04PF-PROVINCIA-NASC>
											<UANK04PF-STATO-NASCITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-STATO-NASCITA']/text()"/>
											</UANK04PF-STATO-NASCITA>
											<UANK04PF-DATA-DECESSO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-DECESSO']/text()"/>
											</UANK04PF-DATA-DECESSO>
											<UANK04PF-EREDITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-EREDITA']/text()"/>
											</UANK04PF-EREDITA>
											<UANK04PF-PROFESSIONE>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROFESSIONE']/text()"/>
											</UANK04PF-PROFESSIONE>
											<UANK04PF-PROFESSIONE-ALT>
												<UANK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROF-ALT-1']/text()"/>
												</UANK04PF-PROF-ALT>
											</UANK04PF-PROFESSIONE-ALT>
											<UANK04PF-PROFESSIONE-ALT>
												<UANK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROF-ALT-2']/text()"/>
												</UANK04PF-PROF-ALT>
											</UANK04PF-PROFESSIONE-ALT>
											<UANK04PF-PROFESSIONE-ALT>
												<UANK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROF-ALT-3']/text()"/>
												</UANK04PF-PROF-ALT>
											</UANK04PF-PROFESSIONE-ALT>
											<UANK04PF-PROFESSIONE-ALT>
												<UANK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROF-ALT-4']/text()"/>
												</UANK04PF-PROF-ALT>
											</UANK04PF-PROFESSIONE-ALT>
											<UANK04PF-PROFESSIONE-ALT>
												<UANK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROF-ALT-5']/text()"/>
												</UANK04PF-PROF-ALT>
											</UANK04PF-PROFESSIONE-ALT>
											<UANK04PF-DOCUMENTI>
												<UANK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DOC-OPERAZIONE-1']/text()"/>
												</UANK04PF-DOC-OPERAZIONE>
												<UANK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-TIPO-DOC-1']/text()"/>
												</UANK04PF-TIPO-DOC>
												<UANK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-NUMERO-DOC-1']/text()"/>
												</UANK04PF-NUMERO-DOC>
												<UANK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-RIL-DOC-1']/text()"/>
												</UANK04PF-DATA-RIL-DOC>
												<UANK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROV-RIL-DOC-1']/text()"/>
												</UANK04PF-PROV-RIL-DOC>
												<UANK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-COMUNE-RIL-DOC-1']/text()"/>
												</UANK04PF-COMUNE-RIL-DOC>
												<UANK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-STATO-RIL-DOC-1']/text()"/>
												</UANK04PF-STATO-RIL-DOC>
												<UANK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-ENTE-RILAS-DOC-1']/text()"/>
												</UANK04PF-ENTE-RILAS-DOC>
												<UANK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-SCA-DOC-1']/text()"/>
												</UANK04PF-DATA-SCA-DOC>
											</UANK04PF-DOCUMENTI>
											<UANK04PF-DOCUMENTI>
												<UANK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DOC-OPERAZIONE-2']/text()"/>
												</UANK04PF-DOC-OPERAZIONE>
												<UANK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-TIPO-DOC-2']/text()"/>
												</UANK04PF-TIPO-DOC>
												<UANK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-NUMERO-DOC-2']/text()"/>
												</UANK04PF-NUMERO-DOC>
												<UANK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-RIL-DOC-2']/text()"/>
												</UANK04PF-DATA-RIL-DOC>
												<UANK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROV-RIL-DOC-2']/text()"/>
												</UANK04PF-PROV-RIL-DOC>
												<UANK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-COMUNE-RIL-DOC-2']/text()"/>
												</UANK04PF-COMUNE-RIL-DOC>
												<UANK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-STATO-RIL-DOC-2']/text()"/>
												</UANK04PF-STATO-RIL-DOC>
												<UANK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-ENTE-RILAS-DOC-2']/text()"/>
												</UANK04PF-ENTE-RILAS-DOC>
												<UANK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-SCA-DOC-2']/text()"/>
												</UANK04PF-DATA-SCA-DOC>
											</UANK04PF-DOCUMENTI>
											<UANK04PF-DOCUMENTI>
												<UANK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DOC-OPERAZIONE-3']/text()"/>
												</UANK04PF-DOC-OPERAZIONE>
												<UANK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-TIPO-DOC-3']/text()"/>
												</UANK04PF-TIPO-DOC>
												<UANK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-NUMERO-DOC-3']/text()"/>
												</UANK04PF-NUMERO-DOC>
												<UANK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-RIL-DOC-3']/text()"/>
												</UANK04PF-DATA-RIL-DOC>
												<UANK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROV-RIL-DOC-3']/text()"/>
												</UANK04PF-PROV-RIL-DOC>
												<UANK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-COMUNE-RIL-DOC-3']/text()"/>
												</UANK04PF-COMUNE-RIL-DOC>
												<UANK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-STATO-RIL-DOC-3']/text()"/>
												</UANK04PF-STATO-RIL-DOC>
												<UANK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-ENTE-RILAS-DOC-3']/text()"/>
												</UANK04PF-ENTE-RILAS-DOC>
												<UANK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-SCA-DOC-3']/text()"/>
												</UANK04PF-DATA-SCA-DOC>
											</UANK04PF-DOCUMENTI>
											<UANK04PF-DOCUMENTI>
												<UANK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DOC-OPERAZIONE-4']/text()"/>
												</UANK04PF-DOC-OPERAZIONE>
												<UANK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-TIPO-DOC-4']/text()"/>
												</UANK04PF-TIPO-DOC>
												<UANK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-NUMERO-DOC-4']/text()"/>
												</UANK04PF-NUMERO-DOC>
												<UANK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-RIL-DOC-4']/text()"/>
												</UANK04PF-DATA-RIL-DOC>
												<UANK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROV-RIL-DOC-4']/text()"/>
												</UANK04PF-PROV-RIL-DOC>
												<UANK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-COMUNE-RIL-DOC-4']/text()"/>
												</UANK04PF-COMUNE-RIL-DOC>
												<UANK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-STATO-RIL-DOC-4']/text()"/>
												</UANK04PF-STATO-RIL-DOC>
												<UANK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-ENTE-RILAS-DOC-4']/text()"/>
												</UANK04PF-ENTE-RILAS-DOC>
												<UANK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-SCA-DOC-4']/text()"/>
												</UANK04PF-DATA-SCA-DOC>
											</UANK04PF-DOCUMENTI>
											<UANK04PF-DOCUMENTI>
												<UANK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DOC-OPERAZIONE-5']/text()"/>
												</UANK04PF-DOC-OPERAZIONE>
												<UANK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-TIPO-DOC-5']/text()"/>
												</UANK04PF-TIPO-DOC>
												<UANK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-NUMERO-DOC-5']/text()"/>
												</UANK04PF-NUMERO-DOC>
												<UANK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-RIL-DOC-5']/text()"/>
												</UANK04PF-DATA-RIL-DOC>
												<UANK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-PROV-RIL-DOC-5']/text()"/>
												</UANK04PF-PROV-RIL-DOC>
												<UANK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-COMUNE-RIL-DOC-5']/text()"/>
												</UANK04PF-COMUNE-RIL-DOC>
												<UANK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-STATO-RIL-DOC-5']/text()"/>
												</UANK04PF-STATO-RIL-DOC>
												<UANK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-ENTE-RILAS-DOC-5']/text()"/>
												</UANK04PF-ENTE-RILAS-DOC>
												<UANK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK04PF-DATA-SCA-DOC-5']/text()"/>
												</UANK04PF-DATA-SCA-DOC>
											</UANK04PF-DOCUMENTI>
											<FILLER1/>
										</UANK04-AREACEPF>
									</UANK04-INPUT>
									<UANK04-OUTPUT>
										<UANK04OU-NDG/>
										<UANK04OU-CONTROLLO-INT/>
										<UANK04OU-CODICE-FISCALE/>
										<UANK04OU-PARTITA-IVA/>
										<UANK04OU-SPORTELLO-CAPOFILA/>
										<UANK04OU-INTESTAZIONE-A/>
										<UANK04OU-INTESTAZIONE-B/>
										<UANK04OU-TIPO-NDG/>
										<UANK04OU-SETTORISTA/>
										<UANK04OU-PSEUDONIMO/>
										<UANK04OU-STATO-RES/>
										<UANK04OU-DESCR-COM-RES/>
										<UANK04OU-CAB-RES>000000</UANK04OU-CAB-RES>
										<UANK04OU-LOCALITA-RES/>
										<UANK04OU-PROVINCIA-RES/>
										<UANK04OU-CAP-RES>00000</UANK04OU-CAP-RES>
										<UANK04OU-VIA-RES/>
										<UANK04OU-PRESSO/>
										<UANK04OU-SETTORE>000</UANK04OU-SETTORE>
										<UANK04OU-RAMO>000</UANK04OU-RAMO>
										<UANK04OU-PROFESSIONE/>
										<UANK04OU-INTESTAZ-CORR/>
										<UANK04OU-STATO-CORR/>
										<UANK04OU-DESCR-COM-CORR/>
										<UANK04OU-LOCALITA-CORR/>
										<UANK04OU-PROVINCIA-CORR/>
										<UANK04OU-CAP-CORR>000</UANK04OU-CAP-CORR>
										<UANK04OU-VIA-CORR/>
										<UANK04OU-PRESSO-CORR/>
										<UANK04OU-UFFICIO-CORR/>
										<UANK04OU-CASELLARIO-CORR/>
										<UANK04OU-STATUS-TAB>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
											<UANK04OU-STATUS/>
										</UANK04OU-STATUS-TAB>
										<UANK04OU-COMPLETEZZA-DATI/>
										<UANK04OU-STATO-NDG/>
										<UANK04OU-DATA-CENSIMENTO/>
										<UANK04OU-DATA-VA/>
										<UANK04OU-SPORTELLO-ESEC/>
										<UANK04OU-OPERATORE/>
										<UANK04OU-INTESTAZIONE-AGG/>
										<UANK04OU-COD-CR>0000000000000</UANK04OU-COD-CR>
										<UANK04OU-PREF-COGN-ACQUIS/>
										<UANK04OU-COGNOME-ACQUISITO/>
										<UANK04OU-TITOLI/>
										<UANK04OU-SESSO/>
										<UANK04OU-DATA-NASCITA/>
										<UANK04OU-COMUNE-NASCITA/>
										<UANK04OU-PROVINCIA-NASC/>
										<UANK04OU-DATA-COSTITUZI/>
										<UANK04OU-NUMERO-CCIAA>00000000</UANK04OU-NUMERO-CCIAA>
										<UANK04OU-PROV-CCIAA/>
										<UANK04OU-DATA-ISCR-CCIA/>
										<UANK04OU-NUMERO-AIA>00000000</UANK04OU-NUMERO-AIA>
										<UANK04OU-PROV-AIA/>
										<UANK04OU-CODICE-ABI>00000</UANK04OU-CODICE-ABI>
										<UANK04OU-COD-CONTROLLO>0</UANK04OU-COD-CONTROLLO>
										<UANK04OU-MINCOMES/>
										<UANK04OU-NUM-REG-TRIB>000000000</UANK04OU-NUM-REG-TRIB>
										<UANK04OU-SEDE-TRIBUNALE/>
										<UANK04OU-CODICE-SWIFT/>
										<UANK04OU-COD-OPERAT-EST/>
										<UANK04OU-STATO-SEDE-F/>
										<UANK04OU-LOCALITA-SEDE-F/>
										<UANK04OU-DESCR-COM-SEDE-F/>
										<UANK04OU-PROVINCIA-SEDE-F/>
										<UANK04OU-CAP-SEDE-F>00000</UANK04OU-CAP-SEDE-F>
										<UANK04OU-VIA-SEDE-F/>
										<UANK04OU-PRESSO-SEDE-F/>
										<UANK04OU-DATA-RIF-ADA/>
										<UANK04OU-CLASSE-DIMENSION>00</UANK04OU-CLASSE-DIMENSION>
										<UANK04OU-FATTURATO>0000000</UANK04OU-FATTURATO>
										<UANK04OU-CAPITALE-SOCIALE>0000000</UANK04OU-CAPITALE-SOCIALE>
										<UANK04OU-NUMERO-DIPENDENTI>000000</UANK04OU-NUMERO-DIPENDENTI>
										<UANK04OU-SETTORE-ALT>000</UANK04OU-SETTORE-ALT>
										<UANK04OU-RAMO-ALT>000</UANK04OU-RAMO-ALT>
										<UANK04OU-STATO-ALT/>
										<UANK04OU-COMUNE-ALT/>
										<UANK04OU-PROVINCIA-ALT/>
										<UANK04OU-CAB-ALT>000000</UANK04OU-CAB-ALT>
										<UANK04OU-CONSENSO-1/>
										<UANK04OU-DT-CONSENSO-1/>
										<UANK04OU-CONSENSO-2/>
										<UANK04OU-DT-CONSENSO-2/>
										<UANK04OU-CONSENSO-3/>
										<UANK04OU-DT-CONSENSO-3/>
										<UANK04OU-CONSENSO-4/>
										<UANK04OU-DT-CONSENSO-4/>
										<UANK04OU-CONSENSO-5/>
										<UANK04OU-DT-CONSENSO-5/>
										<UANK04OU-CONSENSO-6/>
										<UANK04OU-DT-CONSENSO-6/>
										<UANK04OU-INVIO-INFORMATIVA/>
										<UANK04OU-PROF-ATTIVITA/>
										<UANK04OU-PROF-ATTIVITA/>
										<UANK04OU-PROF-ATTIVITA/>
										<UANK04OU-PROF-ATTIVITA/>
										<UANK04OU-PROF-ATTIVITA/>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-TELEFONI>
											<UANK04OU-TEL-PROGR>00</UANK04OU-TEL-PROGR>
											<UANK04OU-TIPO-TELEFONO/>
											<UANK04OU-NUMERO-TELEFONO/>
										</UANK04OU-TELEFONI>
										<UANK04OU-DOCUMENTI>
											<UANK04OU-TIPO-DOCUM/>
											<UANK04OU-NUM-DOCUM/>
											<UANK04OU-DATA-RIL-DOCUM/>
											<UANK04OU-PROV-RIL-DOCUM/>
											<UANK04OU-COMUNE-RIL-DOCUM/>
											<UANK04OU-CAB-RIL-DOCUM>000000</UANK04OU-CAB-RIL-DOCUM>
											<UANK04OU-STATO-RIL-DOCUM/>
											<UANK04OU-ENTE-RILAS-DOCUM/>
											<UANK04OU-DATA-SCA-DOCUM/>
										</UANK04OU-DOCUMENTI>
										<UANK04OU-DOCUMENTI>
											<UANK04OU-TIPO-DOCUM/>
											<UANK04OU-NUM-DOCUM/>
											<UANK04OU-DATA-RIL-DOCUM/>
											<UANK04OU-PROV-RIL-DOCUM/>
											<UANK04OU-COMUNE-RIL-DOCUM/>
											<UANK04OU-CAB-RIL-DOCUM>000000</UANK04OU-CAB-RIL-DOCUM>
											<UANK04OU-STATO-RIL-DOCUM/>
											<UANK04OU-ENTE-RILAS-DOCUM/>
											<UANK04OU-DATA-SCA-DOCUM/>
										</UANK04OU-DOCUMENTI>
										<UANK04OU-DOCUMENTI>
											<UANK04OU-TIPO-DOCUM/>
											<UANK04OU-NUM-DOCUM/>
											<UANK04OU-DATA-RIL-DOCUM/>
											<UANK04OU-PROV-RIL-DOCUM/>
											<UANK04OU-COMUNE-RIL-DOCUM/>
											<UANK04OU-CAB-RIL-DOCUM>000000</UANK04OU-CAB-RIL-DOCUM>
											<UANK04OU-STATO-RIL-DOCUM/>
											<UANK04OU-ENTE-RILAS-DOCUM/>
											<UANK04OU-DATA-SCA-DOCUM/>
										</UANK04OU-DOCUMENTI>
										<UANK04OU-DOCUMENTI>
											<UANK04OU-TIPO-DOCUM/>
											<UANK04OU-NUM-DOCUM/>
											<UANK04OU-DATA-RIL-DOCUM/>
											<UANK04OU-PROV-RIL-DOCUM/>
											<UANK04OU-COMUNE-RIL-DOCUM/>
											<UANK04OU-CAB-RIL-DOCUM>000000</UANK04OU-CAB-RIL-DOCUM>
											<UANK04OU-STATO-RIL-DOCUM/>
											<UANK04OU-ENTE-RILAS-DOCUM/>
											<UANK04OU-DATA-SCA-DOCUM/>
										</UANK04OU-DOCUMENTI>
										<UANK04OU-DOCUMENTI>
											<UANK04OU-TIPO-DOCUM/>
											<UANK04OU-NUM-DOCUM/>
											<UANK04OU-DATA-RIL-DOCUM/>
											<UANK04OU-PROV-RIL-DOCUM/>
											<UANK04OU-COMUNE-RIL-DOCUM/>
											<UANK04OU-CAB-RIL-DOCUM>000000</UANK04OU-CAB-RIL-DOCUM>
											<UANK04OU-STATO-RIL-DOCUM/>
											<UANK04OU-ENTE-RILAS-DOCUM/>
											<UANK04OU-DATA-SCA-DOCUM/>
										</UANK04OU-DOCUMENTI>
										<UANK04OU-SEGMENTO/>
										<UANK04OU-SIT-GIURIDICA/>
										<UANK04OU-EREDITA/>
										<UANK04OU-DATA-EREDITA/>
										<UANK04OU-SEDE-CASAMADRE>000000</UANK04OU-SEDE-CASAMADRE>
										<UANK04OU-DATA-ESTINZIONE/>
										<UANK04OU-SPORTELLO-RIF/>
										<UANK04OU-RIFERIMENTO/>
										<UANK04OU-FILLER/>
									</UANK04-OUTPUT>
								</UANK04-DATI>
							</UANKA004>
						</xsl:template>
					</xsl:stylesheet>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.RomaProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass/>
		<channelclass>it.usi.webfactory.channels.RomaBusinnesChannel</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params/>
	</protocol>
	<channel>
		<params>
			<client>Client_EGU_XX</client>
			<service>RBS-XX-EGU-CENSIMENTO-ANAG-NORTF</service>
			<format>UANKA004-XML</format>
			<timeout>30000</timeout>
		</params>
	</channel>
</service>
