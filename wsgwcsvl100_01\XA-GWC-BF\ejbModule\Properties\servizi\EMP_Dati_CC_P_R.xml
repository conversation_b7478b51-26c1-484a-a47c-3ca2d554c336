<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="EM0I-COD-BANCA"/>
					<param name="EM0I-TORRE"/>
					<param name="EM0I-CONTROP-TIPO-0"/>
					<param name="EM0I-OCS-ID-0"/>
					<param name="EM0I-NDG-0"/>
					<param name="EM0I-COD-FISCALE-0"/>
					<param name="EM0I-CONTROP-TIPO-1"/>
					<param name="EM0I-OCS-ID-1"/>
					<param name="EM0I-NDG-1"/>
					<param name="EM0I-COD-FISCALE-1"/>
					<param name="EM0I-CONTROP-TIPO-2"/>
					<param name="EM0I-OCS-ID-2"/>
					<param name="EM0I-NDG-2"/>
					<param name="EM0I-COD-FISCALE-2"/>
					<param name="EM0I-CONTROP-TIPO-3"/>
					<param name="EM0I-OCS-ID-3"/>
					<param name="EM0I-NDG-3"/>
					<param name="EM0I-COD-FISCALE-3"/>
					<param name="EM0I-CONTROP-TIPO-4"/>
					<param name="EM0I-OCS-ID-4"/>
					<param name="EM0I-NDG-4"/>
					<param name="EM0I-COD-FISCALE-4"/>
					<param name="EM0I-CONTROP-TIPO-5"/>
					<param name="EM0I-OCS-ID-5"/>
					<param name="EM0I-NDG-5"/>
					<param name="EM0I-COD-FISCALE-5"/>
					<param name="EM0I-CONTROP-TIPO-6"/>
					<param name="EM0I-OCS-ID-6"/>
					<param name="EM0I-NDG-6"/>
					<param name="EM0I-COD-FISCALE-6"/>
					<param name="EM0I-CONTROP-TIPO-7"/>
					<param name="EM0I-OCS-ID-7"/>
					<param name="EM0I-NDG-7"/>
					<param name="EM0I-COD-FISCALE-7"/>
					<param name="EM0I-CONTROP-TIPO-8"/>
					<param name="EM0I-OCS-ID-8"/>
					<param name="EM0I-NDG-8"/>
					<param name="EM0I-COD-FISCALE-8"/>
					<param name="EM0I-CONTROP-TIPO-9"/>
					<param name="EM0I-OCS-ID-9"/>
					<param name="EM0I-NDG-9"/>
					<param name="EM0I-COD-FISCALE-9"/>
				</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>EMP0-DATICC-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>WEMP</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
