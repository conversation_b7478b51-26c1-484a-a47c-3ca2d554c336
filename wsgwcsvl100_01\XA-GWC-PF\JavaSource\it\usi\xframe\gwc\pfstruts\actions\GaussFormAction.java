package it.usi.xframe.gwc.pfstruts.actions;

import java.util.ArrayList;
import java.util.Iterator;

import it.usi.xframe.gwc.bfutil.base.BaseCabinBag;
import it.usi.xframe.gwc.bfutil.base.BaseDataStorage;
import it.usi.xframe.gwc.bfutil.base.BaseResponseClass;
import it.usi.xframe.gwc.bfutil.base.BaseStandardAction;
import it.usi.xframe.gwc.bfutil.params.FieldsParams;
import it.usi.xframe.gwc.bfutil.rc.FieldsResponseClass;
import it.usi.xframe.gwc.bfutil.rc.TransactionResponseClass;
import it.usi.xframe.gwc.bfutil.rc.XmlWebServicesResponseClass;
import it.usi.xframe.gwc.pfutil.GwcDelegate;
import it.usi.xframe.utl.bfutil.DataValue;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import javax.servlet.http.HttpServletRequest;

import org.apache.struts.action.ActionForm;

/**
 * @version 	1.0
 * <AUTHOR>
 */
public class GaussFormAction extends BaseStandardAction {

	private Log logger = LogFactory.getLog(this.getClass());

	protected BaseDataStorage createDataStorageObject() {
		// TODO Auto-generated method stub
		return new BaseDataStorage();
	}

	public BaseResponseClass buildResponseClass(
		BaseCabinBag cabinBag,
		BaseDataStorage dataStorage,
		ActionForm form,
		HttpServletRequest inRequest)
		throws Exception {
		
		GwcDelegate del = new GwcDelegate();
		
		TransactionResponseClass trRC = del.transactions();

		XmlWebServicesResponseClass xmls = del.xmlWebServices();
		
		ArrayList ar = new ArrayList();
		
		Iterator iter = trRC.getList().iterator();
		while (iter.hasNext())
		{
			DataValue dv = (DataValue) iter.next();
			
			Iterator iter_2 = xmls.getList().iterator();
			while (iter_2.hasNext())
			{
				DataValue dv_2 = (DataValue) iter_2.next();
				if (dv.getCode().equals(dv_2.getCode())){
					
					DataValue dv_3 = new DataValue();
					dv_3.setCode( dv.getCode() +"@"+dv_2.getDescription() );
					dv_3.setDescriptionNoUC(dv.getCode() + " - " + dv_2.getDescription());
					ar.add(dv_3);
					
//					logger.debug("[SIMODEBUG] Added --> ("+dv.getCode()+","+dv.getCode() + " - " + dv_2.getDescription()+")");
				}
			}
		}
		
		trRC.setList(ar);
		
		// Gauss Type
		String gauss_type = inRequest.getParameter("gauss_type");
		if ("".equals(gauss_type)) gauss_type="1";
		inRequest.setAttribute("gauss_type",gauss_type);

		// Service
		String service = inRequest.getParameter("service");
		inRequest.setAttribute("service",service);
		
		String transaction =  inRequest.getParameter("transaction");
		
		if (transaction!=null && !transaction.equals(""))
		{
			FieldsParams par = new FieldsParams();
			par.setTransaction(transaction);
			FieldsResponseClass fields = del.fields(par);
			inRequest.setAttribute("fields", fields.getList());
		}	
		
		
		return trRC;
	}


}