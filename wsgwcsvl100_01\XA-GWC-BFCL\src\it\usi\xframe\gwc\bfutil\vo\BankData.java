/*
 * Created on Apr 7, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.vo;

import it.usi.xframe.utl.bfutil.DataValue;

import java.io.Serializable;



public class BankData extends DataValue implements Serializable {

	private String bankCode;                     // CO_BANCA
	private String descriptionCode;              // CO_DESC 
	private String towerCode;                    // CO_TORRE


	public BankData() {
		bankCode = "";
		descriptionCode = "";
		towerCode = "";
	}

	/**
	 * @return
	 */
	public String getBankCode() {
		return bankCode;
	}

	/**
	 * @return
	 */
	public String getDescriptionCode() {
		return descriptionCode;
	}

	/**
	 * @param string
	 */
	public void setBankCode(String string) {
		bankCode = string;
	}

	/**
	 * @param string
	 */
	public void setDescriptionCode(String string) {
		descriptionCode = string;
	}



	/**
	 * @return
	 */
	public String getTowerCode() {
		return towerCode;
	}

	/**
	 * @param string
	 */
	public void setTowerCode(String string) {
		towerCode = string;
	}

}
