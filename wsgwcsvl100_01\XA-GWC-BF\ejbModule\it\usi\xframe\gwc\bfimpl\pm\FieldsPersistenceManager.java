/*
 * Created on Oct 19, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfimpl.pm;

import it.usi.xframe.gwc.bfutil.base.BaseSevereException;
import it.usi.xframe.gwc.bfutil.base.BasePersistenceManager;
import it.usi.xframe.gwc.bfutil.params.FieldsParams;
import it.usi.xframe.gwc.bfutil.rc.FieldsResponseClass;
import it.usi.xframe.gwc.bfutil.vo.Field;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class FieldsPersistenceManager extends BasePersistenceManager {

	private static FieldsPersistenceManager instance = null;

	/**	Logger for debugging */
	Log logger = LogFactory.getLog(this.getClass());

	/**
	* @return singleton
	*/
	public static FieldsPersistenceManager getInstance() {
		if (instance == null)
			instance = new FieldsPersistenceManager();

		return instance;
	}

	protected DataValue load(Map record) {
		Field res = new Field();

		res.setAggr_dati(((String) record.get("GAUR_AGGR_DATI")).trim());
		res.setNome_camp(((String) record.get("GAUR_NOME_CAMP")).trim());
		res.setProg(new BigInteger(((String) record.get("GAUR_PROG")).trim()) );
		res.setFormato(((String) record.get("GAUR_FORMATO")).trim());
		res.setLen(new BigInteger(((String) record.get("GAUR_LEN")).trim()));
		res.setLen_dec(new BigInteger(((String) record.get("GAUR_LEN_DEC")).trim()));
		
		String segno = ((String) record.get("GAUR_FLAG_SEGNO")).trim();
		if (segno.equals("S"))
			res.setFlag_segno(true);
		else
			res.setFlag_segno(false);

		return res;
	}

	//SELECT
	public FieldsResponseClass fields(FieldsParams par)	throws BaseSevereException 
	{
		FieldsResponseClass rc = new FieldsResponseClass();

		try {
			String sqlStr =
				"SELECT * FROM DB2C.CXR0GAUSSREPOSIT"+
				" WHERE GAUR_AGGR_DATI IN (SELECT SERV_COD_SERV FROM DB2C.CXR0SERVIZI WHERE SERV_NOME_TRAN= ?)"+
				" AND GAUR_TIPO_CAMP = 'I'"+
				" ORDER BY GAUR_PROG";				

			setSqlList(sqlStr);

			PersistenceManager.SqlParam[] params = new PersistenceManager.SqlParam[1];
			params[0] = sqlParamString(par.getTransaction());

			ArrayList dvl = (ArrayList) executeList(params);
			rc.setList(dvl);

			logger.info("Num. of records extracted::"+dvl.size());
			

			return rc;
			
		} catch (Exception e) {
			BaseSevereException e1 =
				new BaseSevereException(
					"An error occurred while retrieving data: " + e);
			throw e1;
		}
	}

}
