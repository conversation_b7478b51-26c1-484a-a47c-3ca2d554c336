<?xml version="1.0" encoding="UTF-8"?>
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
	<dispenser>
		<road>
			<request>
				<input>
					<param name="R38I-FUNZIONE"/>
					<param name="R38I-COD-PRATICA"/>
					<param name="R38I-DATA-RIF"/>
					<param name="R38I-COD-REASON"/>
					<param name="R38I-ADJUST-03"/>
					<param name="R38I-ACCEPT-03"/>
					<param name="R38I-NOTE"/>
					<param name="R38I-AUTHOR"/>	
					<param name="R38I-FLAG-RD"/>	
					<param name="R38I-TEXT-O1"/>
					<param name="R38I-MAN-RAT"/>
					<param name="R38I-TESTO-VALIDATED"/>
					<param name="R38I-RING-FENCED"/>
					<param name="R38I-FLAG-RD-LGD"/>
					<param name="R38I-O-LGD-LOANS"/>
					<param name="R38I-O-LGD-BONDS"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<hostService>RB38-INPUT</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>01</servBankNumber>
			<version>0100</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>RB38</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>