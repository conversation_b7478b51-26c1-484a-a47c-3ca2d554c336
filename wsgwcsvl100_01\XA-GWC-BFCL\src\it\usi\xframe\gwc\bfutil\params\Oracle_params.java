/*
 * Created on Dec 20, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.params;

import java.io.Serializable;


/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class Oracle_params implements Serializable {

	public static String TYPE = "TYPE";
	public static String VALUE = "VALUE";
	public static String LENGTH = "LENGTH";

	private String owner = "";
	private String table = "";	
	
	public Oracle_params()
	{
	}
	
	/**
	 * @return
	 */
	public String getOwner() {
		return owner;
	}

	/**
	 * @return
	 */
	public String getTable() {
		return table;
	}

	/**
	 * @param string
	 */
	public void setOwner(String string) {
		owner = string;
	}

	/**
	 * @param string
	 */
	public void setTable(String string) {
		table = string;
	}


}
