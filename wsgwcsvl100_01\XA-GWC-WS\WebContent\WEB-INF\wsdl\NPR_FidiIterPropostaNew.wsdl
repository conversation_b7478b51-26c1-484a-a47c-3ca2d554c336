<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidiIterPropostaNew">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidiIterPropostaNewResponse">
    <complexType>
     <sequence>
      <element name="nprFidiIterPropostaNewReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidiIterPropostaNewResponse">

      <wsdl:part element="impl:nprFidiIterPropostaNewResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidiIterPropostaNewRequest">

      <wsdl:part element="impl:nprFidiIterPropostaNew" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_FidiIterPropostaNew_SEI">

      <wsdl:operation name="nprFidiIterPropostaNew">

         <wsdl:input message="impl:nprFidiIterPropostaNewRequest" name="nprFidiIterPropostaNewRequest"/>

         <wsdl:output message="impl:nprFidiIterPropostaNewResponse" name="nprFidiIterPropostaNewResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_FidiIterPropostaNewSoapBinding" type="impl:NPR_FidiIterPropostaNew_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidiIterPropostaNew">

         <wsdlsoap:operation soapAction="nprFidiIterPropostaNew"/>

         <wsdl:input name="nprFidiIterPropostaNewRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidiIterPropostaNewResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_FidiIterPropostaNewService">

      <wsdl:port binding="impl:NPR_FidiIterPropostaNewSoapBinding" name="NPR_FidiIterPropostaNew">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_FidiIterPropostaNew"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
