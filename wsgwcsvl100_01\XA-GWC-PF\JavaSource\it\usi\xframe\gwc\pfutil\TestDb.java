/*
 * Created on Jun 12, 2009
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.pfutil;

import it.usi.xframe.system.bfutil.db.DBConnectionFactory;

import java.io.InputStream;
import java.io.Reader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class TestDb {

	public String testCharacter() {
		
		Connection conn=null;

		PreparedStatement stmt = null;

		String result = "prova";
		
		try {
			conn = DBConnectionFactory.getInstance().retrieveConnection();

			stmt = conn.prepareStatement("SELECT INTESTAZIONE_A FROM DB2C.EGAAAQL WHERE  NDG='0000000000042689'");

			ResultSet rs = stmt.executeQuery();

			while (rs != null && rs.next()) {
				Reader red = rs.getCharacterStream("INTESTAZIONE_A");
				char[] cBuf = new char[1000];
				red.read(cBuf);
				result = new String(cBuf);
			}
		}
		catch  (Exception e) {
		}

		finally {

			try{
	
				if(stmt!=null){stmt.close();}
		
				} 
	
			catch (SQLException e) {}
	
			try {
	
				if (conn!=null) {
					conn.close();
				}
			} 
	
			catch (SQLException e) {}		
		}
		
		return result;
	}

	public String testAscii() {
		
		Connection conn=null;

		PreparedStatement stmt = null;

		String result = "prova";
		
		try {
			conn = DBConnectionFactory.getInstance().retrieveConnection();

			stmt = conn.prepareStatement("SELECT INTESTAZIONE_A FROM DB2C.EGAAAQL WHERE  NDG='0000000000042689'");

			ResultSet rs = stmt.executeQuery();

			while (rs != null && rs.next()) {
				InputStream red = rs.getAsciiStream("INTESTAZIONE_A");
				byte[] cBuf = new byte[1000];
				red.read(cBuf);
				result = new String(cBuf);
			}
		}
		catch  (Exception e) {
		}

		finally {

			try{
	
				if(stmt!=null){stmt.close();}
		
				} 
	
			catch (SQLException e) {}
	
			try {
	
				if (conn!=null) {
					conn.close();
				}
			} 
	
			catch (SQLException e) {}
		}
		return result;
	}
}

