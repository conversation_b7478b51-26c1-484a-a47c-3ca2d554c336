/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_BigliettoPGA {
	
	//	Default Constructor - RSA8 migration prerequisites	
	public NPR_BigliettoPGA(){
			
	}		

	public String nprBigliettoPGA(String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x08, String x09, String x11, String x12, String x97) throws Exception {

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprBigliettoPGA(x01, x02, x03, x04, x05, x06, x07, x08, x09, x11, x12, x97);
	}     
}
