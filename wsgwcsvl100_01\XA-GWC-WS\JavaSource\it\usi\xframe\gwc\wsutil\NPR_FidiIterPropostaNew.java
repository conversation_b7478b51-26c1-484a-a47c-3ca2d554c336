/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_FidiIterPropostaNew {
	
	//	Default Constructor - RSA8 migration prerequisites	
	public NPR_FidiIterPropostaNew(){
			
	}	

	public String nprFidiIterPropostaNew(String x00, String x01, String x02, String x03) throws Exception { 

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprFidiIterPropostaNew(x00, x01, x02, x03);
	}     
}
