<?xml version="1.0" encoding="UTF-8"?>
<com.ibm.etools.webservice.wsext:WsExtension xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:com.ibm.etools.webservice.wsext="http://www.ibm.com/websphere/appserver/schemas/5.0.2/wsext.xmi" xmi:id="WsExtension_1411998472868">
  <wsDescExt xmi:id="WsDescExt_1411998472868" wsDescNameLink="CallServiceService">
    <pcBinding xmi:id="PcBinding_1411998472868" pcNameLink="CallService">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412001030626">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412001030626">
          <loginConfig xmi:id="LoginConfig_1412001030626">
            <authMethods xmi:id="AuthMethod_1412001030626" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412001030626" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412001030627" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039019" wsDescNameLink="CallServiceFreeService">
    <pcBinding xmi:id="PcBinding_1412000039019" pcNameLink="CallServiceFree">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412001030627">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412001030627">
          <loginConfig xmi:id="LoginConfig_1412001030627">
            <authMethods xmi:id="AuthMethod_1412001030627" text="LTPA"/>
          </loginConfig>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039020" wsDescNameLink="CallServiceTokenService">
    <pcBinding xmi:id="PcBinding_1412000039020" pcNameLink="CallServiceToken">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412061729876">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412061729876">
          <loginConfig xmi:id="LoginConfig_1412061729876">
            <authMethods xmi:id="AuthMethod_1412061729876" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412061729876" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412061729877" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039021" wsDescNameLink="NPR_AnagraficaService">
    <pcBinding xmi:id="PcBinding_1412000039021" pcNameLink="NPR_Anagrafica">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412077575640">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412077575640">
          <loginConfig xmi:id="LoginConfig_1412077575640">
            <authMethods xmi:id="AuthMethod_1412077575640" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412077575640" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412077575641" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039022" wsDescNameLink="NPR_AnagraficaAce6Service">
    <pcBinding xmi:id="PcBinding_1412000039022" pcNameLink="NPR_AnagraficaAce6">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412077575641">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412077575641">
          <loginConfig xmi:id="LoginConfig_1412077575641">
            <authMethods xmi:id="AuthMethod_1412077575641" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412077575642" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412077575643" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039023" wsDescNameLink="NPR_BigliettoPGAService">
    <pcBinding xmi:id="PcBinding_1412000039023" pcNameLink="NPR_BigliettoPGA">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412077575642">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412077575642">
          <loginConfig xmi:id="LoginConfig_1412077575642">
            <authMethods xmi:id="AuthMethod_1412077575642" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412077575644" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412077575645" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039024" wsDescNameLink="NPR_DatiFidiPraticaService">
    <pcBinding xmi:id="PcBinding_1412000039024" pcNameLink="NPR_DatiFidiPratica">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412077575643">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412077575643">
          <loginConfig xmi:id="LoginConfig_1412077575643">
            <authMethods xmi:id="AuthMethod_1412077575643" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412077575646" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412077575647" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039026" wsDescNameLink="NPR_EsitoPGAService">
    <pcBinding xmi:id="PcBinding_1412000039026" pcNameLink="NPR_EsitoPGA">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575865">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575865">
          <loginConfig xmi:id="LoginConfig_1412078575865">
            <authMethods xmi:id="AuthMethod_1412078575865" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575865" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575866" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039027" wsDescNameLink="NPR_Fidi_AggiornaRigaService">
    <pcBinding xmi:id="PcBinding_1412000039027" pcNameLink="NPR_Fidi_AggiornaRiga">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575866">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575866">
          <loginConfig xmi:id="LoginConfig_1412078575866">
            <authMethods xmi:id="AuthMethod_1412078575866" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575867" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575868" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039028" wsDescNameLink="NPR_Fidi_AnnullamentoPropostaService">
    <pcBinding xmi:id="PcBinding_1412000039028" pcNameLink="NPR_Fidi_AnnullamentoProposta">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575867">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575867">
          <loginConfig xmi:id="LoginConfig_1412078575867">
            <authMethods xmi:id="AuthMethod_1412078575867" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575869" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575870" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039029" wsDescNameLink="NPR_Fidi_Autorizzazione_NegativaService">
    <pcBinding xmi:id="PcBinding_1412000039029" pcNameLink="NPR_Fidi_Autorizzazione_Negativa">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575868">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575868">
          <loginConfig xmi:id="LoginConfig_1412078575868">
            <authMethods xmi:id="AuthMethod_1412078575868" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575871" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575872" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039030" wsDescNameLink="NPR_Fidi_AutorizzazioneService">
    <pcBinding xmi:id="PcBinding_1412000039030" pcNameLink="NPR_Fidi_Autorizzazione">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575869">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575869">
          <loginConfig xmi:id="LoginConfig_1412078575869">
            <authMethods xmi:id="AuthMethod_1412078575869" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575873" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575874" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039031" wsDescNameLink="NPR_Fidi_AvocaturaService">
    <pcBinding xmi:id="PcBinding_1412000039031" pcNameLink="NPR_Fidi_Avocatura">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575870">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575870">
          <loginConfig xmi:id="LoginConfig_1412078575870">
            <authMethods xmi:id="AuthMethod_1412078575870" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575875" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575876" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039032" wsDescNameLink="NPR_Fidi_BloccoService">
    <pcBinding xmi:id="PcBinding_1412000039032" pcNameLink="NPR_Fidi_Blocco">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575871">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575871">
          <loginConfig xmi:id="LoginConfig_1412078575871">
            <authMethods xmi:id="AuthMethod_1412078575871" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575877" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575878" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039033" wsDescNameLink="NPR_Fidi_CancellaService">
    <pcBinding xmi:id="PcBinding_1412000039033" pcNameLink="NPR_Fidi_Cancella">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575872">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575872">
          <loginConfig xmi:id="LoginConfig_1412078575872">
            <authMethods xmi:id="AuthMethod_1412078575872" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575879" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575880" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039034" wsDescNameLink="NPR_Fidi_CompletamentoService">
    <pcBinding xmi:id="PcBinding_1412000039034" pcNameLink="NPR_Fidi_Completamento">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575873">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575873">
          <loginConfig xmi:id="LoginConfig_1412078575873">
            <authMethods xmi:id="AuthMethod_1412078575873" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575881" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575882" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039035" wsDescNameLink="NPR_Fidi_DeclinoService">
    <pcBinding xmi:id="PcBinding_1412000039035" pcNameLink="NPR_Fidi_Declino">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575874">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575874">
          <loginConfig xmi:id="LoginConfig_1412078575874">
            <authMethods xmi:id="AuthMethod_1412078575874" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575883" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575884" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039036" wsDescNameLink="NPR_Fidi_DeliberaService">
    <pcBinding xmi:id="PcBinding_1412000039036" pcNameLink="NPR_Fidi_Delibera">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575875">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575875">
          <loginConfig xmi:id="LoginConfig_1412078575875">
            <authMethods xmi:id="AuthMethod_1412078575875" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575885" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575886" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039037" wsDescNameLink="NPR_Fidi_Inquiry_DatoVarioService">
    <pcBinding xmi:id="PcBinding_1412000039037" pcNameLink="NPR_Fidi_Inquiry_DatoVario">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575876">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575876">
          <loginConfig xmi:id="LoginConfig_1412078575876">
            <authMethods xmi:id="AuthMethod_1412078575876" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575887" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575888" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039038" wsDescNameLink="NPR_Fidi_IterPropostaService">
    <pcBinding xmi:id="PcBinding_1412000039038" pcNameLink="NPR_Fidi_IterProposta">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575877">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575877">
          <loginConfig xmi:id="LoginConfig_1412078575877">
            <authMethods xmi:id="AuthMethod_1412078575877" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575889" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575890" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039039" wsDescNameLink="NPR_Fidi_LogonService">
    <pcBinding xmi:id="PcBinding_1412000039039" pcNameLink="NPR_Fidi_Logon">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575878">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575878">
          <loginConfig xmi:id="LoginConfig_1412078575878">
            <authMethods xmi:id="AuthMethod_1412078575878" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575891" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575892" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039040" wsDescNameLink="NPR_Fidi_PenninoService">
    <pcBinding xmi:id="PcBinding_1412000039040" pcNameLink="NPR_Fidi_Pennino">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575879">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575879">
          <loginConfig xmi:id="LoginConfig_1412078575879">
            <authMethods xmi:id="AuthMethod_1412078575879" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575893" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575894" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039041" wsDescNameLink="NPR_Fidi_PerfezionamentoGaranziaService">
    <pcBinding xmi:id="PcBinding_1412000039041" pcNameLink="NPR_Fidi_PerfezionamentoGaranzia">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575880">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575880">
          <loginConfig xmi:id="LoginConfig_1412078575880">
            <authMethods xmi:id="AuthMethod_1412078575880" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575895" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575896" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039042" wsDescNameLink="NPR_Fidi_PreCompletamentoService">
    <pcBinding xmi:id="PcBinding_1412000039042" pcNameLink="NPR_Fidi_PreCompletamento">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575881">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575881">
          <loginConfig xmi:id="LoginConfig_1412078575881">
            <authMethods xmi:id="AuthMethod_1412078575881" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575897" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575898" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039043" wsDescNameLink="NPR_Fidi_PropostaService">
    <pcBinding xmi:id="PcBinding_1412000039043" pcNameLink="NPR_Fidi_Proposta">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575882">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575882">
          <loginConfig xmi:id="LoginConfig_1412078575882">
            <authMethods xmi:id="AuthMethod_1412078575882" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575899" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575900" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039044" wsDescNameLink="NPR_Fidi_RecuperoNdgService">
    <pcBinding xmi:id="PcBinding_1412000039044" pcNameLink="NPR_Fidi_RecuperoNdg">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575883">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575883">
          <loginConfig xmi:id="LoginConfig_1412078575883">
            <authMethods xmi:id="AuthMethod_1412078575883" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575901" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575902" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039045" wsDescNameLink="NPR_FidiIterPropostaNewService">
    <pcBinding xmi:id="PcBinding_1412000039045" pcNameLink="NPR_FidiIterPropostaNew">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575884">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575884">
          <loginConfig xmi:id="LoginConfig_1412078575884">
            <authMethods xmi:id="AuthMethod_1412078575884" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575903" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575904" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039046" wsDescNameLink="NPR_Fin_DettaglioService">
    <pcBinding xmi:id="PcBinding_1412000039046" pcNameLink="NPR_Fin_Dettaglio">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575885">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575885">
          <loginConfig xmi:id="LoginConfig_1412078575885">
            <authMethods xmi:id="AuthMethod_1412078575885" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575905" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575906" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039047" wsDescNameLink="NPR_Fin_TassoService">
    <pcBinding xmi:id="PcBinding_1412000039047" pcNameLink="NPR_Fin_Tasso">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575886">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575886">
          <loginConfig xmi:id="LoginConfig_1412078575886">
            <authMethods xmi:id="AuthMethod_1412078575886" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575907" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575908" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039048" wsDescNameLink="NPR_ListaFidiService">
    <pcBinding xmi:id="PcBinding_1412000039048" pcNameLink="NPR_ListaFidi">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575887">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575887">
          <loginConfig xmi:id="LoginConfig_1412078575887">
            <authMethods xmi:id="AuthMethod_1412078575887" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575909" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575910" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039049" wsDescNameLink="NPR_LoginService">
    <pcBinding xmi:id="PcBinding_1412000039049" pcNameLink="NPR_Login">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575888">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575888">
          <loginConfig xmi:id="LoginConfig_1412078575888">
            <authMethods xmi:id="AuthMethod_1412078575888" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575911" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575912" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039050" wsDescNameLink="NPR_PfaService">
    <pcBinding xmi:id="PcBinding_1412000039050" pcNameLink="NPR_Pfa">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575889">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575889">
          <loginConfig xmi:id="LoginConfig_1412078575889">
            <authMethods xmi:id="AuthMethod_1412078575889" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575913" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575914" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039051" wsDescNameLink="NPR_RatingService">
    <pcBinding xmi:id="PcBinding_1412000039051" pcNameLink="NPR_Rating">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575890">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575890">
          <loginConfig xmi:id="LoginConfig_1412078575890">
            <authMethods xmi:id="AuthMethod_1412078575890" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575915" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575916" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039052" wsDescNameLink="NPR_SEM_ZivnoService">
    <pcBinding xmi:id="PcBinding_1412000039052" pcNameLink="NPR_SEM_Zivno">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575891">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575891">
          <loginConfig xmi:id="LoginConfig_1412078575891">
            <authMethods xmi:id="AuthMethod_1412078575891" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575917" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575918" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039053" wsDescNameLink="NPR_SinteticaPGAService">
    <pcBinding xmi:id="PcBinding_1412000039053" pcNameLink="NPR_SinteticaPGA">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575892">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575892">
          <loginConfig xmi:id="LoginConfig_1412078575892">
            <authMethods xmi:id="AuthMethod_1412078575892" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575919" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575920" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039054" wsDescNameLink="NPR_SinteticaPGASempliceService">
    <pcBinding xmi:id="PcBinding_1412000039054" pcNameLink="NPR_SinteticaPGASemplice">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575893">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575893">
          <loginConfig xmi:id="LoginConfig_1412078575893">
            <authMethods xmi:id="AuthMethod_1412078575893" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575921" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575922" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039056" wsDescNameLink="NPX_Fidi_CompletamentoService">
    <pcBinding xmi:id="PcBinding_1412000039056" pcNameLink="NPX_Fidi_Completamento">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575894">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575894">
          <loginConfig xmi:id="LoginConfig_1412078575894">
            <authMethods xmi:id="AuthMethod_1412078575894" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575923" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575924" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039057" wsDescNameLink="NPX_Fidi_InserimentoFidiService">
    <pcBinding xmi:id="PcBinding_1412000039057" pcNameLink="NPX_Fidi_InserimentoFidi">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575895">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575895">
          <loginConfig xmi:id="LoginConfig_1412078575895">
            <authMethods xmi:id="AuthMethod_1412078575895" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575925" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575926" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039058" wsDescNameLink="NPX_Fidi_LogonService">
    <pcBinding xmi:id="PcBinding_1412000039058" pcNameLink="NPX_Fidi_Logon">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575896">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575896">
          <loginConfig xmi:id="LoginConfig_1412078575896">
            <authMethods xmi:id="AuthMethod_1412078575896" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575927" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575928" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412000039059" wsDescNameLink="NPX_Fidi_PreCompletamentoService">
    <pcBinding xmi:id="PcBinding_1412000039059" pcNameLink="NPX_Fidi_PreCompletamento">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575897">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575897">
          <loginConfig xmi:id="LoginConfig_1412078575897">
            <authMethods xmi:id="AuthMethod_1412078575897" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575929" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575930" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412077372605" wsDescNameLink="NPX_Fidi_AperturaPropostaService">
    <pcBinding xmi:id="PcBinding_1412077372605" pcNameLink="NPX_Fidi_AperturaProposta">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575898">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575898">
          <loginConfig xmi:id="LoginConfig_1412078575898">
            <authMethods xmi:id="AuthMethod_1412078575898" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575931" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575932" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1412077726330" wsDescNameLink="NPR_DossierService">
    <pcBinding xmi:id="PcBinding_1412077726330" pcNameLink="NPR_Dossier">
      <serverServiceConfig xmi:id="ServerServiceConfig_1412078575899">
        <securityRequestReceiverServiceConfig xmi:id="SecurityRequestReceiverServiceConfig_1412078575899">
          <loginConfig xmi:id="LoginConfig_1412078575899">
            <authMethods xmi:id="AuthMethod_1412078575899" text="BasicAuth"/>
          </loginConfig>
          <properties xmi:id="Property_1412078575933" name="com.ibm.ws.wssecurity.config.token.BasicAuth.NonceRequired" value="false"/>
          <properties xmi:id="Property_1412078575934" name="com.ibm.ws.wssecurity.config.token.BasicAuth.Nonce.timestampRequired" value="false"/>
        </securityRequestReceiverServiceConfig>
      </serverServiceConfig>
    </pcBinding>
  </wsDescExt>
  <wsDescExt xmi:id="WsDescExt_1417768686930" wsDescNameLink="Rating_CreditBureauService">
    <pcBinding xmi:id="PcBinding_1417768686930" pcNameLink="Rating_CreditBureau"/>
  </wsDescExt>
</com.ibm.etools.webservice.wsext:WsExtension>
