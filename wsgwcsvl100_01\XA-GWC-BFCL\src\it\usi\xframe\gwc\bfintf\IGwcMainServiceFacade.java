/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfintf;

import java.util.Map;

import it.usi.xframe.gwc.bfutil.params.FieldsParams;
import it.usi.xframe.gwc.bfutil.rc.FieldsResponseClass;
import it.usi.xframe.gwc.bfutil.rc.TransactionResponseClass;
import it.usi.xframe.gwc.bfutil.rc.XmlWebServicesResponseClass;
import it.usi.xframe.gwc.bfutil.vo.BanksList;
import it.usi.xframe.system.eservice.IServiceFacade;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments 
 */
public interface IGwcMainServiceFacade extends IServiceFacade {

	public static String KEY_ID= "GwcMain"; 

	public String getFieldFromResponse(String xmlResponse, String tagFrom, String tag, int record) throws Exception;
	public String callService(String service, String params, String sep1, String sep2) throws Exception; 

	public String nprAnagrafica(String x01, String x02, String x03, String x04,  String x97, String x98) throws Exception; 
	public String nprAnagraficaAce6(String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x08, String x97, String x98) throws Exception;
	public String nprDatiFidiPratica(String x00, String x01, String x02, String x03, String x04) throws Exception; 
//	public String nprDossier(String x01, String n001, String x02, String x03, String x04, String x95, String x96) throws Exception; 
	public String nprDossier(String x01, String n001, String x02, String n002, String x03, String n003, String x04, String x05, String x95, String x96) throws Exception; 
	public String nprFidi_AggiornaRiga(String x00, String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x08) throws Exception; 
	public String nprFidi_AnnullamentoProposta(String x00, String x01, String x02, String x03, String x04) throws Exception; 
	public String nprFidi_Autorizzazione(String x00, String x01, String x02, String x03, String x15) throws Exception; 
	public String nprFidi_Autorizzazione_Negativa(String x00, String x01, String x02, String x03, String x04, String x15) throws Exception; 
	public String nprFidi_Avocatura(String x00, String x01, String x02, String x03) throws Exception; 
	public String nprFidi_Blocco(String x00, String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x08) throws Exception; 
	public String nprFidi_Cancella(String x00, String x01, String x02, String x03, String x04) throws Exception; 
	public String nprFidi_Completamento(String x00, String x01, String x02, String x03) throws Exception; 
	public String npxFidi_Completamento(String x00, String x01, String x02, String x03) throws Exception; 
	public String nprFidi_Declino(String x00, String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x08, String x09, String x10, String x11) throws Exception; 
	public String nprFidi_Delibera(String x00, String x01, String x02, String x03, String x04, String x05, String x06) throws Exception; 
	public String nprFidi_IterProposta(String x00, String x01, String x02, String x03) throws Exception; 
	public String nprFidi_Logon(String x00, String x01, String x02) throws Exception; 
	public String npxFidi_Logon(String x00, String x01, String x02) throws Exception; 
	public String nprFidi_Pennino(String x00, String x01, String x02, String x03) throws Exception; 
	public String nprFidi_PreCompletamento(String x00, String x01, String x02, String x05) throws Exception; 
	public String npxFidi_PreCompletamento(String x00, String x01, String x02, String x05) throws Exception; 

//	public String nprFidi_Proposta(String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x09, String x97, String x98) throws Exception;
	public String nprFidi_Proposta(String x01, String x02, String x03, String x04, String x97, String x98) throws Exception;
	 
	public String nprFidi_RecuperoNdg(String x00, String x02, String x03) throws Exception; 
	public String nprFidiIterPropostaNew(String x00, String x01, String x02, String x03) throws Exception; 
	
	public String nprSinteticaPGASemplice(String x01, String x02, String x03, String x04, String x05, String x07, String x97, String x98) throws Exception; 
	public String nprSinteticaPGA(String x01, String x02, String x03, String x98) throws Exception; 
	public String nprEsitoPGA(String x03) throws Exception; 
	public String nprBigliettoPGA(String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x08, String x09, String x11, String x12, String x97) throws Exception; 

	public String nprFidi_PerfezionamentoGaranzia(String x00, String x01, String x02, String x03, String x04, String x05, String x06, String x07, String x08, String x09, String x10, String x11, String x12, String x13, String x14, String x15, String x16, String x17, String x18, String x19) throws Exception; 
	public String nprFin_Dettaglio(String x01, String x02, String x03, String x04, String x05, String x97, String x98) throws Exception; 
	public String nprFin_Tasso(String x01, String x02, String x03, String x04, String x05) throws Exception; 
	public String nprListaFidi(String x00, String x01, String x02, String x03) throws Exception;
	public String nprLogin(String x01, String x02, String x03, String x04, String x05) throws Exception;
	public String nprPfa(String x01, String n001, String x02, String n002, String x03, String x04, String x95, String x96) throws Exception; 
	public String nprRating(String n001, String x97, String n003, String x01, String x02, String x03, String x04, String n004, String n005, String x05, String x06) throws Exception;
	public String nprSemZivno(String n001, String n002, String x01, String n003, String x02, String n004, String x03, String n005, String x04, String n006, String x05, String n007, String x06, String n008, 
							String x07, String n009, String x08, String n010, String x09, String n011, String x10, String n012, String x11, String n013, String x12, String n014, String x13, String n015,
							String n016, String n017, String x14, String x15, String x16, String x17, String n018, String n019, String n020, String n021, String n022, String x18, String x19, String n023,
							String x20, String x21, String n024, String x22, String x23, String n025, String x24, String n026, String n027, String n028, String x25, String x26, String x27, String x28, String x29,
							String x30, String n029, String x31, String n030) throws Exception;
	public String nprFidi_Inquiry_DatoVario(String x00, String x01, String x02, String x03, String x04) throws Exception;
	public String npxFidi_AperturaProposta(String x00, String x01, String x02, String x03, String x05, String x06, String x08) throws Exception; 
	public String npxFidi_InserimentoFidi(String x00, String x01, String x02, String x03, String x06, String x08, String x09, String x10, String x11 ) throws Exception; 
	
	 
	//************************ Table DB2C.TGTB0135 ***************************
	public BanksList getBanks() throws Exception;
	
	public boolean UCF_db2_oracle_copy() throws Exception;			
	
	// G A U S S   T E S T I N G
	public TransactionResponseClass transactions() throws Exception;
	public FieldsResponseClass fields(FieldsParams par1) throws Exception;
	public XmlWebServicesResponseClass xmlWebServices() throws Exception;
	public String call_gauss(String serviceName, Map params) throws Exception;
}