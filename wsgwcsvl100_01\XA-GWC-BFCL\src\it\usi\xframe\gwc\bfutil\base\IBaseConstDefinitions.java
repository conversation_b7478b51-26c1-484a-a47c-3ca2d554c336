/*
 * Created on May 23, 2005
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.base;

import java.math.BigDecimal;

public interface IBaseConstDefinitions {
	
	public static final String CACHE_ASCII_ALLOWED_TABLE	= "CACHE_ASCII_ALLOWED_TABLE_";
	public static final String SESSION_DATA_OBJECT_NAME = "sessionData";
	public static final String RESPONSE_CLASS_OBJECT_NAME = "responseClass";
	
	public static final String DATE_EMPTY = "0001-01-01";
	public static final String TIMESTAMP_EMPTY = "0001-01-01-00.00.00.000000";
	public static final String HOST_TIMESTAMP_FORMAT = "yyyy-MM-dd-HH.mm.ss.SSS";
	public static final String FROM_HOST_TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
	public static final String HOST_DATE_FORMAT = "yyyy-MM-dd";
	public static final BigDecimal BIGDECIMALNULLVALUE9_9	= new BigDecimal("-999999999.999999999");
	public static final BigDecimal BIGDECIMALNULLVALUE6_9	= new BigDecimal("-999999.999999999");
	
	public static final String YES = "Y";
	public static final String NO = "N";
	
	public static final String WS_RETURN_CODE_OK = "";
	public static final String WS_RETURN_CODE_KO= "E";
	public static final String WS_RETURN_CODE_INFO = "G";
	public static final String WS_RETURN_CODE_RECALCULATE = "R";
	public static final String WS_RETURN_CODE_UPDATE = "U";
	public static final String WS_RETURN_CODE_DELETE = "D";
	public static final String WS_RETURN_CODE_BACKTO = "S";
	public static final String WS_RETURN_CODE_BLOCK_WARNING = "W";
	
	public static final String SELECT_CODE_NONE = "00";
	public static final String SELECT_CODE_MISSING = "98";
	public static final String SELECT_CODE_OTHER = "99";
	
	public static final String LOCALFORWARD_OK    = "ok";
}