<?xml version='1.0'?>

<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="internet">
<route appOwner="GWC" serviceName="EMPListaAggregatiFIN" active="true"/>
    <dispenser>

        <road>

            <request>

                <input>

                    <param name="PROGR-CHIAM"/>

                    <param name="INP-BANCA"/>

                    <param name="INP-CONV-TIPO"/>

                    <param name="INP-CONV-COD"/>

                    <param name="INP-PRODOTTO"/>

                    <param name="INP-ATTRIB-01"/>

                    <param name="INP-ATTRIB-02"/>

                    <param name="INP-ATTRIB-03"/>

                    <param name="INP-ATTRIB-04"/>

                    <param name="INP-ATTRIB-05"/>

                    <param name="INP-ATTRIB-06"/>

                    <param name="INP-ATTRIB-07"/>

                    <param name="INP-ATTRIB-08"/>

                    <param name="INP-ATTRIB-09"/>

                    <param name="INP-ATTRIB-10"/>

                    <param name="INP-ATTRIB-11"/>

                    <param name="INP-ATTRIB-12"/>

                    <param name="INP-ATTRIB-13"/>

                    <param name="INP-ATTRIB-14"/>

                    <param name="INP-ATTRIB-15"/>

                    <param name="INP-VALORE-DA-01"/>

                    <param name="INP-VALORE-DA-02"/>

                    <param name="INP-VALORE-DA-03"/>

                    <param name="INP-VALORE-DA-04"/>

                    <param name="INP-VALORE-DA-05"/>

                    <param name="INP-VALORE-DA-06"/>

                    <param name="INP-VALORE-DA-07"/>

                    <param name="INP-VALORE-DA-08"/>

                    <param name="INP-VALORE-DA-09"/>

                    <param name="INP-VALORE-DA-10"/>

                    <param name="INP-VALORE-DA-11"/>

                    <param name="INP-VALORE-DA-12"/>

                    <param name="INP-VALORE-DA-13"/>

                    <param name="INP-VALORE-DA-14"/>

                    <param name="INP-VALORE-DA-15"/>

                    <param name="INP-VALORE-A-01"/>

                    <param name="INP-VALORE-A-02"/>

                    <param name="INP-VALORE-A-03"/>

                    <param name="INP-VALORE-A-04"/>

                    <param name="INP-VALORE-A-05"/>

                    <param name="INP-VALORE-A-06"/>

                    <param name="INP-VALORE-A-07"/>

                    <param name="INP-VALORE-A-08"/>

                    <param name="INP-VALORE-A-09"/>

                    <param name="INP-VALORE-A-10"/>

                    <param name="INP-VALORE-A-11"/>

                    <param name="INP-VALORE-A-12"/>

                    <param name="INP-VALORE-A-13"/>

                    <param name="INP-VALORE-A-14"/>

                    <param name="INP-VALORE-A-15"/>

                </input>

                <output>

<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request" xmlns:myxsl="http://www.w3.org/1999/XSL/Transform" 

version="1.0">

                        <xsl:output method="xml"/>

                        <xsl:template match="/">

                            <XP0C031A>

                                <ROCH-03-HEADER>

                                    <ROCH-03-STRUCID>ROCH</ROCH-03-STRUCID>

                                    <ROCH-03-VERSION>0003</ROCH-03-VERSION>

                                    <ROCH-03-BSNAME>RBS-XX-XP0-EMP-LISTA-AGGREGATI</ROCH-03-BSNAME>

                                    <ROCH-03-RETURNCODE>0000</ROCH-03-RETURNCODE>

                                    <ROCH-03-UOWCONTROL>0000</ROCH-03-UOWCONTROL>

                                    <ROCH-03-ABEND-CODE/>

                                    <ROCH-03-MSG-LENGHT/>

                                    <ROCH-03-MSG-FORMAT/>

                                    <ROCH-03-REQID/>

                                    <ROCH-03-ERROR-MSG/>

                                    <ROCH-03-TOKEN/>

                                </ROCH-03-HEADER>

                                <XP0R31A-INPUT>

                                    <XP0R31A-PROGR-CHIAM>

                                        <xsl:value-of select="xgen:request/xgen:param[@name='PROGR-CHIAM']/text()"/>

                                    </XP0R31A-PROGR-CHIAM>

                                    <XP0R31A-INP-BANCA>

                                        <xsl:value-of select="xgen:request/xgen:param[@name='INP-BANCA']/text()"/>

                                    </XP0R31A-INP-BANCA>

                                    <XP0R31A-INP-CONV>

                                        <XP0R31A-INP-CONV-TIPO>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-CONV-TIPO']/text()"/>

                                        </XP0R31A-INP-CONV-TIPO>

                                        <XP0R31A-INP-CONV-COD>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-CONV-COD']/text()"/>

                                        </XP0R31A-INP-CONV-COD>

                                    </XP0R31A-INP-CONV>

                                    <XP0R31A-INP-PRODOTTO>

                                        <xsl:value-of select="xgen:request/xgen:param[@name='INP-PRODOTTO']/text()"/>

                                    </XP0R31A-INP-PRODOTTO>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-01']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-01']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-01']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-02']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-02']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-02']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-03']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-03']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-03']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-04']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-04']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-04']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-05']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-05']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-05']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-06']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-06']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-06']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-07']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-07']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-07']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-08']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-08']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-08']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-09']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-09']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-09']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-10']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-10']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-10']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-11']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-11']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-11']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-12']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-12']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-12']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-13']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-13']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-13']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-14']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-14']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-14']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                    <XP0R31A-INP-FILTRI>

                                        <XP0R31A-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-15']/text()"/>

                                        </XP0R31A-INP-ATTRIB>

                                        <XP0R31A-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-15']/text()"/>

                                        </XP0R31A-INP-VALORE-DA>

                                        <XP0R31A-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-15']/text()"/>

                                        </XP0R31A-INP-VALORE-A>

                                    </XP0R31A-INP-FILTRI>

                                </XP0R31A-INPUT>

                                <XP0R31A-OUTPUT>

                                    <XP0R31A-RC>1</XP0R31A-RC>

                                    <XP0R31A-MESS>_XP0R31A-MESS_</XP0R31A-MESS>

                                    <XP0R31A-OUT-NUM-ELE>1</XP0R31A-OUT-NUM-ELE>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>
									
									</XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                    <XP0R31A-OUT-AGGR>

                                        <XP0R31A-OUT-AGGREGATO>_SZ0</XP0R31A-OUT-AGGREGATO>

                                        <XP0R31A-OUT-AGGR-DESC>_XP0R31A-OUT-AGGR-DESC_</XP0R31A-OUT-AGGR-DESC>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                        <XP0R31A-OUT-ATTR>

                                            <XP0R31A-OUT-ATTR-VAL>_XP0R31A-OUT-ATTR-VAL_</XP0R31A-OUT-ATTR-VAL>

                                        </XP0R31A-OUT-ATTR>

                                    </XP0R31A-OUT-AGGR>

                                </XP0R31A-OUTPUT>

                            </XP0C031A>

                        </xsl:template>

                    </xsl:stylesheet>

                </output>

            </request>

            <response>

                <input/>

                <output/>

            </response>

        </road>

        <providerclass>it.usi.webfactory.provider.RomaProvider</providerclass>

    </dispenser>

    <provider>

        <protocolclass/>

        <channelclass>it.usi.webfactory.channels.RomaStandardChannel</channelclass>

    </provider>

    <protocol>

        <bridge>

            <request>

                <input/>

                <output/>

            </request>

            <response>

                <input/>

                <output/>

            </response>

        </bridge>

        <params/>

    </protocol>

    <channel>

        <params>

            <client>Client_EMP_XX</client>

            <service>RBS-XX-XP0-EMP-LISTA-AGGREGATI</service>

            <format>XP0C031A-XML</format>

            <timeout>180000</timeout>

        </params>

    </channel>

</service>