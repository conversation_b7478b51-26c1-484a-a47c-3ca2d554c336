/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_SinteticaPGASemplice {
	
	//	Default Constructor - RSA8 migration prerequisites	
	public NPR_SinteticaPGASemplice(){
			
	}	

	public String nprSinteticaPGASemplice(String x01, String x02, String x03, String x04, String x05, String x07, String x97, String x98) throws Exception { 

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprSinteticaPGASemplice(x01, x02, x03, x04, x05, x07, x97, x98);
	}     
}
