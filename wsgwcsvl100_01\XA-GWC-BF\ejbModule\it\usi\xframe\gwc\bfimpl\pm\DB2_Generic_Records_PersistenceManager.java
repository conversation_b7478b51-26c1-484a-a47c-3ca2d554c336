/*
 * Created on June 4, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfimpl.pm;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import it.usi.xframe.gwc.bfutil.vo.Generic_Record;
import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */




public class DB2_Generic_Records_PersistenceManager extends PersistenceManager {
	
	private static DB2_Generic_Records_PersistenceManager instance = null;
	
	/**	Logger for debugging */
	private Log logger = LogFactory.getLog(this.getClass());
			
	private DB2_Generic_Records_PersistenceManager() 
	{}			
	
	/**
	* @return singleton
	*/
	public static synchronized DB2_Generic_Records_PersistenceManager getInstance() {

		if (instance == null)
			instance = new DB2_Generic_Records_PersistenceManager();
			
		return instance;
	}
	
	protected DataValue load(Map record) {
		
		Generic_Record rec = new Generic_Record();
		rec.setFields(record);
		return rec;
	}

	public Collection getRecords(String owner,String table) throws XFRException 
	{
		String query = "SELECT * FROM "+owner+"."+table;
						
		PersistenceManager.SqlParam[] params = new PersistenceManager.SqlParam[0];						
//		params[0] = sqlParamString(owner);
//		params[1] = sqlParamString(table);
										
		setSqlList(query);
		
		List dvl = (List) executeList(params);

		logger.info(query);		
//		logger.info("Owner = #"+owner+"#");
//		logger.info("Name = #"+ table+"#");
		
		return dvl;	
	}	


}