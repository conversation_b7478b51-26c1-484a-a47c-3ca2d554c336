<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Declino">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
      <element name="x06" nillable="true" type="xsd:string"/>
      <element name="x07" nillable="true" type="xsd:string"/>
      <element name="x08" nillable="true" type="xsd:string"/>
      <element name="x09" nillable="true" type="xsd:string"/>
      <element name="x10" nillable="true" type="xsd:string"/>
      <element name="x11" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_DeclinoResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_DeclinoReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_DeclinoRequest">

      <wsdl:part element="impl:nprFidi_Declino" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_DeclinoResponse">

      <wsdl:part element="impl:nprFidi_DeclinoResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Declino_SEI">

      <wsdl:operation name="nprFidi_Declino">

         <wsdl:input message="impl:nprFidi_DeclinoRequest" name="nprFidi_DeclinoRequest"/>

         <wsdl:output message="impl:nprFidi_DeclinoResponse" name="nprFidi_DeclinoResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_DeclinoSoapBinding" type="impl:NPR_Fidi_Declino_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Declino">

         <wsdlsoap:operation soapAction="nprFidi_Declino"/>

         <wsdl:input name="nprFidi_DeclinoRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_DeclinoResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_DeclinoService">

      <wsdl:port binding="impl:NPR_Fidi_DeclinoSoapBinding" name="NPR_Fidi_Declino">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Declino"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
