<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1417768642176">
      <package-mapping id="PackageMapping_1417768642176">
         <package-type>it.usi.xframe.gwc.wsutil.dto</package-type>
         <namespaceURI>http://dto.wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <package-mapping id="PackageMapping_1417768642177">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1417768642176">
         <class-type>it.usi.xframe.gwc.wsutil.dto.ExperianX031OutputMap</class-type>
         <root-type-qname id="RootTypeQname_1417768642176">
            <namespaceURI>http://dto.wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>ExperianX031OutputMap</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1417768642176">
            <java-variable-name>ndg</java-variable-name>
            <xml-element-name>ndg</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642177">
            <java-variable-name>statoPratica</java-variable-name>
            <xml-element-name>statoPratica</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642178">
            <java-variable-name>tipoOperazione</java-variable-name>
            <xml-element-name>tipoOperazione</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642179">
            <java-variable-name>progFinanziatore</java-variable-name>
            <xml-element-name>progFinanziatore</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642180">
            <java-variable-name>ruolo</java-variable-name>
            <xml-element-name>ruolo</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642181">
            <java-variable-name>dataStipula</java-variable-name>
            <xml-element-name>dataStipula</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642182">
            <java-variable-name>dataFineContratto</java-variable-name>
            <xml-element-name>dataFineContratto</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642183">
            <java-variable-name>dataAggiornamento</java-variable-name>
            <xml-element-name>dataAggiornamento</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642184">
            <java-variable-name>profInsoUlt12Mesi</java-variable-name>
            <xml-element-name>profInsoUlt12Mesi</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642185">
            <java-variable-name>numRate</java-variable-name>
            <xml-element-name>numRate</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642186">
            <java-variable-name>impRateMediaMens</java-variable-name>
            <xml-element-name>impRateMediaMens</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642187">
            <java-variable-name>impRateInso</java-variable-name>
            <xml-element-name>impRateInso</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642188">
            <java-variable-name>numMaxInso</java-variable-name>
            <xml-element-name>numMaxInso</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1417768642177">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1417768642177">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1417768642178">
         <class-type>it.usi.xframe.gwc.wsutil.dto.GetExperianDataOutput</class-type>
         <root-type-qname id="RootTypeQname_1417768642178">
            <namespaceURI>http://dto.wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>GetExperianDataOutput</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1417768642189">
            <java-variable-name>x030</java-variable-name>
            <xml-element-name>x030</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642190">
            <java-variable-name>x031</java-variable-name>
            <xml-element-name>x031</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642191">
            <java-variable-name>x032</java-variable-name>
            <xml-element-name>x032</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642192">
            <java-variable-name>x033</java-variable-name>
            <xml-element-name>x033</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1417768642179">
         <class-type>it.usi.xframe.gwc.wsutil.dto.ExperianX033OutputMap</class-type>
         <root-type-qname id="RootTypeQname_1417768642179">
            <namespaceURI>http://dto.wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>ExperianX033OutputMap</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1417768642193">
            <java-variable-name>nome</java-variable-name>
            <xml-element-name>nome</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642194">
            <java-variable-name>via</java-variable-name>
            <xml-element-name>via</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642195">
            <java-variable-name>luogo</java-variable-name>
            <xml-element-name>luogo</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642196">
            <java-variable-name>provincia</java-variable-name>
            <xml-element-name>provincia</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642197">
            <java-variable-name>indiDato</java-variable-name>
            <xml-element-name>indiDato</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642198">
            <java-variable-name>dataRegis</java-variable-name>
            <xml-element-name>dataRegis</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642199">
            <java-variable-name>importo</java-variable-name>
            <xml-element-name>importo</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642200">
            <java-variable-name>soggFavore</java-variable-name>
            <xml-element-name>soggFavore</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1417768642180">
         <class-type>it.usi.xframe.gwc.wsutil.dto.ExperianX030OutputMap</class-type>
         <root-type-qname id="RootTypeQname_1417768642180">
            <namespaceURI>http://dto.wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>ExperianX030OutputMap</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1417768642201">
            <java-variable-name>requestsNumber</java-variable-name>
            <xml-element-name>requestsNumber</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642202">
            <java-variable-name>valTotRich</java-variable-name>
            <xml-element-name>valTotRich</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642203">
            <java-variable-name>refusedRequestsNumber</java-variable-name>
            <xml-element-name>refusedRequestsNumber</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642204">
            <java-variable-name>waivedRequestsNumber</java-variable-name>
            <xml-element-name>waivedRequestsNumber</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642205">
            <java-variable-name>acceptedRequestsNumber</java-variable-name>
            <xml-element-name>acceptedRequestsNumber</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642206">
            <java-variable-name>numContratti</java-variable-name>
            <xml-element-name>numContratti</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642207">
            <java-variable-name>impegnoMensileRate</java-variable-name>
            <xml-element-name>impegnoMensileRate</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642208">
            <java-variable-name>saldoTotRateChiro</java-variable-name>
            <xml-element-name>saldoTotRateChiro</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642209">
            <java-variable-name>saldoTotIpotec</java-variable-name>
            <xml-element-name>saldoTotIpotec</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642210">
            <java-variable-name>peggStatoUlt12Mesi</java-variable-name>
            <xml-element-name>peggStatoUlt12Mesi</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642211">
            <java-variable-name>peggStatoAnteUlt12Mesi</java-variable-name>
            <xml-element-name>peggStatoAnteUlt12Mesi</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642212">
            <java-variable-name>peggStatoAccens</java-variable-name>
            <xml-element-name>peggStatoAccens</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642213">
            <java-variable-name>rateMoroseMagg7</java-variable-name>
            <xml-element-name>rateMoroseMagg7</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642214">
            <java-variable-name>rateMoroseDa3A6</java-variable-name>
            <xml-element-name>rateMoroseDa3A6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642215">
            <java-variable-name>rateMoroseDa1A2</java-variable-name>
            <xml-element-name>rateMoroseDa1A2</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642216">
            <java-variable-name>delphiScore</java-variable-name>
            <xml-element-name>delphiScore</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642217">
            <java-variable-name>gruppoDiRischio</java-variable-name>
            <xml-element-name>gruppoDiRischio</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1417768642181">
         <class-type>it.usi.xframe.gwc.wsutil.dto.GetExperianDataInput</class-type>
         <root-type-qname id="RootTypeQname_1417768642181">
            <namespaceURI>http://dto.wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>GetExperianDataInput</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1417768642218">
            <java-variable-name>ndg</java-variable-name>
            <xml-element-name>ndg</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642219">
            <java-variable-name>requestType</java-variable-name>
            <xml-element-name>requestType</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642220">
            <java-variable-name>requestDataType</java-variable-name>
            <xml-element-name>requestDataType</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642221">
            <java-variable-name>applicant</java-variable-name>
            <xml-element-name>applicant</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642222">
            <java-variable-name>currencyFlag</java-variable-name>
            <xml-element-name>currencyFlag</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642223">
            <java-variable-name>surname</java-variable-name>
            <xml-element-name>surname</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642224">
            <java-variable-name>name</java-variable-name>
            <xml-element-name>name</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642225">
            <java-variable-name>gender</java-variable-name>
            <xml-element-name>gender</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642226">
            <java-variable-name>birthDate</java-variable-name>
            <xml-element-name>birthDate</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642227">
            <java-variable-name>birthPlace</java-variable-name>
            <xml-element-name>birthPlace</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642228">
            <java-variable-name>birthProvince</java-variable-name>
            <xml-element-name>birthProvince</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642229">
            <java-variable-name>segmentFlag2</java-variable-name>
            <xml-element-name>segmentFlag2</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642230">
            <java-variable-name>street</java-variable-name>
            <xml-element-name>street</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642231">
            <java-variable-name>streetNumber</java-variable-name>
            <xml-element-name>streetNumber</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642232">
            <java-variable-name>town</java-variable-name>
            <xml-element-name>town</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642233">
            <java-variable-name>province</java-variable-name>
            <xml-element-name>province</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642234">
            <java-variable-name>segmentFlag3</java-variable-name>
            <xml-element-name>segmentFlag3</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642235">
            <java-variable-name>previousStreet</java-variable-name>
            <xml-element-name>previousStreet</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642236">
            <java-variable-name>previousStreetNumber</java-variable-name>
            <xml-element-name>previousStreetNumber</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642237">
            <java-variable-name>previousTown</java-variable-name>
            <xml-element-name>previousTown</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642238">
            <java-variable-name>previousProvince</java-variable-name>
            <xml-element-name>previousProvince</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642239">
            <java-variable-name>segmentFlag5</java-variable-name>
            <xml-element-name>segmentFlag5</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642240">
            <java-variable-name>dossierId</java-variable-name>
            <xml-element-name>dossierId</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642241">
            <java-variable-name>segmentFlag6</java-variable-name>
            <xml-element-name>segmentFlag6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642242">
            <java-variable-name>surname6</java-variable-name>
            <xml-element-name>surname6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642243">
            <java-variable-name>name6</java-variable-name>
            <xml-element-name>name6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642244">
            <java-variable-name>gender6</java-variable-name>
            <xml-element-name>gender6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642245">
            <java-variable-name>birthDate6</java-variable-name>
            <xml-element-name>birthDate6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642246">
            <java-variable-name>birthPlace6</java-variable-name>
            <xml-element-name>birthPlace6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642247">
            <java-variable-name>birthProvince6</java-variable-name>
            <xml-element-name>birthProvince6</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642248">
            <java-variable-name>applicationAlias</java-variable-name>
            <xml-element-name>applicationAlias</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642249">
            <java-variable-name>taxCode</java-variable-name>
            <xml-element-name>taxCode</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642250">
            <java-variable-name>indiType1</java-variable-name>
            <xml-element-name>indiType1</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642251">
            <java-variable-name>indiType2</java-variable-name>
            <xml-element-name>indiType2</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642252">
            <java-variable-name>creditType1</java-variable-name>
            <xml-element-name>creditType1</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642253">
            <java-variable-name>zipCode</java-variable-name>
            <xml-element-name>zipCode</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642254">
            <java-variable-name>previousZipCode</java-variable-name>
            <xml-element-name>previousZipCode</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642255">
            <java-variable-name>creditType2</java-variable-name>
            <xml-element-name>creditType2</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642256">
            <java-variable-name>creditReason</java-variable-name>
            <xml-element-name>creditReason</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642257">
            <java-variable-name>requestedAmount</java-variable-name>
            <xml-element-name>requestedAmount</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642258">
            <java-variable-name>installmentsNumber</java-variable-name>
            <xml-element-name>installmentsNumber</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642259">
            <java-variable-name>proposalNumber</java-variable-name>
            <xml-element-name>proposalNumber</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1417768642182">
         <class-type>it.usi.xframe.gwc.wsutil.dto.ExperianX032OutputMap</class-type>
         <root-type-qname id="RootTypeQname_1417768642182">
            <namespaceURI>http://dto.wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>ExperianX032OutputMap</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1417768642260">
            <java-variable-name>nome</java-variable-name>
            <xml-element-name>nome</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642261">
            <java-variable-name>localita</java-variable-name>
            <xml-element-name>localita</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642262">
            <java-variable-name>via</java-variable-name>
            <xml-element-name>via</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642263">
            <java-variable-name>provincia</java-variable-name>
            <xml-element-name>provincia</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642264">
            <java-variable-name>dataProtesto</java-variable-name>
            <xml-element-name>dataProtesto</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642265">
            <java-variable-name>valProt</java-variable-name>
            <xml-element-name>valProt</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642266">
            <java-variable-name>tipoEffetto</java-variable-name>
            <xml-element-name>tipoEffetto</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1417768642267">
            <java-variable-name>indicProt</java-variable-name>
            <xml-element-name>indicProt</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1417768642192">
         <service-interface>it.usi.xframe.gwc.wsutil.Rating_CreditBureauService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1417768642192">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>Rating_CreditBureauService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1417768642192">
            <port-name>Rating_CreditBureau</port-name>
            <java-port-name>Rating_CreditBureau</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1417768642192">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.Rating_CreditBureau</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1417768642192">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>Rating_CreditBureau</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1417768642192">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>Rating_CreditBureauSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1417768642192">
            <java-method-name>getExperianData</java-method-name>
            <wsdl-operation>getExperianData</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1417768642192">
               <param-position>0</param-position>
               <param-type>it.usi.xframe.gwc.wsutil.dto.GetExperianDataInput</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1417768642192">
                  <wsdl-message id="WSDLMessage_1417768642192">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>getExperianDataRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>input</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1417768642192">
               <method-return-value>it.usi.xframe.gwc.wsutil.dto.GetExperianDataOutput</method-return-value>
               <wsdl-message id="WSDLMessage_1417768642193">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>getExperianDataResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>getExperianDataReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
