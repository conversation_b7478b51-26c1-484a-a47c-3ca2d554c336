<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411999917727">
      <package-mapping id="PackageMapping_1411999917727">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917727">
         <class-type>it.usi.xframe.gwc.wsutil.HandleFault</class-type>
         <root-type-qname id="RootTypeQname_1411999917727">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>handleFault</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917728">
         <class-type>javax.xml.namespace.QName[]</class-type>
         <root-type-qname id="RootTypeQname_1411999917728">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>QName[unbounded]</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917729">
         <class-type>it.usi.xframe.gwc.wsutil.Destroy</class-type>
         <root-type-qname id="RootTypeQname_1411999917729">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>destroy</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917730">
         <class-type>it.usi.xframe.gwc.wsutil.DestroyResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917730">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>destroyResponse</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917731">
         <class-type>it.usi.xframe.gwc.wsutil.HandleResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917731">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>handleResponse</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917732">
         <class-type>it.usi.xframe.gwc.wsutil.GetHeadersResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917732">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;getHeadersResponse</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917727">
            <java-variable-name>getHeadersReturn</java-variable-name>
            <xml-element-name>getHeadersReturn</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917733">
         <class-type>it.usi.xframe.gwc.wsutil.Destroy</class-type>
         <root-type-qname id="RootTypeQname_1411999917733">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;destroy</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917734">
         <class-type>it.usi.xframe.gwc.wsutil.Init</class-type>
         <root-type-qname id="RootTypeQname_1411999917734">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>init</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917735">
         <class-type>it.usi.xframe.gwc.wsutil.HandleResponseResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917735">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>handleResponseResponse</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917736">
         <class-type>it.usi.xframe.gwc.wsutil.DestroyResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917736">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;destroyResponse</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917737">
         <class-type>boolean</class-type>
         <root-type-qname id="RootTypeQname_1411999917737">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>boolean</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917738">
         <class-type>it.usi.xframe.gwc.wsutil.GetHeaders</class-type>
         <root-type-qname id="RootTypeQname_1411999917738">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;getHeaders</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917739">
         <class-type>it.usi.xframe.gwc.wsutil.HandleResponseResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917739">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;handleResponseResponse</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917728">
            <java-variable-name>handleResponseReturn</java-variable-name>
            <xml-element-name>handleResponseReturn</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917740">
         <class-type>it.usi.xframe.gwc.wsutil.InitResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917740">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;initResponse</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917741">
         <class-type>it.usi.xframe.gwc.wsutil.Init</class-type>
         <root-type-qname id="RootTypeQname_1411999917741">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;init</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917729">
            <java-variable-name>handlerInfo</java-variable-name>
            <xml-element-name>handlerInfo</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917742">
         <class-type>it.usi.xframe.gwc.wsutil.HandleRequestResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917742">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;handleRequestResponse</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917730">
            <java-variable-name>handleRequestReturn</java-variable-name>
            <xml-element-name>handleRequestReturn</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917743">
         <class-type>it.usi.xframe.gwc.wsutil.GetHeaders</class-type>
         <root-type-qname id="RootTypeQname_1411999917743">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>getHeaders</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917744">
         <class-type>it.usi.xframe.gwc.wsutil.HandleFault</class-type>
         <root-type-qname id="RootTypeQname_1411999917744">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;handleFault</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917731">
            <java-variable-name>arg_0_1</java-variable-name>
            <xml-element-name>arg_0_1</xml-element-name>
         </variable-mapping>
         <variable-mapping id="VariableMapping_1411999917732">
            <java-variable-name>smc</java-variable-name>
            <xml-element-name>smc</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917745">
         <class-type>it.usi.xframe.gwc.wsutil.InitResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917745">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>initResponse</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917746">
         <class-type>it.usi.xframe.gwc.wsutil.HandleRequestResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917746">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>handleRequestResponse</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917747">
         <class-type>it.usi.xframe.gwc.wsutil.GetHeadersResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917747">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>getHeadersResponse</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917748">
         <class-type>it.usi.xframe.gwc.wsutil.HandleFaultResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917748">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>handleFaultResponse</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917749">
         <class-type>java.lang.Object</class-type>
         <root-type-qname id="RootTypeQname_1411999917749">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>anyType</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917750">
         <class-type>javax.xml.namespace.QName</class-type>
         <root-type-qname id="RootTypeQname_1411999917750">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>QName</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917751">
         <class-type>it.usi.xframe.gwc.wsutil.HandleFaultResponse1</class-type>
         <root-type-qname id="RootTypeQname_1411999917751">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;handleFaultResponse1</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917733">
            <java-variable-name>handleFaultReturn</java-variable-name>
            <xml-element-name>handleFaultReturn</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917752">
         <class-type>it.usi.xframe.gwc.wsutil.HandleFaultResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917752">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;handleFaultResponse</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917734">
            <java-variable-name>handleFaultReturn</java-variable-name>
            <xml-element-name>handleFaultReturn</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917753">
         <class-type>it.usi.xframe.gwc.wsutil.HandleFaultResponse1</class-type>
         <root-type-qname id="RootTypeQname_1411999917753">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>handleFaultResponse1</localpart>
         </root-type-qname>
         <qname-scope>element</qname-scope>
      </java-xml-type-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999917754">
         <class-type>it.usi.xframe.gwc.wsutil.HandleResponse</class-type>
         <root-type-qname id="RootTypeQname_1411999917754">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>&gt;handleResponse</localpart>
         </root-type-qname>
         <qname-scope>complexType</qname-scope>
         <variable-mapping id="VariableMapping_1411999917735">
            <java-variable-name>messageContext</java-variable-name>
            <xml-element-name>messageContext</xml-element-name>
         </variable-mapping>
      </java-xml-type-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411999917727">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.SoapRequestResponseHandler</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411999917727">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>SoapRequestResponseHandler</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411999917727">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>SoapRequestResponseHandlerSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999917727">
            <java-method-name>handleFault</java-method-name>
            <wsdl-operation>handleFault</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999917727">
               <param-position>0</param-position>
               <param-type>java.lang.Object</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999917727">
                  <wsdl-message id="WSDLMessage_1411999917727">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>handleFaultRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>arg_0_1</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999917728">
               <param-position>1</param-position>
               <param-type>java.lang.Object</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999917728">
                  <wsdl-message id="WSDLMessage_1411999917728">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>handleFaultRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>smc</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999917727">
               <method-return-value>boolean</method-return-value>
               <wsdl-message id="WSDLMessage_1411999917729">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>handleFaultResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>handleFaultReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999917728">
            <java-method-name>handleRequest</java-method-name>
            <wsdl-operation>handleRequest</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999917729">
               <param-position>0</param-position>
               <param-type>java.lang.Object</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999917729">
                  <wsdl-message id="WSDLMessage_1411999917730">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>handleRequestRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>messageContext</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999917728">
               <method-return-value>boolean</method-return-value>
               <wsdl-message id="WSDLMessage_1411999917731">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>handleRequestResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>handleRequestReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999917729">
            <java-method-name>destroy</java-method-name>
            <wsdl-operation>destroy</wsdl-operation>
            <wrapped-element></wrapped-element>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999917729">
               <method-return-value>void</method-return-value>
               <wsdl-message id="WSDLMessage_1411999917732">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>destroyResponse</localpart>
               </wsdl-message>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999917730">
            <java-method-name>getHeaders</java-method-name>
            <wsdl-operation>getHeaders</wsdl-operation>
            <wrapped-element></wrapped-element>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999917730">
               <method-return-value>javax.xml.namespace.QName[]</method-return-value>
               <wsdl-message id="WSDLMessage_1411999917733">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>getHeadersResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>getHeadersReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999917731">
            <java-method-name>handleFault</java-method-name>
            <wsdl-operation>handleFault</wsdl-operation>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999917731">
               <method-return-value>boolean</method-return-value>
               <wsdl-message id="WSDLMessage_1411999917734">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>handleFaultResponse1</localpart>
               </wsdl-message>
               <wsdl-message-part-name>handleFaultReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999917732">
            <java-method-name>handleResponse</java-method-name>
            <wsdl-operation>handleResponse</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999917730">
               <param-position>0</param-position>
               <param-type>java.lang.Object</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999917730">
                  <wsdl-message id="WSDLMessage_1411999917735">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>handleResponseRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>messageContext</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999917732">
               <method-return-value>boolean</method-return-value>
               <wsdl-message id="WSDLMessage_1411999917736">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>handleResponseResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>handleResponseReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999917733">
            <java-method-name>init</java-method-name>
            <wsdl-operation>init</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999917731">
               <param-position>0</param-position>
               <param-type>java.lang.Object</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999917731">
                  <wsdl-message id="WSDLMessage_1411999917737">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>initRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>handlerInfo</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999917733">
               <method-return-value>void</method-return-value>
               <wsdl-message id="WSDLMessage_1411999917738">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>initResponse</localpart>
               </wsdl-message>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
