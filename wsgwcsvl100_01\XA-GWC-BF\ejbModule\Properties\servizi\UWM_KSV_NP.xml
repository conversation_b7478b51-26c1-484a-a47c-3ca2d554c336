<?xml version="1.0"?>
<!-- This XML has been Auto-Generated -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
                  <param name="SERVIZIO" />
                  <param name="VERSIONE" />
                  <param name="COD_BAN_SERV" />
                  <param name="CEKI2_TYP_BUSINESS_ACC" />    
					<param name="CEKI2_NUMBER_ACCOUNT" />     
					<param name="CEKI2_BRANCH" />             
					<param name="CEKI2_CURRENCY_ACCOUNT" />   
					<param name="CEKI2_TIMESTAMP" />          
					<param name="CEKI2_NDG" />                
					<param name="CEKI2_TYP_MESSAGE" />        
					<param name="CEKI2_USER" />               
					<param name="CEKI2_CREDIT_AMOUNT" />      
					<param name="CEKI2_TYP_PAYBACK" />        
					<param name="CEKI2_DT_LAST_PAYMENT" />    
					<param name="CEKI2_OUTSTAND_AMOUNT" />    
					<param name="CEKI2_TYP_CREDIT" />         
					<param name="CEKI2_CURRENCY" />           
					<param name="CEKI2_PERIODE" />            
					<param name="CEKI2_FINAL_DATE" />         
					<param name="CEKI2_AMOUNT_INSTAL" />      
					<param name="CEKI2_DT_FIRST_INSTAL" />    
					<param name="CEKI2_FLAG_REFUSAL" />       
					<param name="CEKI2_REFUSAL_DATE" />       
					<param name="CEKI2_REFUSAL_CAUSE" />      
					<param name="CEKI2_TYP_INFORMATION" />    
					<param name="CEKI2_DT_REPAYMENT" />       
					<param name="CEKI2_DT_CLOSING" />         
					<param name="CEKI2_SETTLEMENT_INFO" />    
					<param name="CEKI2_FLAG_NEG1" />          
					<param name="CEKI2_DT_NEG1" />            
					<param name="CEKI2_FLAG_NEG2" />          
					<param name="CEKI2_DT_NEG2" />            
					<param name="CEKI2_FLAG_NEG3" />          
					<param name="CEKI2_DT_NEG3" />         
					<param name="XF_GAUSS_ID" />                 
				</input>
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
    </provider>
	<protocol>
		<bridge>
			<request>
				<input />
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</bridge>
        <params>
            <hostService>UWM_KSV_NP</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0001</version>
        </params>
	</protocol>
	<channel>
        <params>
            <transaction>WUW1</transaction>
            <program>PC00WUW1</program>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
	</channel>
</service>