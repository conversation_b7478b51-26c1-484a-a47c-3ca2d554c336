<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.14.284" utente="Gasparini" Timestamp="06/05/2004 19.07.49" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="X01"/>
					<param name="X02"/>
					<param name="X03"/>
					<param name="X04"/>
					<param name="X90"/>
					<param name="X91"/>
					<param name="X97"/>
					<param name="X98"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NRussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output>
					<hostService id="1" name="SZL7P0L7"/>
					<hostService id="2" name="SZL8P0L8"/>
					<hostService id="3" name="SZBSP0BS"/>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<scheduler>SZ00</scheduler>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>SZ00</transaction>
			<timeout>60000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>
