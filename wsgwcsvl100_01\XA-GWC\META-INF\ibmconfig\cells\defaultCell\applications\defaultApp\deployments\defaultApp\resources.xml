<?xml version="1.0" encoding="UTF-8"?>
<xmi:XMI xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:resources.j2c="http://www.ibm.com/websphere/appserver/schemas/5.0/resources.j2c.xmi" xmlns:resources.jms="http://www.ibm.com/websphere/appserver/schemas/5.0/resources.jms.xmi" xmlns:resources.mail="http://www.ibm.com/websphere/appserver/schemas/5.0/resources.mail.xmi" xmlns:resources.url="http://www.ibm.com/websphere/appserver/schemas/5.0/resources.url.xmi">
  <resources.jms:JMSProvider xmi:id="builtin_jmsprovider" name="WebSphere JMS Provider" description="V5 Default Messaging Provider" externalInitialContextFactory="" externalProviderURL=""/>
  <resources.jms:JMSProvider xmi:id="builtin_mqprovider" name="WebSphere MQ JMS Provider" description="WebSphere MQ Messaging Provider" externalInitialContextFactory="" externalProviderURL="">
    <classpath>${MQJMS_LIB_ROOT}</classpath>
    <nativepath>${MQJMS_LIB_ROOT}</nativepath>
  </resources.jms:JMSProvider>
  <resources.j2c:J2CResourceAdapter xmi:id="builtin_rra" name="WebSphere Relational Resource Adapter" description="Built-in Relational Resource Adapter for WebSphere Persistence" archivePath="${WAS_LIBS_DIR}/rsadapter.rar">
    <classpath>${WAS_LIBS_DIR}/rsadapter.rar</classpath>
    <propertySet xmi:id="J2EEResourcePropertySet_1411997919591"/>
    <deploymentDescriptor xmi:id="Connector_1411997919591" displayName="WS_RdbResourceAdapter" vendorName="IBM" specVersion="1.5" eisType="RRA" version="6.0">
      <icons xmi:id="IconType_1411997919591" smallIcon="rdb_small_icon.jpg" largeIcon="rdb_large_icon.jpg"/>
      <displayNames xmi:id="DisplayName_1411997919591" value="WS_RdbResourceAdapter"/>
      <displayNames xmi:id="DisplayName_1411997919592" value="WS_RdbResourceAdapter"/>
      <descriptions xmi:id="Description_1411997919591" value="IBM Relational ResourceAdapter"/>
      <license xmi:id="License_1411997919591" required="false">
        <descriptions xmi:id="Description_1411997919592" value="IBM Relational ResourceAdapter"/>
      </license>
      <resourceAdapter xmi:id="ResourceAdapter_1411997919591" transactionSupport="NoTransaction" reauthenticationSupport="false" resourceAdapterClass="com.ibm.ws.rsadapter.spi.WSResourceAdapterImpl">
        <outboundResourceAdapter xmi:id="OutboundResourceAdapter_1411997919591" reauthenticationSupport="false" transactionSupport="XATransaction">
          <connectionDefinitions xmi:id="ConnectionDefinition_1411997919591" managedConnectionFactoryClass="com.ibm.ws.rsadapter.spi.WSManagedConnectionFactoryImpl" connectionFactoryInterface="javax.resource.cci.ConnectionFactory" connectionFactoryImplClass="com.ibm.ws.rsadapter.cci.WSRdbConnectionFactoryImpl" connectionInterface="javax.resource.cci.Connection" connectionImplClass="com.ibm.ws.rsadapter.cci.WSRdbConnectionImpl">
            <configProperties xmi:id="ConfigProperty_1411997919591" name="ConnectionFactoryType" type="java.lang.Integer" value="2">
              <descriptions xmi:id="Description_1411997919593" value="A constant indicating the type of connection factory: WSJdbcDataSource (1) or WSRdbConnectionFactory (2)"/>
            </configProperties>
          </connectionDefinitions>
          <authenticationMechanisms xmi:id="AuthenticationMechanism_1411997919591" authenticationMechanismType="BasicPassword" credentialInterface="javax.resource.spi.security.PasswordCredential">
            <descriptions xmi:id="Description_1411997919594" value="BasicPassword authentication"/>
          </authenticationMechanisms>
          <authenticationMechanisms xmi:id="AuthenticationMechanism_1411997919592" authenticationMechanismType="Kerbv5" credentialInterface="javax.resource.spi.security.GenericCredential">
            <descriptions xmi:id="Description_1411997919595" value="Kerbv5 Authentication"/>
          </authenticationMechanisms>
        </outboundResourceAdapter>
      </resourceAdapter>
    </deploymentDescriptor>
    <connectionDefTemplateProps xmi:id="ConnectionDefTemplateProps_1411997919591" ConnectionDefinition="ConnectionDefinition_1411997919591"/>
  </resources.j2c:J2CResourceAdapter>
  <resources.mail:MailProvider xmi:id="builtin_mailprovider" name="Built-in Mail Provider" description="The built-in mail provider">
    <protocolProviders xmi:id="ProtocolProvider_1411997919591" protocol="smtp" classname="com.sun.mail.smtp.SMTPTransport" type="TRANSPORT"/>
    <protocolProviders xmi:id="ProtocolProvider_1411997919592" protocol="pop3" classname="com.sun.mail.pop3.POP3Store" type="STORE"/>
    <protocolProviders xmi:id="ProtocolProvider_1411997919593" protocol="imap" classname="com.sun.mail.imap.IMAPStore" type="STORE"/>
  </resources.mail:MailProvider>
  <resources.url:URLProvider xmi:id="URLProvider_1" name="Default URL Provider" streamHandlerClassName="unused" protocol="unused"/>
  <resources.j2c:J2CResourceAdapter xmi:id="J2CResourceAdapter_1148393643865" name="SIB JMS Resource Adapter" archivePath="${WAS_INSTALL_ROOT}/installedConnectors/sib.api.jmsra.rar">
    <classpath>${WAS_INSTALL_ROOT}/installedConnectors/sib.api.jmsra.rar</classpath>
    <propertySet xmi:id="J2EEResourcePropertySet_1411997919592"/>
    <deploymentDescriptor xmi:id="Connector_1411997919592" displayName="WebSphere Default Messaging Provider" vendorName="IBM" specVersion="1.5" eisType="JMS Provider" version="0.3">
      <displayNames xmi:id="DisplayName_1411997919593" value="WebSphere Default Messaging Provider"/>
      <displayNames xmi:id="DisplayName_1411997919594" value="WebSphere Default Messaging Provider"/>
      <displayNames xmi:id="DisplayName_1411997919595" value="WebSphere Default Messaging Provider"/>
      <displayNames xmi:id="DisplayName_1411997919596" value="WebSphere Default Messaging Provider"/>
      <displayNames xmi:id="DisplayName_1411997919597" value="WebSphere Default Messaging Provider"/>
      <resourceAdapter xmi:id="ResourceAdapter_1411997919592" transactionSupport="NoTransaction" reauthenticationSupport="false" resourceAdapterClass="com.ibm.ws.sib.api.jmsra.impl.JmsJcaResourceAdapterImpl">
        <outboundResourceAdapter xmi:id="OutboundResourceAdapter_1411997919592" reauthenticationSupport="false" transactionSupport="XATransaction">
          <connectionDefinitions xmi:id="ConnectionDefinition_1411997919592" managedConnectionFactoryClass="com.ibm.ws.sib.api.jmsra.impl.JmsJcaManagedQueueConnectionFactoryImpl" connectionFactoryInterface="javax.jms.QueueConnectionFactory" connectionFactoryImplClass="com.ibm.ws.sib.api.jms.impl.JmsQueueConnFactoryImpl" connectionInterface="javax.jms.QueueConnection" connectionImplClass="com.ibm.ws.sib.api.jms.impl.JmsQueueConnectionImpl">
            <configProperties xmi:id="ConfigProperty_1411997919592" name="BusName" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919593" name="ClientID" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919594" name="UserName" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919595" name="Password" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919596" name="NonPersistentMapping" type="java.lang.String" value="ExpressNonPersistent"/>
            <configProperties xmi:id="ConfigProperty_1411997919597" name="PersistentMapping" type="java.lang.String" value="ReliablePersistent"/>
            <configProperties xmi:id="ConfigProperty_1411997919598" name="ReadAhead" type="java.lang.String" value="Default"/>
            <configProperties xmi:id="ConfigProperty_1411997919599" name="Target" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919600" name="TargetType" type="java.lang.String" value="BusMember"/>
            <configProperties xmi:id="ConfigProperty_1411997919601" name="TargetSignificance" type="java.lang.String" value="Preferred"/>
            <configProperties xmi:id="ConfigProperty_1411997919602" name="RemoteProtocol" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919603" name="TargetTransportChain" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919604" name="ProviderEndpoints" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919605" name="ConnectionProximity" type="java.lang.String" value="Bus"/>
            <configProperties xmi:id="ConfigProperty_1411997919606" name="TemporaryQueueNamePrefix" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919607" name="ShareDataSourceWithCMP" type="java.lang.Boolean" value="false"/>
          </connectionDefinitions>
          <connectionDefinitions xmi:id="ConnectionDefinition_1411997919593" managedConnectionFactoryClass="com.ibm.ws.sib.api.jmsra.impl.JmsJcaManagedTopicConnectionFactoryImpl" connectionFactoryInterface="javax.jms.TopicConnectionFactory" connectionFactoryImplClass="com.ibm.ws.sib.api.jms.impl.JmsTopicConnFactoryImpl" connectionInterface="javax.jms.TopicConnection" connectionImplClass="com.ibm.ws.sib.api.jms.impl.JmsTopicConnectionImpl">
            <configProperties xmi:id="ConfigProperty_1411997919608" name="BusName" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919609" name="ClientID" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919610" name="UserName" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919611" name="Password" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919612" name="NonPersistentMapping" type="java.lang.String" value="ExpressNonPersistent"/>
            <configProperties xmi:id="ConfigProperty_1411997919613" name="PersistentMapping" type="java.lang.String" value="ReliablePersistent"/>
            <configProperties xmi:id="ConfigProperty_1411997919614" name="DurableSubscriptionHome" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919615" name="ReadAhead" type="java.lang.String" value="Default"/>
            <configProperties xmi:id="ConfigProperty_1411997919616" name="Target" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919617" name="TargetType" type="java.lang.String" value="BusMember"/>
            <configProperties xmi:id="ConfigProperty_1411997919618" name="TargetSignificance" type="java.lang.String" value="Preferred"/>
            <configProperties xmi:id="ConfigProperty_1411997919619" name="RemoteProtocol" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919620" name="TargetTransportChain" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919621" name="ProviderEndpoints" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919622" name="ConnectionProximity" type="java.lang.String" value="Bus"/>
            <configProperties xmi:id="ConfigProperty_1411997919623" name="TemporaryTopicNamePrefix" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919624" name="ShareDataSourceWithCMP" type="java.lang.Boolean" value="false"/>
            <configProperties xmi:id="ConfigProperty_1411997919625" name="ShareDurableSubscriptions" type="java.lang.String" value="InCluster"/>
          </connectionDefinitions>
          <connectionDefinitions xmi:id="ConnectionDefinition_1411997919594" managedConnectionFactoryClass="com.ibm.ws.sib.api.jmsra.impl.JmsJcaManagedConnectionFactoryImpl" connectionFactoryInterface="javax.jms.ConnectionFactory" connectionFactoryImplClass="com.ibm.ws.sib.api.jms.impl.JmsConnFactoryImpl" connectionInterface="javax.jms.Connection" connectionImplClass="com.ibm.ws.sib.api.jms.impl.JmsConnectionImpl">
            <configProperties xmi:id="ConfigProperty_1411997919626" name="BusName" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919627" name="ClientID" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919628" name="UserName" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919629" name="Password" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919630" name="NonPersistentMapping" type="java.lang.String" value="ExpressNonPersistent"/>
            <configProperties xmi:id="ConfigProperty_1411997919631" name="PersistentMapping" type="java.lang.String" value="ReliablePersistent"/>
            <configProperties xmi:id="ConfigProperty_1411997919632" name="DurableSubscriptionHome" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919633" name="ReadAhead" type="java.lang.String" value="Default"/>
            <configProperties xmi:id="ConfigProperty_1411997919634" name="Target" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919635" name="TargetType" type="java.lang.String" value="BusMember"/>
            <configProperties xmi:id="ConfigProperty_1411997919636" name="TargetSignificance" type="java.lang.String" value="Preferred"/>
            <configProperties xmi:id="ConfigProperty_1411997919637" name="RemoteProtocol" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919638" name="TargetTransportChain" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919639" name="ProviderEndpoints" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919640" name="ConnectionProximity" type="java.lang.String" value="Bus"/>
            <configProperties xmi:id="ConfigProperty_1411997919641" name="TemporaryQueueNamePrefix" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919642" name="TemporaryTopicNamePrefix" type="java.lang.String"/>
            <configProperties xmi:id="ConfigProperty_1411997919643" name="ShareDataSourceWithCMP" type="java.lang.Boolean" value="false"/>
            <configProperties xmi:id="ConfigProperty_1411997919644" name="ShareDurableSubscriptions" type="java.lang.String" value="InCluster"/>
          </connectionDefinitions>
          <authenticationMechanisms xmi:id="AuthenticationMechanism_1411997919593" authenticationMechanismType="BasicPassword" credentialInterface="javax.resource.spi.security.PasswordCredential"/>
        </outboundResourceAdapter>
        <inboundResourceAdapter xmi:id="InboundResourceAdapter_1411997919591">
          <messageAdapter xmi:id="MessageAdapter_1411997919591">
            <messageListeners xmi:id="MessageListener_1411997919591" messageListenerType="javax.jms.MessageListener">
              <activationSpec xmi:id="ActivationSpec_1411997919591" activationSpecClass="com.ibm.ws.sib.api.jmsra.impl.JmsJcaActivationSpecImpl">
                <requiredConfigProperties xmi:id="RequiredConfigPropertyType_1411997919591" name="destination"/>
                <requiredConfigProperties xmi:id="RequiredConfigPropertyType_1411997919592" name="destinationType"/>
                <requiredConfigProperties xmi:id="RequiredConfigPropertyType_1411997919593" name="busName"/>
              </activationSpec>
            </messageListeners>
          </messageAdapter>
        </inboundResourceAdapter>
        <adminObjects xmi:id="AdminObject_1411997919591" adminObjectInterface="javax.jms.Queue" adminObjectClass="com.ibm.ws.sib.api.jms.impl.JmsQueueImpl">
          <configProperties xmi:id="ConfigProperty_1411997919645" name="QueueName" type="java.lang.String"/>
          <configProperties xmi:id="ConfigProperty_1411997919646" name="DeliveryMode" type="java.lang.String" value="Application"/>
          <configProperties xmi:id="ConfigProperty_1411997919647" name="TimeToLive" type="java.lang.Long"/>
          <configProperties xmi:id="ConfigProperty_1411997919648" name="Priority" type="java.lang.Integer"/>
          <configProperties xmi:id="ConfigProperty_1411997919649" name="ReadAhead" type="java.lang.String" value="AsConnection"/>
          <configProperties xmi:id="ConfigProperty_1411997919650" name="BusName" type="java.lang.String"/>
        </adminObjects>
        <adminObjects xmi:id="AdminObject_1411997919592" adminObjectInterface="javax.jms.Topic" adminObjectClass="com.ibm.ws.sib.api.jms.impl.JmsTopicImpl">
          <configProperties xmi:id="ConfigProperty_1411997919651" name="TopicSpace" type="java.lang.String" value="Default.Topic.Space"/>
          <configProperties xmi:id="ConfigProperty_1411997919652" name="TopicName" type="java.lang.String"/>
          <configProperties xmi:id="ConfigProperty_1411997919653" name="DeliveryMode" type="java.lang.String" value="Application"/>
          <configProperties xmi:id="ConfigProperty_1411997919654" name="TimeToLive" type="java.lang.Long"/>
          <configProperties xmi:id="ConfigProperty_1411997919655" name="Priority" type="java.lang.Integer"/>
          <configProperties xmi:id="ConfigProperty_1411997919656" name="ReadAhead" type="java.lang.String" value="AsConnection"/>
          <configProperties xmi:id="ConfigProperty_1411997919657" name="BusName" type="java.lang.String"/>
        </adminObjects>
      </resourceAdapter>
    </deploymentDescriptor>
    <connectionDefTemplateProps xmi:id="ConnectionDefTemplateProps_1411997919592" ConnectionDefinition="ConnectionDefinition_1411997919592">
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919591" name="BusName" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919607" name="ClientID" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919608" name="ConnectionProximity" type="java.lang.String" value="Bus" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919609" name="durableSubscriptionHome" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919610" name="NonPersistentMapping" type="java.lang.String" value="ExpressNonPersistent" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919611" name="Password" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919612" name="PersistentMapping" type="java.lang.String" value="ReliablePersistent" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919613" name="ProviderEndpoints" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919614" name="ReadAhead" type="java.lang.String" value="Default" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919615" name="RemoteProtocol" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919616" name="TargetTransportChain" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919617" name="ShareDataSourceWithCMP" type="java.lang.Boolean" value="false" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919618" name="shareDurableSubscriptions" type="java.lang.String" value="AsCluster" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919619" name="Target" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919620" name="TargetSignificance" type="java.lang.String" value="Preferred" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919621" name="TargetType" type="java.lang.String" value="BusMember" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919622" name="TemporaryQueueNamePrefix" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919623" name="temporaryTopicNamePrefix" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919624" name="UserName" type="java.lang.String" required="false"/>
    </connectionDefTemplateProps>
    <connectionDefTemplateProps xmi:id="ConnectionDefTemplateProps_1411997919607" ConnectionDefinition="ConnectionDefinition_1411997919593">
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919625" name="BusName" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919626" name="ClientID" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919627" name="ConnectionProximity" type="java.lang.String" value="Bus" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919628" name="DurableSubscriptionHome" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919629" name="NonPersistentMapping" type="java.lang.String" value="ExpressNonPersistent" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919630" name="Password" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919631" name="PersistentMapping" type="java.lang.String" value="ReliablePersistent" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919632" name="ProviderEndpoints" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919633" name="ReadAhead" type="java.lang.String" value="Default" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919634" name="RemoteProtocol" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919635" name="TargetTransportChain" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919636" name="ShareDataSourceWithCMP" type="java.lang.Boolean" value="false" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919637" name="ShareDurableSubscriptions" type="java.lang.String" value="InCluster" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919638" name="Target" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919639" name="TargetSignificance" type="java.lang.String" value="Preferred" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919640" name="TargetType" type="java.lang.String" value="BusMember" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919641" name="temporaryQueueNamePrefix" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919642" name="TemporaryTopicNamePrefix" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919643" name="UserName" type="java.lang.String" required="false"/>
    </connectionDefTemplateProps>
    <connectionDefTemplateProps xmi:id="ConnectionDefTemplateProps_1411997919608" ConnectionDefinition="ConnectionDefinition_1411997919594">
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919644" name="BusName" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919645" name="ClientID" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919646" name="ConnectionProximity" type="java.lang.String" value="Bus" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919647" name="DurableSubscriptionHome" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919648" name="NonPersistentMapping" type="java.lang.String" value="ExpressNonPersistent" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919649" name="Password" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919650" name="PersistentMapping" type="java.lang.String" value="ReliablePersistent" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919651" name="ProviderEndpoints" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919652" name="ReadAhead" type="java.lang.String" value="Default" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919653" name="RemoteProtocol" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919654" name="TargetTransportChain" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919655" name="ShareDataSourceWithCMP" type="java.lang.Boolean" value="false" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919656" name="ShareDurableSubscriptions" type="java.lang.String" value="InCluster" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919657" name="Target" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919658" name="TargetSignificance" type="java.lang.String" value="Preferred" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919659" name="TargetType" type="java.lang.String" value="BusMember" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919660" name="TemporaryQueueNamePrefix" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919661" name="TemporaryTopicNamePrefix" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919662" name="UserName" type="java.lang.String" required="false"/>
    </connectionDefTemplateProps>
    <activationSpecTemplateProps xmi:id="ActivationSpecTemplateProps_1411997919607" activationSpec="ActivationSpec_1411997919591">
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919663" name="acknowledgeMode" type="java.lang.String" value="Auto-acknowledge" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919664" name="busName" type="java.lang.String" required="true"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919665" name="clientId" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919666" name="destination" type="javax.jms.Destination" required="true"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919667" name="destinationType" type="java.lang.String" required="true"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919668" name="durableSubscriptionHome" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919669" name="maxBatchSize" type="java.lang.Integer" value="1" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919670" name="maxConcurrency" type="java.lang.Integer" value="10" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919671" name="messageSelector" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919672" name="password" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919673" name="readAhead" type="java.lang.String" value="Default" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919674" name="shareDataSourceWithCMP" type="java.lang.Boolean" value="false" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919675" name="shareDurableSubscriptions" type="java.lang.String" value="InCluster" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919676" name="subscriptionDurability" type="java.lang.String" value="NonDurable" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919677" name="subscriptionName" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919678" name="targetTransportChain" type="java.lang.String" required="false"/>
      <resourceProperties xmi:id="J2EEResourceProperty_1411997919679" name="userName" type="java.lang.String" required="false"/>
      <complexPropertySet xmi:id="J2EEResourcePropertySet_1411997919607">
        <resourceProperties xmi:id="J2EEResourceProperty_1411997919680" name="destination" type="javax.jms.Destination" required="false"/>
      </complexPropertySet>
    </activationSpecTemplateProps>
    <adminObjectTemplateProps xmi:id="AdminObjectTemplateProps_1411997919607" adminObject="AdminObject_1411997919591">
      <properties xmi:id="J2EEResourceProperty_1411997919681" name="BusName" type="java.lang.String" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919682" name="DeliveryMode" type="java.lang.String" value="Application" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919683" name="forwardRoutingPath" type="[Ljava.lang.String;" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919684" name="Priority" type="java.lang.Integer" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919685" name="QueueName" type="java.lang.String" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919686" name="ReadAhead" type="java.lang.String" value="AsConnection" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919687" name="reverseRoutingPath" type="[Ljava.lang.String;" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919688" name="TimeToLive" type="java.lang.Long" required="false"/>
    </adminObjectTemplateProps>
    <adminObjectTemplateProps xmi:id="AdminObjectTemplateProps_1411997919608" adminObject="AdminObject_1411997919592">
      <properties xmi:id="J2EEResourceProperty_1411997919689" name="BusName" type="java.lang.String" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919690" name="DeliveryMode" type="java.lang.String" value="Application" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919691" name="forwardRoutingPath" type="[Ljava.lang.String;" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919692" name="Priority" type="java.lang.Integer" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919693" name="ReadAhead" type="java.lang.String" value="AsConnection" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919694" name="reverseRoutingPath" type="[Ljava.lang.String;" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919695" name="TimeToLive" type="java.lang.Long" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919696" name="TopicName" type="java.lang.String" required="false"/>
      <properties xmi:id="J2EEResourceProperty_1411997919697" name="TopicSpace" type="java.lang.String" value="Default.Topic.Space" required="false"/>
    </adminObjectTemplateProps>
  </resources.j2c:J2CResourceAdapter>
</xmi:XMI>
