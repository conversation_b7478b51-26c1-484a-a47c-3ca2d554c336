<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="23/03/2004 13.08.28" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
	security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="UW28I_BANCA" />
					<param name="UW28I_NDG" />
					<param name="UW28I_SPORTELLO" />
					<param name="UW28I_NUM_CONTO" />
					<param name="UW28I_PROG_FIDO" />
					<param name="UW28I_FORMA_TEC_FIDO" />
					<param name="UW28I_FORMA_TEC_RAPP" />
					<param name="UW28I_CODICE_COND" />
					<param name="UW28I_TASSO" />
					<param name="UW28I_SPREAD" />
					<param name="UW28I_INDICE_RIF" />
                    <param name="XF_GAUSS_ID"/>
				</input>
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</road>
       <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
    </provider>
	<protocol>
		<bridge>
			<request>
				<input />
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</bridge>
		<params>
			<hostService>UWM_DEROGA_COND</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUW1</transaction>
			<program>PC00WUW1</program>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>