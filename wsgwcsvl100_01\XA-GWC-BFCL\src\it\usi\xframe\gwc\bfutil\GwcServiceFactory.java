package it.usi.xframe.gwc.bfutil;

import it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade;
import it.usi.xframe.system.eservice.AbstractServiceFactory;
import it.usi.xframe.system.eservice.ServiceFactoryException;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * <AUTHOR>
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class GwcServiceFactory extends AbstractServiceFactory {

	private static GwcServiceFactory me;
	private static Log logger = LogFactory.getLog(GwcServiceFactory.class);

	/**
	 * 
	 */
	private GwcServiceFactory() {
		super();
	}

	public static GwcServiceFactory getInstance() {
		if (me == null){ 
			try {
				me = new GwcServiceFactory();
			} catch (Exception e) {
				logger.error("Failed to load GwcServiceFactory: " + e.getMessage(), e);
			}
		}
		return me;		
	}

	/**
	 * 
	 * @return <code>IGwcMainServiceFacade</code>
	 * @throws ServiceFactoryException
	 */
	public IGwcMainServiceFacade getGwcMainServiceFacade() throws ServiceFactoryException {
		return (IGwcMainServiceFacade)this.getXfrServiceFacade(IGwcMainServiceFacade.KEY_ID);
	}
}
