<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411999229992">
      <package-mapping id="PackageMapping_1411999229992">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999229992">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411999229992">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411999229992">
         <service-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_IterPropostaService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411999229992">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_IterPropostaService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411999229992">
            <port-name>NPR_Fidi_IterProposta</port-name>
            <java-port-name>NPR_Fidi_IterProposta</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411999229992">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_IterProposta_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411999229992">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_IterProposta_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411999229992">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_IterPropostaSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999229992">
            <java-method-name>nprFidi_IterProposta</java-method-name>
            <wsdl-operation>nprFidi_IterProposta</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999229992">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999229992">
                  <wsdl-message id="WSDLMessage_1411999229992">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_IterPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x00</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999229993">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999229993">
                  <wsdl-message id="WSDLMessage_1411999229993">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_IterPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x01</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999229994">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999229994">
                  <wsdl-message id="WSDLMessage_1411999229994">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_IterPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x02</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999229995">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999229995">
                  <wsdl-message id="WSDLMessage_1411999229995">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_IterPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x03</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999229992">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411999229996">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>nprFidi_IterPropostaResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>nprFidi_IterPropostaReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
