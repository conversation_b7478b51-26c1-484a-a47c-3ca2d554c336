<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Pennino">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_PenninoResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_PenninoReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_PenninoRequest">

      <wsdl:part element="impl:nprFidi_Pennino" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_PenninoResponse">

      <wsdl:part element="impl:nprFidi_PenninoResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Pennino_SEI">

      <wsdl:operation name="nprFidi_Pennino">

         <wsdl:input message="impl:nprFidi_PenninoRequest" name="nprFidi_PenninoRequest"/>

         <wsdl:output message="impl:nprFidi_PenninoResponse" name="nprFidi_PenninoResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_PenninoSoapBinding" type="impl:NPR_Fidi_Pennino_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Pennino">

         <wsdlsoap:operation soapAction="nprFidi_Pennino"/>

         <wsdl:input name="nprFidi_PenninoRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_PenninoResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_PenninoService">

      <wsdl:port binding="impl:NPR_Fidi_PenninoSoapBinding" name="NPR_Fidi_Pennino">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Pennino"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
