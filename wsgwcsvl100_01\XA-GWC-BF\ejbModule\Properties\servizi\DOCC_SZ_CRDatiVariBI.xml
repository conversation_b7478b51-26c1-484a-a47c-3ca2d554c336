<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="25/11/2002 16.52.18" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="X01" />
                    <param name="X02" />
                    <param name="X03" />
                    <param name="X04" />
                    <param name="X97" />
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="SZ62P062" />
                        <!--<param select="CR_X01" name="X01" />
                        <param select="CR_X02" name="X02" />
                        <param select="CR_X03" name="X03" />
                        <param select="CR_X04" name="X04" />
                        <param select="CR_X97" name="X97" />
                    </hostService>-->
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>SZ00</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>SZ00</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>