package it.usi.xframe.gwc.bfutil.base;

import java.io.Serializable;

import it.usi.xframe.system.errors.XFRSevereException;

public class BaseSevereException extends XFRSevereException implements Serializable {

	public BaseSevereException () {
	}

	public BaseSevereException (String message) {
		super(message);
	}

	public BaseSevereException (String message, Throwable cause) {
		super(message, cause);
	}

	public BaseSevereException (String message, Object[] parameters) {
		super(message, parameters);
	}

	public BaseSevereException (String message, Object[] parameters, Throwable cause) {
		super(message, parameters, cause);
	}

	public BaseSevereException (Throwable cause) {
		super(cause);
	}

}
