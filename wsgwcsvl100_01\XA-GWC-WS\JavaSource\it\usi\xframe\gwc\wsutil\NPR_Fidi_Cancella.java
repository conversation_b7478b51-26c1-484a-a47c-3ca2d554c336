/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_Fidi_Cancella {
	
	//	Default Constructor - RSA8 migration prerequisites	
	public NPR_Fidi_Cancella(){
			
	}	

	public String nprFidi_Cancella(String x00, String x01, String x02, String x03, String x04) throws Exception { 

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprFidi_Cancella(x00, x01, x02, x03, x04);
	}     
}
