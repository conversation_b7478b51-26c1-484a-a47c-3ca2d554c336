<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="3.3.192" utente="tommasi" Timestamp="04/03/2002 15.06.22" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
<route appOwner="egu" serviceName="censimentoCORoma" active="true"/>
	<dispenser>
		<road>
			<request>
				<input>
					<param name="EGUK06CE-STATUS-AGG"/>
					<param name="EGUK06CE-TELEFONI-AGG"/>
					<param name="EGUK06CE-INDIR-CORR-AGG"/>
					<param name="EGUK06CE-ALTRI-DATI-AGG"/>
					<param name="EGUK06CE-CODICI-VARI-AGG"/>
					<param name="EGUK06CE-SEDE-FISCALE-AGG"/>
					<param name="EGUK06CE-DATI-PRIVACY-AGG"/>
					<param name="EGUK06CE-DATI-GENERALI-AGG"/>
					<param name="EGUK06CE-DATI-AZIENDALI-AGG"/>
					<param name="EGUK06CE-PROFESSIONI-ALT-AGG"/>
					<param name="EGUK06CE-INTESTAZIONE-LONG-AGG"/>
					<param name="EGUK06CE-CLASSIFICAZIONE-ALT-A"/>
					<param name="EGUK06CE-NDG"/>
					<param name="EGUK06CE-CONTROLLO-INT"/>
					<param name="EGUK06CE-CODICE-FISCALE"/>
					<param name="EGUK06CE-PARTITA-IVA"/>
					<param name="EGUK06CE-SPORTELLO-CAPOFILA"/>
					<param name="EGUK06CE-INTESTAZIONE-A"/>
					<param name="EGUK06CE-INTESTAZIONE-B"/>
					<param name="EGUK06CE-TIPO-NDG"/>
					<param name="EGUK06CE-SETTORISTA"/>
					<param name="EGUK06CE-PSEUDONIMO"/>
					<param name="EGUK06CE-STATO-RES"/>
					<param name="EGUK06CE-COMUNE-RES"/>
					<param name="EGUK06CE-LOCALITA-RES"/>
					<param name="EGUK06CE-PROVINCIA-RES"/>
					<param name="EGUK06CE-CAP-RES"/>
					<param name="EGUK06CE-VIA-RES"/>
					<param name="EGUK06CE-PRESSO-RES"/>
					<param name="EGUK06CE-INTESTAZIONE-CORR"/>
					<param name="EGUK06CE-STATO-CORR"/>
					<param name="EGUK06CE-COMUNE-CORR"/>
					<param name="EGUK06CE-LOCALITA-CORR"/>
					<param name="EGUK06CE-PROVINCIA-CORR"/>
					<param name="EGUK06CE-CAP-CORR"/>
					<param name="EGUK06CE-VIA-CORR"/>
					<param name="EGUK06CE-PRESSO-CORR"/>
					<param name="EGUK06CE-UFFICIO-CORR"/>
					<param name="EGUK06CE-CASELLARIO-CORR"/>
					<param name="EGUK06CE-CODICE-FISCALE-ESTERO"/>
					<param name="EGUK06CE-RAMO"/>
					<param name="EGUK06CE-SETTORE"/>
					<param name="EGUK06CE-RAMO-ALT"/>
					<param name="EGUK06CE-SETTORE-ALT"/>
					<param name="EGUK06CE-COMUNE-ALT"/>
					<param name="EGUK06CE-PROVINCIA-ALT"/>
					<param name="EGUK06CE-STATO-ALT"/>
					<param name="EGUK06CE-SEGMENTO"/>
					<param name="EGUK06CE-SIT-GIURIDICA"/>
					<param name="EGUK06CE-STATUS"/>
					<param name="EGUK06CE-UBIC-DOC-RIFERIM"/>
					<param name="EGUK06CE-UBIC-DOC-SPORTELLO"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-1"/>
					<param name="EGUK06CE-TEL-PROGR-1"/>
					<param name="EGUK06CE-TIPO-TELEFONO-1"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-1"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-2"/>
					<param name="EGUK06CE-TEL-PROGR-2"/>
					<param name="EGUK06CE-TIPO-TELEFONO-2"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-2"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-3"/>
					<param name="EGUK06CE-TEL-PROGR-3"/>
					<param name="EGUK06CE-TIPO-TELEFONO-3"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-3"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-4"/>
					<param name="EGUK06CE-TEL-PROGR-4"/>
					<param name="EGUK06CE-TIPO-TELEFONO-4"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-4"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-5"/>
					<param name="EGUK06CE-TEL-PROGR-5"/>
					<param name="EGUK06CE-TIPO-TELEFONO-5"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-5"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-6"/>
					<param name="EGUK06CE-TEL-PROGR-6"/>
					<param name="EGUK06CE-TIPO-TELEFONO-6"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-6"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-7"/>
					<param name="EGUK06CE-TEL-PROGR-7"/>
					<param name="EGUK06CE-TIPO-TELEFONO-7"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-7"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-8"/>
					<param name="EGUK06CE-TEL-PROGR-8"/>
					<param name="EGUK06CE-TIPO-TELEFONO-8"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-8"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-9"/>
					<param name="EGUK06CE-TEL-PROGR-9"/>
					<param name="EGUK06CE-TIPO-TELEFONO-9"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-9"/>
					<param name="EGUK06CE-TEL-OPERAZIONE-10"/>
					<param name="EGUK06CE-TEL-PROGR-10"/>
					<param name="EGUK06CE-TIPO-TELEFONO-10"/>
					<param name="EGUK06CE-NUMERO-TELEFONO-10"/>
					<param name="EGUK06CE-CONSENSO-1"/>
					<param name="EGUK06CE-DT-CONSENSO-1"/>
					<param name="EGUK06CE-CONSENSO-2"/>
					<param name="EGUK06CE-DT-CONSENSO-2"/>
					<param name="EGUK06CE-CONSENSO-3"/>
					<param name="EGUK06CE-DT-CONSENSO-3"/>
					<param name="EGUK06CE-CONSENSO-4"/>
					<param name="EGUK06CE-DT-CONSENSO-4"/>
					<param name="EGUK06CE-CONSENSO-5"/>
					<param name="EGUK06CE-DT-CONSENSO-5"/>
					<param name="EGUK06CE-CONSENSO-6"/>
					<param name="EGUK06CE-DT-CONSENSO-6"/>
					<param name="EGUK06CE-INVIO-INFORMATIVA"/>
					<param name="EGUK06CE-ESENZ-FISC-DATA-EM"/>
					<param name="EGUK06CE-ESENZ-FISC-DATA-SCAD"/>
					<param name="EGUK06CE-ESENZ-FISC-TIPO"/>
					<param name="EGUK06CE-OPERATORE"/>
					<param name="EGUK06CO-OPERAZIONE-1"/>
					<param name="EGUK06CO-NDG-COLLEGATO-1"/>
					<param name="EGUK06CO-COD-COLLEG-1"/>
					<param name="EGUK06CO-OPERAZIONE-2"/>
					<param name="EGUK06CO-NDG-COLLEGATO-2"/>
					<param name="EGUK06CO-COD-COLLEG-2"/>
					<param name="EGUK06CO-OPERAZIONE-3"/>
					<param name="EGUK06CO-NDG-COLLEGATO-3"/>
					<param name="EGUK06CO-COD-COLLEG-3"/>
					<param name="EGUK06CO-OPERAZIONE-4"/>
					<param name="EGUK06CO-NDG-COLLEGATO-4"/>
					<param name="EGUK06CO-COD-COLLEG-4"/>
					<param name="EGUK06CO-OPERAZIONE-5"/>
					<param name="EGUK06CO-NDG-COLLEGATO-5"/>
					<param name="EGUK06CO-COD-COLLEG-5"/>
					<param name="EGUK06CO-OPERAZIONE-6"/>
					<param name="EGUK06CO-NDG-COLLEGATO-6"/>
					<param name="EGUK06CO-COD-COLLEG-6"/>
					<param name="EGUK06CO-OPERAZIONE-7"/>
					<param name="EGUK06CO-NDG-COLLEGATO-7"/>
					<param name="EGUK06CO-COD-COLLEG-7"/>
					<param name="EGUK06CO-OPERAZIONE-8"/>
					<param name="EGUK06CO-NDG-COLLEGATO-8"/>
					<param name="EGUK06CO-COD-COLLEG-8"/>
					<param name="EGUK06CO-OPERAZIONE-9"/>
					<param name="EGUK06CO-NDG-COLLEGATO-9"/>
					<param name="EGUK06CO-COD-COLLEG-9"/>
					<param name="EGUK06CO-OPERAZIONE-10"/>
					<param name="EGUK06CO-NDG-COLLEGATO-10"/>
					<param name="EGUK06CO-COD-COLLEG-10"/>
					<param name="EGUK06CO-OPERAZIONE-11"/>
					<param name="EGUK06CO-NDG-COLLEGATO-11"/>
					<param name="EGUK06CO-COD-COLLEG-11"/>
					<param name="EGUK06CO-OPERAZIONE-12"/>
					<param name="EGUK06CO-NDG-COLLEGATO-12"/>
					<param name="EGUK06CO-COD-COLLEG-12"/>
					<param name="EGUK06CO-OPERAZIONE-13"/>
					<param name="EGUK06CO-NDG-COLLEGATO-13"/>
					<param name="EGUK06CO-COD-COLLEG-13"/>
					<param name="EGUK06CO-OPERAZIONE-14"/>
					<param name="EGUK06CO-NDG-COLLEGATO-14"/>
					<param name="EGUK06CO-COD-COLLEG-14"/>
					<param name="EGUK06CO-OPERAZIONE-15"/>
					<param name="EGUK06CO-NDG-COLLEGATO-15"/>
					<param name="EGUK06CO-COD-COLLEG-15"/>
					<param name="EGUK06CO-OPERAZIONE-16"/>
					<param name="EGUK06CO-NDG-COLLEGATO-16"/>
					<param name="EGUK06CO-COD-COLLEG-16"/>
					<param name="EGUK06CO-OPERAZIONE-17"/>
					<param name="EGUK06CO-NDG-COLLEGATO-17"/>
					<param name="EGUK06CO-COD-COLLEG-17"/>
					<param name="EGUK06CO-OPERAZIONE-18"/>
					<param name="EGUK06CO-NDG-COLLEGATO-18"/>
					<param name="EGUK06CO-COD-COLLEG-18"/>
					<param name="EGUK06CO-OPERAZIONE-19"/>
					<param name="EGUK06CO-NDG-COLLEGATO-19"/>
					<param name="EGUK06CO-COD-COLLEG-19"/>
				</input>
				<output>
					<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request">
						<xsl:output method="xml"/>
						<xsl:template match="/">
							<EGUKA006>
								<ROCH-HEADER>
									<ROCH-STRUCID>ROCH</ROCH-STRUCID>
									<ROCH-VERSION>0002</ROCH-VERSION>
									<ROCH-BSNAME>RBS-XX-EGU-CENSIMENTO-ANAG</ROCH-BSNAME>
									<ROCH-RETURNCODE>0000</ROCH-RETURNCODE>
									<ROCH-UOWCONTROL>0000</ROCH-UOWCONTROL>
									<ROCH-ABEND-CODE>0000</ROCH-ABEND-CODE>
									<ROCH-AREA-FREE/>
								</ROCH-HEADER>
								<EGUK06-SYS>
									<EGUK06-SYS-CO-TERMINALE/>
									<EGUK06-SYS-CO-OPERATORE/>
									<EGUK06-SYS-CO-FIL-OPER/>
									<EGUK06-SYS-CO-BANCA>00001</EGUK06-SYS-CO-BANCA>
									<EGUK06-SYS-FIL/>
									<EGUK06-SYS-CO-APPL>EG</EGUK06-SYS-CO-APPL>
									<EGUK06-SYS-SERVIZIO>AFA-CENSIMENTO-CO</EGUK06-SYS-SERVIZIO>
									</EGUK06-SYS>
								<EGUK06-ERR>
									<EGUK06-ERR-RC>S</EGUK06-ERR-RC>
									<EGUK06-ERR-RC-DESC>0000</EGUK06-ERR-RC-DESC>
									<EGUK06-ERR-RC-DESC-ERRORE/>
									<EGUK06-ERR-CODICE-ABEND/>
									<EGUK06-ERR-SQLCODE>000000000</EGUK06-ERR-SQLCODE>
									<EGUK06-ERR-SQLERRMC/>
									<EGUK06-ERR-LABEL/>
									<EGUK06-ERR-TABEL/>
									<EGUK06-ERR-FUNZIONE/>
									<EGUK06-ERR-PGM/>
									<EGUK06-ERR-CAMPI/>
								</EGUK06-ERR>
								<EGUK06-SYS-LU-DATI>05617</EGUK06-SYS-LU-DATI>
								<EGUK06-DATI>
									<EGUK06-INPUT>
										<EGUK06-AREACENS>
											<EGUK06CE-STATUS-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-STATUS-AGG']/text()"/>
											</EGUK06CE-STATUS-AGG>
											<EGUK06CE-TELEFONI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TELEFONI-AGG']/text()"/>
											</EGUK06CE-TELEFONI-AGG>
											<EGUK06CE-INDIR-CORR-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-INDIR-CORR-AGG']/text()"/>
											</EGUK06CE-INDIR-CORR-AGG>
											<EGUK06CE-ALTRI-DATI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UEGUK06CE-ALTRI-DATI-AGG']/text()"/>
											</EGUK06CE-ALTRI-DATI-AGG>
											<EGUK06CE-CODICI-VARI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CODICI-VARI-AGG']/text()"/>
											</EGUK06CE-CODICI-VARI-AGG>
											<EGUK06CE-SEDE-FISCALE-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-SEDE-FISCALE-AGG']/text()"/>
											</EGUK06CE-SEDE-FISCALE-AGG>
											<EGUK06CE-DATI-PRIVACY-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DATI-PRIVACY-AGG']/text()"/>
											</EGUK06CE-DATI-PRIVACY-AGG>
											<EGUK06CE-DATI-GENERALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DATI-GENERALI-AGG']/text()"/>
											</EGUK06CE-DATI-GENERALI-AGG>
											<EGUK06CE-DATI-AZIENDALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DATI-AZIENDALI-AGG']/text()"/>
											</EGUK06CE-DATI-AZIENDALI-AGG>
											<EGUK06CE-PROFESSIONI-ALT-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PROFESSIONI-ALT-AGG']/text()"/>
											</EGUK06CE-PROFESSIONI-ALT-AGG>
											<EGUK06CE-INTESTAZIONE-LONG-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-INTESTAZIONE-LONG-AGG']/text()"/>
											</EGUK06CE-INTESTAZIONE-LONG-AGG>
											<EGUK06CE-CLASSIFICAZIONE-ALT-A>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CLASSIFICAZIONE-ALT-A']/text()"/>
											</EGUK06CE-CLASSIFICAZIONE-ALT-A>
											<EGUK06CE-FUNZIONE>S</EGUK06CE-FUNZIONE>
											<EGUK06CE-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NDG']/text()"/>
											</EGUK06CE-NDG>
											<EGUK06CE-CONTROLLO-INT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CONTROLLO-INT']/text()"/>
											</EGUK06CE-CONTROLLO-INT>
											<EGUK06CE-CODICE-FISCALE>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CODICE-FISCALE']/text()"/>
											</EGUK06CE-CODICE-FISCALE>
											<EGUK06CE-PARTITA-IVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PARTITA-IVA']/text()"/>
											</EGUK06CE-PARTITA-IVA>
											<EGUK06CE-SPORTELLO-CAPOFILA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-SPORTELLO-CAPOFILA']/text()"/>
											</EGUK06CE-SPORTELLO-CAPOFILA>
											<EGUK06CE-INTESTAZIONE-A>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-INTESTAZIONE-A']/text()"/>
											</EGUK06CE-INTESTAZIONE-A>
											<EGUK06CE-INTESTAZIONE-B>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-INTESTAZIONE-B']/text()"/>
											</EGUK06CE-INTESTAZIONE-B>
											<EGUK06CE-TIPO-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-NDG']/text()"/>
											</EGUK06CE-TIPO-NDG>
											<EGUK06CE-SETTORISTA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-SETTORISTA']/text()"/>
											</EGUK06CE-SETTORISTA>
											<EGUK06CE-PSEUDONIMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PSEUDONIMO']/text()"/>
											</EGUK06CE-PSEUDONIMO>
											<EGUK06CE-STATO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-STATO-RES']/text()"/>
											</EGUK06CE-STATO-RES>
											<EGUK06CE-COMUNE-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-COMUNE-RES']/text()"/>
											</EGUK06CE-COMUNE-RES>
											<EGUK06CE-LOCALITA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-LOCALITA-RES']/text()"/>
											</EGUK06CE-LOCALITA-RES>
											<EGUK06CE-PROVINCIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PROVINCIA-RES']/text()"/>
											</EGUK06CE-PROVINCIA-RES>
											<EGUK06CE-CAP-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CAP-RES']/text()"/>
											</EGUK06CE-CAP-RES>
											<EGUK06CE-VIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-VIA-RES']/text()"/>
											</EGUK06CE-VIA-RES>
											<EGUK06CE-PRESSO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PRESSO-RES']/text()"/>
											</EGUK06CE-PRESSO-RES>
											<EGUK06CE-INTESTAZIONE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-INTESTAZIONE-CORR']/text()"/>
											</EGUK06CE-INTESTAZIONE-CORR>
											<EGUK06CE-STATO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-STATO-CORR']/text()"/>
											</EGUK06CE-STATO-CORR>
											<EGUK06CE-COMUNE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-COMUNE-CORR']/text()"/>
											</EGUK06CE-COMUNE-CORR>
											<EGUK06CE-LOCALITA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-LOCALITA-CORR']/text()"/>
											</EGUK06CE-LOCALITA-CORR>
											<EGUK06CE-PROVINCIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PROVINCIA-CORR']/text()"/>
											</EGUK06CE-PROVINCIA-CORR>
											<EGUK06CE-CAP-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CAP-CORR']/text()"/>
											</EGUK06CE-CAP-CORR>
											<EGUK06CE-VIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-VIA-CORR']/text()"/>
											</EGUK06CE-VIA-CORR>
											<EGUK06CE-PRESSO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PRESSO-CORR']/text()"/>
											</EGUK06CE-PRESSO-CORR>
											<EGUK06CE-UFFICIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-UFFICIO-CORR']/text()"/>
											</EGUK06CE-UFFICIO-CORR>
											<EGUK06CE-CASELLARIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CASELLARIO-CORR']/text()"/>
											</EGUK06CE-CASELLARIO-CORR>
											<EGUK06CE-CODICE-FISCALE-ESTERO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CODICE-FISCALE-ESTERO']/text()"/>
											</EGUK06CE-CODICE-FISCALE-ESTERO>
											<EGUK06CE-RAMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-RAMO']/text()"/>
											</EGUK06CE-RAMO>
											<EGUK06CE-SETTORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-SETTORE']/text()"/>
											</EGUK06CE-SETTORE>
											<EGUK06CE-RAMO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-RAMO-ALT']/text()"/>
											</EGUK06CE-RAMO-ALT>
											<EGUK06CE-SETTORE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-SETTORE-ALT']/text()"/>
											</EGUK06CE-SETTORE-ALT>
											<EGUK06CE-COMUNE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-COMUNE-ALT']/text()"/>
											</EGUK06CE-COMUNE-ALT>
											<EGUK06CE-PROVINCIA-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-PROVINCIA-ALT']/text()"/>
											</EGUK06CE-PROVINCIA-ALT>
											<EGUK06CE-STATO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-STATO-ALT']/text()"/>
											</EGUK06CE-STATO-ALT>
											<EGUK06CE-SEGMENTO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-SEGMENTO']/text()"/>
											</EGUK06CE-SEGMENTO>
											<EGUK06CE-SIT-GIURIDICA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-SIT-GIURIDICA']/text()"/>
											</EGUK06CE-SIT-GIURIDICA>
											<EGUK06CE-STATUS>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-STATUS']/text()"/>
											</EGUK06CE-STATUS>
											<EGUK06CE-UBIC-DOC-RIFERIM>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-UBIC-DOC-RIFERIM']/text()"/>
											</EGUK06CE-UBIC-DOC-RIFERIM>
											<EGUK06CE-UBIC-DOC-SPORTELLO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-UBIC-DOC-SPORTELLO']/text()"/>
											</EGUK06CE-UBIC-DOC-SPORTELLO>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-1']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-1']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-1']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-1']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-2']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-2']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-2']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-2']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-3']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-3']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-3']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-3']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONEC-4']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-4']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-4']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-4']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-5']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-5']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-5']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-5']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-6']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-6']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-6']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-6']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-7']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-7']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-7']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-7']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-8']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-8']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-8']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-8']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-9']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-9']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-9']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-9']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-TELEFONI>
												<EGUK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-OPERAZIONE-10']/text()"/>
												</EGUK06CE-TEL-OPERAZIONE>
												<EGUK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TEL-PROGR-10']/text()"/>
												</EGUK06CE-TEL-PROGR>
												<EGUK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-TIPO-TELEFONO-10']/text()"/>
												</EGUK06CE-TIPO-TELEFONO>
												<EGUK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-NUMERO-TELEFONO-10']/text()"/>
												</EGUK06CE-NUMERO-TELEFONO>
											</EGUK06CE-TELEFONI>
											<EGUK06CE-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CONSENSO-1']/text()"/>
											</EGUK06CE-CONSENSO-1>
											<EGUK06CE-DT-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DT-CONSENSO-1']/text()"/>
											</EGUK06CE-DT-CONSENSO-1>
											<EGUK06CE-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CONSENSO-2']/text()"/>
											</EGUK06CE-CONSENSO-2>
											<EGUK06CE-DT-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DT-CONSENSO-2']/text()"/>
											</EGUK06CE-DT-CONSENSO-2>
											<EGUK06CE-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CONSENSO-3']/text()"/>
											</EGUK06CE-CONSENSO-3>
											<EGUK06CE-DT-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DT-CONSENSO-3']/text()"/>
											</EGUK06CE-DT-CONSENSO-3>
											<EGUK06CE-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CONSENSO-4']/text()"/>
											</EGUK06CE-CONSENSO-4>
											<EGUK06CE-DT-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DT-CONSENSO-4']/text()"/>
											</EGUK06CE-DT-CONSENSO-4>
											<EGUK06CE-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CONSENSO-5']/text()"/>
											</EGUK06CE-CONSENSO-5>
											<EGUK06CE-DT-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DT-CONSENSO-5']/text()"/>
											</EGUK06CE-DT-CONSENSO-5>
											<EGUK06CE-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-CONSENSO-6']/text()"/>
											</EGUK06CE-CONSENSO-6>
											<EGUK06CE-DT-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-DT-CONSENSO-6']/text()"/>
											</EGUK06CE-DT-CONSENSO-6>
											<EGUK06CE-INVIO-INFORMATIVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-INVIO-INFORMATIVA']/text()"/>
											</EGUK06CE-INVIO-INFORMATIVA>
											<EGUK06CE-ESENZ-FISC-DATA-EM>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-ESENZ-FISC-DATA-EM']/text()"/>
											</EGUK06CE-ESENZ-FISC-DATA-EM>
											<EGUK06CE-ESENZ-FISC-DATA-SCAD>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-ESENZ-FISC-DATA-SCAD']/text()"/>
											</EGUK06CE-ESENZ-FISC-DATA-SCAD>
											<EGUK06CE-ESENZ-FISC-TIPO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-ESENZ-FISC-TIPO']/text()"/>
											</EGUK06CE-ESENZ-FISC-TIPO>
											<EGUK06CE-OPERATORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CE-OPERATORE']/text()"/>
											</EGUK06CE-OPERATORE>
											<FILLER1/>
										</EGUK06-AREACENS>
										<EGUK06-AREACOIN>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-1']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-1']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-1']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-2']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-2']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-2']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-3']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-3']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-3']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-4']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-4']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-4']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-5']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-5']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-5']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-6']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-6']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-6']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-7']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-7']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-7']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-8']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-8']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-8']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-9']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-9']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-9']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-10']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-10']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-10']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-10']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-10']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-10']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-11']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-11']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-11']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-12']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-12']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-12']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-13']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-13']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-13']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-14']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-14']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-14']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-15']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-15']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-15']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-16']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-16']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-16']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-17']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-17']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-17']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-18']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-18']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-18']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<EGUK06CO-TABELLA>
												<EGUK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-OPERAZIONE-19']/text()"/>
												</EGUK06CO-OPERAZIONE>
												<EGUK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-NDG-COLLEGATO-19']/text()"/>
												</EGUK06CO-NDG-COLLEGATO>
												<EGUK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK06CO-COD-COLLEG-19']/text()"/>
												</EGUK06CO-COD-COLLEG>
											</EGUK06CO-TABELLA>
											<FILLER1/>
										</EGUK06-AREACOIN>
									</EGUK06-INPUT>
									<EGUK06-OUTPUT>
										<EGUK06OU-NDG/>
										<EGUK06OU-CONTROLLO-INT>S</EGUK06OU-CONTROLLO-INT>
										<EGUK06OU-CODICE-FISCALE/>
										<EGUK06OU-PARTITA-IVA/>
										<EGUK06OU-SPORTELLO-CAPOFILA/>
										<EGUK06OU-INTESTAZIONE-A/>
										<EGUK06OU-INTESTAZIONE-B/>
										<EGUK06OU-TIPO-NDG/>
										<EGUK06OU-SETTORISTA/>
										<EGUK06OU-PSEUDONIMO/>
										<EGUK06OU-STATO-RES/>
										<EGUK06OU-DESCR-COM-RES/>
										<EGUK06OU-CAB-RES>00000</EGUK06OU-CAB-RES>
										<EGUK06OU-LOCALITA-RES/>
										<EGUK06OU-PROVINCIA-RES/>
										<EGUK06OU-CAP-RES>00000</EGUK06OU-CAP-RES>
										<EGUK06OU-VIA-RES/>
										<EGUK06OU-PRESSO/>
										<EGUK06OU-SETTORE>000</EGUK06OU-SETTORE>
										<EGUK06OU-RAMO>000</EGUK06OU-RAMO>
										<EGUK06OU-PROFESSIONE/>
										<EGUK06OU-INTESTAZ-CORR/>
										<EGUK06OU-STATO-CORR/>
										<EGUK06OU-DESCR-COM-CORR/>
										<EGUK06OU-LOCALITA-CORR/>
										<EGUK06OU-PROVINCIA-CORR/>
										<EGUK06OU-CAP-CORR>00000</EGUK06OU-CAP-CORR>
										<EGUK06OU-VIA-CORR/>
										<EGUK06OU-PRESSO-CORR/>
										<EGUK06OU-UFFICIO-CORR/>
										<EGUK06OU-CASELLARIO-CORR/>
										<EGUK06OU-STATUS-TAB>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
											<EGUK06OU-STATUS>S</EGUK06OU-STATUS>
										</EGUK06OU-STATUS-TAB>
										<EGUK06OU-COMPLETEZZA-DATI>S</EGUK06OU-COMPLETEZZA-DATI>
										<EGUK06OU-STATO-NDG>S</EGUK06OU-STATO-NDG>
										<EGUK06OU-DATA-CENSIMENTO/>
										<EGUK06OU-DATA-VA/>
										<EGUK06OU-SPORTELLO-ESEC/>
										<EGUK06OU-OPERATORE/>
										<EGUK06OU-INTESTAZIONE-AGG/>
										<EGUK06OU-COD-CR>0000000000000</EGUK06OU-COD-CR>
										<EGUK06OU-PREF-COGN-ACQUIS/>
										<EGUK06OU-COGNOME-ACQUISITO/>
										<EGUK06OU-TITOLI/>
										<EGUK06OU-SESSO>S</EGUK06OU-SESSO>
										<EGUK06OU-DATA-NASCITA/>
										<EGUK06OU-COMUNE-NASCITA/>
										<EGUK06OU-PROVINCIA-NASC/>
										<EGUK06OU-DATA-COSTITUZI/>
										<EGUK06OU-NUMERO-CCIAA>00000000</EGUK06OU-NUMERO-CCIAA>
										<EGUK06OU-PROV-CCIAA/>
										<EGUK06OU-DATA-ISCR-CCIA/>
										<EGUK06OU-NUMERO-AIA>00000000</EGUK06OU-NUMERO-AIA>
										<EGUK06OU-PROV-AIA/>
										<EGUK06OU-CODICE-ABI>00000</EGUK06OU-CODICE-ABI>
										<EGUK06OU-COD-CONTROLLO>0</EGUK06OU-COD-CONTROLLO>
										<EGUK06OU-MINCOMES/>
										<EGUK06OU-NUM-REG-TRIB>000000000</EGUK06OU-NUM-REG-TRIB>
										<EGUK06OU-SEDE-TRIBUNALE/>
										<EGUK06OU-CODICE-SWIFT/>
										<EGUK06OU-COD-OPERAT-EST/>
										<EGUK06OU-STATO-SEDE-F/>
										<EGUK06OU-LOCALITA-SEDE-F/>
										<EGUK06OU-DESCR-COM-SEDE-F/>
										<EGUK06OU-PROVINCIA-SEDE-F/>
										<EGUK06OU-CAP-SEDE-F>00000</EGUK06OU-CAP-SEDE-F>
										<EGUK06OU-VIA-SEDE-F/>
										<EGUK06OU-PRESSO-SEDE-F/>
										<EGUK06OU-DATA-RIF-ADA/>
										<EGUK06OU-CLASSE-DIMENSION>00</EGUK06OU-CLASSE-DIMENSION>
										<EGUK06OU-FATTURATO>0000000</EGUK06OU-FATTURATO>
										<EGUK06OU-CAPITALE-SOCIALE>0000000</EGUK06OU-CAPITALE-SOCIALE>
										<EGUK06OU-NUMERO-DIPENDENTI>000000</EGUK06OU-NUMERO-DIPENDENTI>
										<EGUK06OU-SETTORE-ALT>000</EGUK06OU-SETTORE-ALT>
										<EGUK06OU-RAMO-ALT>000</EGUK06OU-RAMO-ALT>
										<EGUK06OU-STATO-ALT/>
										<EGUK06OU-COMUNE-ALT/>
										<EGUK06OU-PROVINCIA-ALT/>
										<EGUK06OU-CAB-ALT>000000</EGUK06OU-CAB-ALT>
										<EGUK06OU-CONSENSO-1>S</EGUK06OU-CONSENSO-1>
										<EGUK06OU-DT-CONSENSO-1/>
										<EGUK06OU-CONSENSO-2>S</EGUK06OU-CONSENSO-2>
										<EGUK06OU-DT-CONSENSO-2/>
										<EGUK06OU-CONSENSO-3>S</EGUK06OU-CONSENSO-3>
										<EGUK06OU-DT-CONSENSO-3/>
										<EGUK06OU-CONSENSO-4>S</EGUK06OU-CONSENSO-4>
										<EGUK06OU-DT-CONSENSO-4/>
										<EGUK06OU-CONSENSO-5>S</EGUK06OU-CONSENSO-5>
										<EGUK06OU-DT-CONSENSO-5/>
										<EGUK06OU-CONSENSO-6>S</EGUK06OU-CONSENSO-6>
										<EGUK06OU-DT-CONSENSO-6/>
										<EGUK06OU-INVIO-INFORMATIVA/>
										<EGUK06OU-PROF-ATTIVITA/>
										<EGUK06OU-PROF-ATTIVITA/>
										<EGUK06OU-PROF-ATTIVITA/>
										<EGUK06OU-PROF-ATTIVITA/>
										<EGUK06OU-PROF-ATTIVITA/>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-TELEFONI>
											<EGUK06OU-TEL-PROGR>00</EGUK06OU-TEL-PROGR>
											<EGUK06OU-TIPO-TELEFONO/>
											<EGUK06OU-NUMERO-TELEFONO/>
										</EGUK06OU-TELEFONI>
										<EGUK06OU-DOCUMENTI>
											<EGUK06OU-TIPO-DOCUM/>
											<EGUK06OU-NUM-DOCUM/>
											<EGUK06OU-DATA-RIL-DOCUM/>
											<EGUK06OU-PROV-RIL-DOCUM/>
											<EGUK06OU-COMUNE-RIL-DOCUM/>
											<EGUK06OU-CAB-RIL-DOCUM>000000</EGUK06OU-CAB-RIL-DOCUM>
											<EGUK06OU-STATO-RIL-DOCUM/>
											<EGUK06OU-ENTE-RILAS-DOCUM/>
											<EGUK06OU-DATA-SCA-DOCUM/>
										</EGUK06OU-DOCUMENTI>
										<EGUK06OU-DOCUMENTI>
											<EGUK06OU-TIPO-DOCUM/>
											<EGUK06OU-NUM-DOCUM/>
											<EGUK06OU-DATA-RIL-DOCUM/>
											<EGUK06OU-PROV-RIL-DOCUM/>
											<EGUK06OU-COMUNE-RIL-DOCUM/>
											<EGUK06OU-CAB-RIL-DOCUM>000000</EGUK06OU-CAB-RIL-DOCUM>
											<EGUK06OU-STATO-RIL-DOCUM/>
											<EGUK06OU-ENTE-RILAS-DOCUM/>
											<EGUK06OU-DATA-SCA-DOCUM/>
										</EGUK06OU-DOCUMENTI>
										<EGUK06OU-DOCUMENTI>
											<EGUK06OU-TIPO-DOCUM/>
											<EGUK06OU-NUM-DOCUM/>
											<EGUK06OU-DATA-RIL-DOCUM/>
											<EGUK06OU-PROV-RIL-DOCUM/>
											<EGUK06OU-COMUNE-RIL-DOCUM/>
											<EGUK06OU-CAB-RIL-DOCUM>000000</EGUK06OU-CAB-RIL-DOCUM>
											<EGUK06OU-STATO-RIL-DOCUM/>
											<EGUK06OU-ENTE-RILAS-DOCUM/>
											<EGUK06OU-DATA-SCA-DOCUM/>
										</EGUK06OU-DOCUMENTI>
										<EGUK06OU-DOCUMENTI>
											<EGUK06OU-TIPO-DOCUM/>
											<EGUK06OU-NUM-DOCUM/>
											<EGUK06OU-DATA-RIL-DOCUM/>
											<EGUK06OU-PROV-RIL-DOCUM/>
											<EGUK06OU-COMUNE-RIL-DOCUM/>
											<EGUK06OU-CAB-RIL-DOCUM>000000</EGUK06OU-CAB-RIL-DOCUM>
											<EGUK06OU-STATO-RIL-DOCUM/>
											<EGUK06OU-ENTE-RILAS-DOCUM/>
											<EGUK06OU-DATA-SCA-DOCUM/>
										</EGUK06OU-DOCUMENTI>
										<EGUK06OU-DOCUMENTI>
											<EGUK06OU-TIPO-DOCUM/>
											<EGUK06OU-NUM-DOCUM/>
											<EGUK06OU-DATA-RIL-DOCUM/>
											<EGUK06OU-PROV-RIL-DOCUM/>
											<EGUK06OU-COMUNE-RIL-DOCUM/>
											<EGUK06OU-CAB-RIL-DOCUM>000000</EGUK06OU-CAB-RIL-DOCUM>
											<EGUK06OU-STATO-RIL-DOCUM/>
											<EGUK06OU-ENTE-RILAS-DOCUM/>
											<EGUK06OU-DATA-SCA-DOCUM/>
										</EGUK06OU-DOCUMENTI>
										<EGUK06OU-SEGMENTO/>
										<EGUK06OU-SIT-GIURIDICA/>
										<EGUK06OU-EREDITA>S</EGUK06OU-EREDITA>
										<EGUK06OU-DATA-EREDITA/>
										<EGUK06OU-SEDE-CASAMADRE>000000</EGUK06OU-SEDE-CASAMADRE>
										<EGUK06OU-DATA-ESTINZIONE/>
										<EGUK06OU-SPORTELLO-RIF/>
										<EGUK06OU-RIFERIMENTO/>
										<EGUK06OU-FILLER/>
									</EGUK06-OUTPUT>
								</EGUK06-DATI>
							</EGUKA006>
						</xsl:template>
					</xsl:stylesheet>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.RomaProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass/>
		<channelclass>it.usi.webfactory.channels.RomaBusinnesChannel</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params/>
	</protocol>
	<channel>
		<params>
			<client>Client_EGU_XX</client>
			<service>RBS-XX-EGU-CENSIMENTO-ANAG</service>
			<format>EGUKA006-XML</format>
			<timeout>30000</timeout>
		</params>
	</channel>
</service>
