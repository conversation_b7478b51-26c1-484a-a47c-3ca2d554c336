<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411998988080">
      <package-mapping id="PackageMapping_1411998988080">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411998988080">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411998988080">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411998988080">
         <service-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_BloccoService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411998988080">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_BloccoService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411998988080">
            <port-name>NPR_Fidi_Blocco</port-name>
            <java-port-name>NPR_Fidi_Blocco</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411998988080">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Blocco_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411998988080">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_Blocco_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411998988080">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_BloccoSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411998988080">
            <java-method-name>nprFidi_Blocco</java-method-name>
            <wsdl-operation>nprFidi_Blocco</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988080">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988080">
                  <wsdl-message id="WSDLMessage_1411998988080">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x00</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988081">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988081">
                  <wsdl-message id="WSDLMessage_1411998988081">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x01</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988082">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988082">
                  <wsdl-message id="WSDLMessage_1411998988082">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x02</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988083">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988083">
                  <wsdl-message id="WSDLMessage_1411998988083">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x03</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988084">
               <param-position>4</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988084">
                  <wsdl-message id="WSDLMessage_1411998988084">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x04</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988085">
               <param-position>5</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988085">
                  <wsdl-message id="WSDLMessage_1411998988085">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x05</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988086">
               <param-position>6</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988086">
                  <wsdl-message id="WSDLMessage_1411998988086">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x06</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988087">
               <param-position>7</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988087">
                  <wsdl-message id="WSDLMessage_1411998988087">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x07</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998988088">
               <param-position>8</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998988088">
                  <wsdl-message id="WSDLMessage_1411998988088">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_BloccoRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x08</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411998988080">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411998988089">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>nprFidi_BloccoResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>nprFidi_BloccoReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
