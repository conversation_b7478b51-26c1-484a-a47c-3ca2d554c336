package it.usi.xframe.gwc.pfstruts.forms;

import javax.servlet.http.HttpServletRequest;

import org.apache.struts.action.ActionErrors;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.validator.ValidatorForm;


public class InputForm extends ValidatorForm {
	
	private String serviceName = "";
	private String textInput = "";
	private String sep1 = "" ;
	private String sep2 = "";
	
	public void reset(ActionMapping mapping, HttpServletRequest request) {
		serviceName = "";
		textInput = "";
		sep1 = "" ;
		sep2 = "";
	}

	public ActionErrors validate(ActionMapping mapping, HttpServletRequest request) {
		return null;
	}

	/**
	 * @return
	 */
	public String getTextInput() {
		return textInput;
	}

	/**
	 * @param string
	 */
	public void setTextInput(String string) {
		textInput = string;
	}

	/**
	 * @return
	 */
	public String getSep1() {
		return sep1;
	}

	/**
	 * @return
	 */
	public String getSep2() {
		return sep2;
	}

	/**
	 * @param string
	 */
	public void setSep1(String string) {
		sep1 = string;
	}

	/**
	 * @param string
	 */
	public void setSep2(String string) {
		sep2 = string;
	}

	/**
	 * @return
	 */
	public String getServiceName() {
		return serviceName;
	}

	/**
	 * @param string
	 */
	public void setServiceName(String string) {
		serviceName = string;
	}

}
