package it.usi.xframe.gwc.bfutil.base;


import it.usi.xframe.ifg.bfutil.wpro.UserData;
import it.usi.xframe.gwc.bfutil.base.IBaseConstDefinitions;


public class BaseDataStorage implements IBaseConstDefinitions {
	
	// common data objects
	private UserData userData;
	
	public BaseDataStorage() {
	}
	/**
	 * @return
	 */
	public UserData getUserData() {
		return userData;
	}

	/**
	 * @param data
	 */
	public void setUserData(UserData data) {
		userData = data;
	}

}