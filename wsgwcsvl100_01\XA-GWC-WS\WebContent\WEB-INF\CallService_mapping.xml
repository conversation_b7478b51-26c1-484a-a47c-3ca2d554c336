<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411998472899">
      <package-mapping id="PackageMapping_1411998472915">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411998472915">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411998472915">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411998472915">
         <service-interface>it.usi.xframe.gwc.wsutil.CallServiceService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411998472915">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411998472930">
            <port-name>CallService</port-name>
            <java-port-name>CallService</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411998472930">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallService_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411998472930">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallService_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411998472930">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411998472930">
            <java-method-name>callService</java-method-name>
            <wsdl-operation>callService</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998472930">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998472930">
                  <wsdl-message id="WSDLMessage_1411998472930">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>service</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998472931">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998472931">
                  <wsdl-message id="WSDLMessage_1411998472931">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>params</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998472932">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998472932">
                  <wsdl-message id="WSDLMessage_1411998472932">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep1</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998472933">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998472933">
                  <wsdl-message id="WSDLMessage_1411998472933">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep2</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411998472930">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411998472934">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>callServiceResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>callServiceReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
