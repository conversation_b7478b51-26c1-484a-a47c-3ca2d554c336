/*
 * Created on Apr 7, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.vo;

import it.usi.xframe.utl.bfutil.DataValue;

import java.io.Serializable;

public class Field_Detail extends DataValue implements Serializable {

	private String name = "";
	private String type = "";

	public Field_Detail() {
	}
	/**
	 * @return
	 */
	public String getName() {
		return name;
	}

	/**
	 * @return
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param string
	 */
	public void setName(String string) {
		name = string;
	}

	/**
	 * @param string
	 */
	public void setType(String string) {
		type = string;
	}

}
