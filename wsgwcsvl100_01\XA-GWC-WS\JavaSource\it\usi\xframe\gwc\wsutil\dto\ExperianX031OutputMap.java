package it.usi.xframe.gwc.wsutil.dto;

import java.io.Serializable;

public class ExperianX031OutputMap implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String ndg = "";
	private String statoPratica = "";
	private String tipoOperazione = "";
	private String progFinanziatore = "";
	private String ruolo = "";
	private String dataStipula = "";
	private String dataFineContratto = "";
	private String dataAggiornamento = "";
	private String profInsoUlt12Mesi = "";
	private String numRate = "";
	private String impRateMediaMens = "";
	private String impRateInso = "";
	private String numMaxInso = "";

	public ExperianX031OutputMap() {
	}

	public String getNdg() {
		return ndg;
	}

	public void setNdg(String ndg) {
		this.ndg = ndg;
	}

	public String getStatoPratica() {
		return statoPratica;
	}

	public void setStatoPratica(String statoPratica) {
		this.statoPratica = statoPratica;
	}

	public String getTipoOperazione() {
		return tipoOperazione;
	}

	public void setTipoOperazione(String tipoOperazione) {
		this.tipoOperazione = tipoOperazione;
	}

	public String getProgFinanziatore() {
		return progFinanziatore;
	}

	public void setProgFinanziatore(String progFinanziatore) {
		this.progFinanziatore = progFinanziatore;
	}

	public String getRuolo() {
		return ruolo;
	}

	public void setRuolo(String ruolo) {
		this.ruolo = ruolo;
	}

	public String getDataStipula() {
		return dataStipula;
	}

	public void setDataStipula(String dataStipula) {
		this.dataStipula = dataStipula;
	}

	public String getDataFineContratto() {
		return dataFineContratto;
	}

	public void setDataFineContratto(String dataFineContratto) {
		this.dataFineContratto = dataFineContratto;
	}

	public String getDataAggiornamento() {
		return dataAggiornamento;
	}

	public void setDataAggiornamento(String dataAggiornamento) {
		this.dataAggiornamento = dataAggiornamento;
	}

	public String getProfInsoUlt12Mesi() {
		return profInsoUlt12Mesi;
	}

	public void setProfInsoUlt12Mesi(String profInsoUlt12Mesi) {
		this.profInsoUlt12Mesi = profInsoUlt12Mesi;
	}

	public String getNumRate() {
		return numRate;
	}

	public void setNumRate(String numRate) {
		this.numRate = numRate;
	}

	public String getImpRateMediaMens() {
		return impRateMediaMens;
	}

	public void setImpRateMediaMens(String impRateMediaMens) {
		this.impRateMediaMens = impRateMediaMens;
	}

	public String getImpRateInso() {
		return impRateInso;
	}

	public void setImpRateInso(String impRateInso) {
		this.impRateInso = impRateInso;
	}

	public String getNumMaxInso() {
		return numMaxInso;
	}

	public void setNumMaxInso(String numMaxInso) {
		this.numMaxInso = numMaxInso;
	}
}
