<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="23/03/2004 13.08.30" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="X01"/>
                    <param name="X02"/>
                    <param name="X03"/>
					<param name="X04"/>
					<param name="X05"/>
					<param name="X06"/>
					<param name="X08"/>
					<param name="X09"/>
					<param name="X10"/>
					<param name="X11"/>
					<param name="X12"/>
                    <param name="X97"/>
					<param name="X98"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="G054">
						<param select="TIPO_ARCHIVIO" name="X01"/>
						<param select="DATA_DI_AGGIORNAMENTO_ARCHIVIO" name="X02"/>
						<param select="SNDG_SINGOLO" name="X03"/>
						<param select="FLAG_PRESENZA_S3" name="X04"/>
						<param select="FLAG_PRESENZA_UNICREDIT" name="X05"/>
						<param select="IDPROG_UNICREDIT" name="X06"/>
						<param select="BANCA" name="X07"/>
						<param select="NDG" name="X08"/>
						<param select="INTESTAZIONE_SNDG" name="X09"/>
						<param select="SAE" name="X10"/>
						<param select="RAE" name="X11"/>
						<param select="CODICE_FISCALE" name="X12"/>
						<param select="PARTITA_IVA" name="X13"/>
						<param select="NATURA_GIURIDICA" name="X14"/>
						<param select="SPECIE_GIURIDICA" name="X15"/>
						<param select="SPORTELLO_CAPOFILA" name="X16"/>
						<param select="INDIRIZZO" name="X17"/>
						<param select="LOCALITA" name="X18"/>
						<param select="COMUNE" name="X19"/>
						<param select="CAP" name="X20"/>
						<param select="PROVINCIA" name="X21"/>
						<param select="STATO_NDG" name="X22"/>
						<param select="AFFIDATO_PER_SINGOLO" name="X23"/>
						<param select="SAE_DESCRIZIONE" name="X24"/>
						<param select="RAE_DESCRIZIONE" name="X25"/>
						<param select="NATURA_GIURIDICA_DESCRIZIONE" name="X26"/>
						<param select="AMBIENTE_ANAGRAFE" name="X27"/>
						<param select="DAZEN_SINGOLO" name="X28"/>
						<param select="DESCRIZIONE_DAZEN_SINGOLO" name="X29"/>
						<param select="UNITA_CAPOFILA" name="X30"/>
						<param select="DESCRIZIONE_UNITA_CAPOFILA" name="X31"/>	
						<param select="AZIENDA_CAPOFILA" name="X32"/>
						<param select="DESCRIZIONE_SPECIE_GIURIDICA" name="X33"/>
						<param select="CREDIT_SEGMENT" name="X34"/>
						<param select="DATA_ULTIMA_VARIAZIONE" name="X35"/>
						<param select="DESCRIZIONE_AZIENDA_CAPOFILA" name="X36"/>
						<param select="CODICE_CCIAA" name="X37"/>
						<param select="CODICE_CR" name="X38"/>
						<param select="CODICE_CRA" name="X39"/>
						<param select="A_DISPOSIZIONE" name="X40"/>
						<param select="SNDG_GRUPPO_ECONOMICO" name="X41"/>
						<param select="INTESTAZIONE_GRUPPO_ECONOMICO" name="X42"/>
						<param select="NUM_COMPONENTI_GRUPPO" name="X43"/>
						<param select="AMBIENTE_GRUPPO_ECONOMICO" name="X44"/>
						<param select="REFERENTE" name="X45"/>
						<param select="GRUPPO_CERTIFICATO" name="X46"/>
						<param select="DAZEN_GRUPPO_ECONOMICO" name="X47"/>
						<param select="DATA_CERTIFICAZIONE" name="X48"/>
						<param select="SNDG_CAPOGRUPPO" name="X49"/>
						<param select="INTESTAZIONE_REFERENTE" name="X50"/>
						<param select="DESCRIZIONE_DAZEN_GRUPPO" name="X51"/>
						<param select="INTESTAZIONE_CAPOGRUPPO" name="X52"/>
						<param select="MULTINATIONAL" name="X53"/>
						<param select="DATA_REGISTRAZIONE_GRUPPO" name="X54"/>
						<param select="LEADING_LE" name="X55"/>
						<param select="FLAG_PADRE_FIGLIO" name="X56"/>
						<param select="IDPROG_PADRE" name="X57"/>
						<param select="APPARTENENZA_GAM_ANAGRAFE" name="X58"/>
						<param select="APPARTENENZA_GAM_WCA" name="X59"/>
					</hostService>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>GLXL</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>GL54</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>