package it.usi.xframe.gwc.pfstruts.actions;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class ReadPwd extends Action {

	private Log logger = LogFactory.getLog(this.getClass());


	// Execute Implementation
	public ActionForward execute(
		ActionMapping mapping,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {

		String userid = (String) request.getParameter("userid");
		String password = (String) request.getParameter("attributo");

		Enumeration en = request.getAttributeNames();
		while (en.hasMoreElements()) {
			logger.info("request element = [" + en.nextElement() + "]");
		}

		logger.info("executing " + this.getClass() + " UserId   = [" + userid + "]");
		logger.info("executing " + this.getClass() + " Password = [" + password + "]");

		request.setAttribute("param1", userid);
		request.setAttribute("param2", password);

		return mapping.findForward("ok");
	}
	
}
