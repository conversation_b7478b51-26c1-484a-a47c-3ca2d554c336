/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_Fidi_Logon {
	
	//	Default Constructor - RSA8 migration prerequisites	
	public NPR_Fidi_Logon(){
			
	}	

	public String nprFidi_Logon(String x00, String x01, String x02) throws Exception { 

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprFidi_Logon(x00, x01, x02);
	}     
}
