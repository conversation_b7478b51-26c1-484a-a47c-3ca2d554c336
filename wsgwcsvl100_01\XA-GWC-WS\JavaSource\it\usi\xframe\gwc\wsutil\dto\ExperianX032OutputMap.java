package it.usi.xframe.gwc.wsutil.dto;

import java.io.Serializable;

public class ExperianX032OutputMap implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String nome = "";
	private String localita = "";
	private String via = "";
	private String provincia = "";
	private String dataProtesto = "";
	private String valProt = "";
	private String tipoEffetto = "";
	private String indicProt = "";

	public ExperianX032OutputMap() {
	}

	public String getNome() {
		return nome;
	}

	public void setNome(String nome) {
		this.nome = nome;
	}

	public String getLocalita() {
		return localita;
	}

	public void setLocalita(String localita) {
		this.localita = localita;
	}

	public String getVia() {
		return via;
	}

	public void setVia(String via) {
		this.via = via;
	}

	public String getProvincia() {
		return provincia;
	}

	public void setProvincia(String provincia) {
		this.provincia = provincia;
	}

	public String getDataProtesto() {
		return dataProtesto;
	}

	public void setDataProtesto(String dataProtesto) {
		this.dataProtesto = dataProtesto;
	}

	public String getValProt() {
		return valProt;
	}

	public void setValProt(String valProt) {
		this.valProt = valProt;
	}

	public String getTipoEffetto() {
		return tipoEffetto;
	}

	public void setTipoEffetto(String tipoEffetto) {
		this.tipoEffetto = tipoEffetto;
	}

	public String getIndicProt() {
		return indicProt;
	}

	public void setIndicProt(String indicProt) {
		this.indicProt = indicProt;
	}
}
