<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="23/03/2004 13.08.30" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="X01"/>
                    <param name="X02"/>
                    <param name="X03"/>
					<param name="X04"/>
					<param name="X05"/>
					<param name="X06"/>
					<param name="X08"/>
					<param name="X09"/>
					<param name="X10"/>
					<param name="X11"/>
					<param name="X12"/>
					<param name="X13"/>
					<param name="X14"/>
					<param name="X15"/>
					<param name="X16"/>
					<param name="X17"/>
					<param name="X19"/>
					<param name="X21"/>
                    <param name="X97"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="G131">
						<param select="BIGLIETTO_CORRETTO" name="X01"/>
						<param select="ULTIMO_BIGLIETTO_REGISTRATO" name="X02"/>
						<param select="NUMERO_BIGLIETTI_REGISTRATI" name="001"/>
						<param select="STATO_ULTIMO_BIGLIETTO" name="X03"/>
						<param select="DESCRIZIONE_STATO_ULTIMO_BIGLIETTO" name="X04"/>
						<param select="CHIAVE_ESTERNA" name="X05"/>
						<param select="FLAG_CHIAVE_ESTERNA" name="X06"/>
						<param select="BANCA_CHIAVE_ESTERNA" name="X07"/>
						<param select="AMBIENTE_RIFERIMENTO" name="X08"/>
					</hostService>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>GLH0</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>GLD1</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>