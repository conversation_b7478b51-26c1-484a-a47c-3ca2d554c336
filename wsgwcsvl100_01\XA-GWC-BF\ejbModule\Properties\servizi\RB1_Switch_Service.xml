<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R50I-PROPOSAL-ID"/>
					<param name="R50I-FUNCTION"/>
					<param name="R50I-USER-ID"/>
					<param name="R50I-DT-BALANCE-SHEET"/>
					<param name="R50I-BALANCE-SHEET-TYPE"/>
					<param name="R50I-COMPANION-CODE"/>
					<param name="R50I-SNDG"/>
					<param name="R50I-LLE"/>
					<param name="R50I-CUSTOMER-ID"/>
					<param name="R50I-COD-COUNTRY"/>
					<param name="R50I-SERVER"/>
					<param name="R50I-DATA-ASS-FROM"/>
					<param name="R50I-DATA-ASS-TO"/>
					<param name="R50I-DATA-VAL-FROM"/>
					<param name="R50I-DATA-VAL-TO"/>
					<param name="R50I-FLAG-VALID-RAT"/>
					<param name="R50I-PROPOSAL-AUTHOR"/>
					<param name="R50I-PROPOSAL-CERTIFIER"/>
					<param name="R50I-PROPOSAL-OVERRIDE"/>
					<param name="R50I-TS-SAVE"/>
					<param name="R50I-SEGM-USER"/>
					<param name="R50I-SEGM-MOT-A"/>
					<param name="R50I-SEGM-MOT-M"/>					
				</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB50-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB50</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
