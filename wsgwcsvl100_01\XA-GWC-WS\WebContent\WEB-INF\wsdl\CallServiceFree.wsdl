<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="callServiceFree">
    <complexType>
     <sequence>
      <element name="service" nillable="true" type="xsd:string"/>
      <element name="params" nillable="true" type="xsd:string"/>
      <element name="sep1" nillable="true" type="xsd:string"/>
      <element name="sep2" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="callServiceFreeResponse">
    <complexType>
     <sequence>
      <element name="callServiceFreeReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="callServiceFreeRequest">

      <wsdl:part element="impl:callServiceFree" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="callServiceFreeResponse">

      <wsdl:part element="impl:callServiceFreeResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="CallServiceFree_SEI">

      <wsdl:operation name="callServiceFree">

         <wsdl:input message="impl:callServiceFreeRequest" name="callServiceFreeRequest"/>

         <wsdl:output message="impl:callServiceFreeResponse" name="callServiceFreeResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="CallServiceFreeSoapBinding" type="impl:CallServiceFree_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="callServiceFree">

         <wsdlsoap:operation soapAction="callServiceFree"/>

         <wsdl:input name="callServiceFreeRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="callServiceFreeResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="CallServiceFreeService">

      <wsdl:port binding="impl:CallServiceFreeSoapBinding" name="CallServiceFree">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/CallServiceFree"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
