<?xml version="1.0"?>
<!-- Generated utente="Monacò Girolamo" Timestamp="04/12/2015 10.59.00" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="X01"/>
                    <param name="X02"/>
                    <param name="X03"/>
					<param name="X04"/>
					<param name="X05"/>
                    <param name="X06"/>
                    <param name="X07"/>
					<param name="X08"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
					<hostService id="1" name="G164">
						<param select="DESCRIZIONE_DELLA_RIGA_DA_VISUALIZZARE" name="X01"/>
						<param select="FLAG_LIVELLO_DI_INDENTAZIONE" name="X02"/>
						<param select="IMPORTO_RISCHI" name="001"/>
						<param select="IMPORTO_FIDO" name="002"/>
						<param select="TIPO_DATO" name="X03"/>
						<param select="DATA_AGGIORNAMENTO_IMPORTI_RISCHIO" name="X04"/>
						<param select="COORDINATA_DI_RISCHIO" name="X90"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONI_SU_DAZEN_E_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64H">
						<param select="CODICE_HUB" name="X80"/>
						<param select="CODICE_ABI" name="X80"/>
						<param select="DESCRIZIONE_DELLA_RIGA_DA_VISUALIZZARE" name="X02"/>
						<param select="FLAG_LIVELLO_DI_INDENTAZIONE" name="X03"/>
						<param select="IMPORTO_UTILIZZATO" name="001"/>
						<param select="IMPORTO_ACCORDATO" name="002"/>
						<param select="TIPO_DATO" name="X04"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X05"/>
						<param select="COORDINATA_DI_RISCHIO" name="X90"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANC/BDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64X">
						<param select="CODICE_HUB" name="X80"/>
						<param select="DESCRIZIONE_DELLA_RIGA_DA_VISUALIZZARE" name="X01"/>
						<param select="FLAG_LIVELLO_DI_INDENTAZIONE" name="X02"/>
						<param select="IMPORTO UTILIZZATO" name="001"/>
						<param select="IMPORTO ACCORDATO" name="002"/>
						<param select="TIPO DATO" name="X03"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X04"/>
						<param select="COORDINATA_DI_RISCHIO" name="X90"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64I">
						<param select="CODICE_ABI" name="X01"/>
						<param select="NDG" name="X03"/>
						<param select="DESCRIZIONE_DELLA_RIGA_DA_VISUALIZZARE" name="X04"/>
						<param select="FLAG_LIVELLO_DI_INDENTAZIONE" name="X05"/>
						<param select="IMPORTO_UTILIZZATO" name="001"/>
						<param select="IMPORTO ACCORDATO" name="002"/>
						<param select="TIPO_DATO" name="X06"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X07"/>
						<param select="COORDINATA_DI_RISCHIO" name="X90"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64J">
						<param select="ID_COMPONENTE" name="X01"/>
						<param select="DESCRIZIONE_DELLA_RIGA_DA_VISUALIZZARE" name="X02"/>
						<param select="FLAG_LIVELLO_DI_INDENTAZIONE" name="X03"/>
						<param select="IMPORTO_UTILIZZATO" name="001"/>
						<param select="IMPORTO ACCORDATO" name="002"/>
						<param select="TIPO_DATO" name="X04"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X05"/>
						<param select="COORDINATA_DI_RISCHIO" name="X90"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64K">
						<param select="CODICE_HUB_O_SUBGROUP" name="X80"/>
						<param select="ID_COMPONENTE" name="X01"/>
						<param select="DESCRIZIONE_DELLA_RIGA_DA_VISUALIZZARE" name="X02"/>
						<param select="FLAG_LIVELLO_DI_INDENTAZIONE" name="X03"/>
						<param select="IMPORTO_UTILIZZATO" name="001"/>
						<param select="IMPORTO ACCORDATO" name="002"/>
						<param select="TIPO_DATO" name="X04"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X05"/>
						<param select="COORDINATA_DI_RISCHIO" name="X90"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G14L">
						<param select="ID_FIGLIO" name="X01"/>
						<param select="DENOMINAZIONE" name="X02"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_DEBITORIE" name="001"/>
						<param select="IMPORTO_ACCORDATO_POSIZIONI_DEBITORIE" name="002"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X03"/>
						<param select="FLAG_PRESENZA_DATI_DI_SINTETICA" name="X04"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_CREDITORIE" name="003"/>
						<param select="FLAG_FIGLIO_SENZA_BANCA/NDG" name="X05"/>
						<param select="FLAG_FIGLIO_CON_ANAGRAFATURE_ESTERE" name="X06"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
						<param select="LOCALITA_COMPONENTE" name="X07"/>
						<param select="RELAZIONE_RATING" name="X08"/>
					</hostService>
					<hostService id="1" name="G64L">
						<param select="HUB_O_SUBGROUP" name="X80"/>
						<param select="ID_FIGLIO" name="X01"/>
						<param select="DENOMINAZIONE" name="X02"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_DEBITORIE" name="001"/>
						<param select="IMPORTO_ACCORDATO_POSIZIONI_DEBITORIE" name="002"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X03"/>
						<param select="FLAG_PRESENZA_DATI_DI_SINTETICA" name="X04"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_CREDITORIE" name="003"/>
						<param select="FLAG_FIGLIO_SENZA_BANCA/NDG" name="X05"/>
						<param select="FLAG_FIGLIO_CON_ANAGRAFATURE_ESTERE" name="X06"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
						<param select="LOCALITA_COMPONENTE" name="X07"/>
						<param select="RELAZIONE_RATING" name="X08"/>
					</hostService>
					<hostService id="1" name="G14K">
						<param select="ID_FIGLIO" name="X01"/>
						<param select="DENOMINAZIONE" name="X02"/>
						<param select="IMPORTO_UTILIZZATO" name="001"/>
						<param select="IMPORTO_ACCORDATO" name="002"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					
					<hostService id="1" name="G14W">
						<param select="ABI" name="X01"/>
						<param select="BANCA" name="X02"/>
						<param select="NDG" name="X03"/>
						<param select="TOTALE_UTILIZZATO" name="001"/>
						<param select="TOTALE_ACCORDATO" name="002"/>
						<param select="TOTALE" name="004"/>
						<param select="DESCRIZIONE_BANCA" name="X04"/>
					</hostService>
					
					<hostService id="1" name="G14X">
						<param select="ID_FIGLIO" name="X01"/>
						<param select="COD_ABI" name="X02"/>
						<param select="NDG" name="X03"/>
						<param select="IMPORTO_UTILIZZATO" name="001"/>
						<param select="IMPORTO_ACCORDATO" name="002"/>
						<param select="BANCA" name="X04"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64P">
						<param select="HUB_/_SUBGROUP" name="X80"/>
						<param select="ID_FIGLIO" name="X01"/>
						<param select="COD_ABI" name="X02"/>
						<param select="NDG" name="X03"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_DEBITORIE" name="001"/>
						<param select="IMPORTO_ACCORDATO_POSIZIONI_DEBITORIE" name="002"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X04"/>
						<param select="FLAG_PRESENZA_DATI_DI_SINTETICA" name="X05"/>
						<param select="BANCA" name="X06"/>
						<param select="TOTALE_UTILIZZATO_POSIZIONI_CREDITORIE" name="003"/>
						<param select="IMPORTO" name="004"/>
						<param select="DENOMINAZIONE_NDG" name="X07"/>
						<param select="FLAG_FORZATURA" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64O">
						<param select="HUB_/_SUBGROUP" name="X80"/>
						<param select="ABI" name="X01"/>
						<param select="BANCA" name="X02"/>
						<param select="DESCRIZIONE_BANCA" name="X03"/>
						<param select="ID_COMPONENTE" name="X04"/>
						<param select="DENOMINAZIONE" name="X05"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_DEBITORIE" name="001"/>
						<param select="IMPORTO_ACCORDATO_POSIZIONI_DEBITORIE" name="002"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_CREDITORIE" name="003"/>
						<param select="IMPORTO" name="004"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64W">
						<param select="HUB_/_SUBGROUP" name="X80"/>
						<param select="ABI" name="X01"/>
						<param select="BANCA" name="X02"/>
						<param select="DESCRIZIONE_BANCA" name="X03"/>
						<param select="ID_FIGLIO" name="X04"/>
						<param select="NDG" name="X05"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_DEBITORIE" name="001"/>
						<param select="IMPORTO_ACCORDATO_POSIZIONI_DEBITORIE" name="002"/>
						<param select="IMPORTO_UTILIZZATO_POSIZIONI_CREDITORIE" name="003"/>
						<param select="IMPORTO" name="004"/>
						<param select="DENOMINAZIONE_COMPONENTE" name="X06"/>
						<param select="FLAG_FORZATURA_PER_ANOMALIE_SU_BANCA/NDG_ESTERI" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
					</hostService>
					<hostService id="1" name="G64Q">
						<param select="TIPO_AGGREGAZIONE" name="X01"/>
						<param select="DATA_DI_REVIZIONE_CS" name="X02"/>
					</hostService>
					<hostService id="1" name="G64M">
						<param select="NUMERO_BANCHE_TOTALI" name="X01"/>
						<param select="NUMERO_BANCHE_CHE_NON_SUPPORTANO_IL_MOTORE_ONLINE" name="X02"/>
						<param select="NUMERO_BANCHE_CHE_SUPPORTANO_IL_MOTORE_ONLINE" name="X03"/>
						<param select="NUMERO_BANCHE_CHE_HANNO_DATO_ESITO_OK" name="X04"/>
						<param select="NUMERO_BANCHE_CHE_HANNO_DATO_ESITO_KO" name="X05"/>
					</hostService>
					<hostService id="1" name="G64G">
						<param select="HUB_O_SUBGROUP" name="X80"/>
						<param select="DESCRIZIONE_HUB" name="X01"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X02"/>
						<param select="FLAG PRESENZA DATI DI SINTETICA" name="X03"/>
						<param select="TOTALE_UTILIZZATO_POSIZIONI_DEBITORIE" name="001"/>
						<param select="TOTALE_ACCORDATO_POSIZIONI_DEBITORIE" name="002"/>
						<param select="TOTALE_UTILIZZATO_POSIZIONI_CREDITORIE" name="003"/>
						<param select="TOTALE" name="004"/>
						<param select="FLAG_FORZATURA" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
						<param select="PLAFOND" name="005"/>
						<param select="FREE_PLAFOND" name="006"/>
						<param select="SEGNO_FREE_PLAFOND" name="X04"/>
						<param select="FLAG_INDENTAZIONE" name="X05"/>
					</hostService>
					<hostService id="1" name="G64N">
						<param select="HUB_O_SUBGROUP" name="X80"/>
						<param select="ABI" name="X01"/>
						<param select="BANCA" name="X02"/>
						<param select="DESCRIZIONE_BANCA" name="X03"/>
						<param select="DATA_DI_AGGIORNAMENTO" name="X04"/>
						<param select="NDG" name="X05"/>
						<param select="FLAG_PRESENZA_DATI_DI_SINTETICA" name="X06"/>
						<param select="TOTALE_UTILIZZATO_POSIZIONI_DEBITORIE" name="001"/>
						<param select="TOTALE_ACCORDATO_POSIZIONI_DEBITORIE" name="002"/>
						<param select="TOTALE_UTILIZZATO_POSIZIONI_CREDITORIE" name="003"/>
						<param select="DATA_MINIMA" name="X07"/>
						<param select="FLAG_DATA_OBSOLETA" name="X08"/>
						<param select="TOTALE" name="004"/>
						<param select="FLAG FORZATURA" name="X91"/>
						<param select="FLAG_FORZATURA_PER_ABILITAZIONE_SU_DAZEN_E_LEGAL_ENTITY" name="X92"/>
						<param select="DESCRIZIONE BREVE BANCA" name="X09"/>            
					</hostService>
					<hostService id="1" name="G64R">
						<param select="CODICE_HUB" name="X80"/>
						<param select="ABI" name="X01"/>
						<param select="BANCA" name="X02"/>
						<param select="VALORE_DI_MERCATO_DERIVATI" name="001"/>
						<param select="QUANTITA_FIDI_DI_SCONFINO" name="002"/>
						<param select="IMPORTO_FIDI_DI_SCONFINO" name="003"/>
						<param select="SEGNO_VALORE_DI_MERCATO_DEI_DERIVATI" name="X03"/>
						<param select="QUANTITA_FIDI_NON_OPERATIVI" name="004"/>
						<param select="IMPORTO_FIDI_NON_OPERATIVI" name="005"/>
						<param select="QUANTITA_FIDI_SU_CARTE" name="006"/>
						<param select="IMPORTO_FIDI_SU_CARTE" name="007"/>
						<param select="QUANTITA_SOSPESI_DARE" name="008"/>
						<param select="IMPORTO_SOSPESI_DARE" name="009"/>
						<param select="QUANTITA_SOSPESI_AVERE" name="010"/>
						<param select="IMPORTO_SOSPESI_AVERE" name="011"/>        
					</hostService>
					<hostService id="1" name="G64Z">
						<param select="CHIAVE_DI_RIPRESA" name="X01"/>
						<param select="NUMERO_TOTALE_BANCA/NDG" name="001"/>
						<param select="NUMERO_MASSIMO_BANCA/NDG_PER_PAGINA" name="002"/>
						<param select="NUMERO_BANCA/NDG_DELLA_PAGINA_CORRENTE" name="003"/>						     
					</hostService>
					<hostService id="1" name="G64Y">
						<param select="DESCRIZIONE RIGA" name="X01"/>
						<param select="DATA DI AGGIORNAMENTO" name="X02"/>
						<param select="TOTALE UTILIZZATO POSIZIONI DEBITORIE" name="001"/>
						<param select="TOTALE ACCORDATO  POSIZIONI DEBITORIE" name="002"/>
						<param select="TOTALE" name="004"/>
						<param select="TOTALE CREDITORIE" name="003"/>
						<param select="FLAG ABILITA CREDITORIE" name="X03"/>
						<param select="PLAFOND" name="005"/>
						<param select="FREE PLAFOND" name="006"/>
						<param select="SEGNO FREE PLAFOND" name="X04"/>						
					</hostService>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>GLH0</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>GLG4</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>