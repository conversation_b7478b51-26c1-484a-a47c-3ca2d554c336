/*
 * Created on Nov 8, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.pfutil;

import it.usi.xframe.system.pfutil.tools.GenericMessageCacher;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class BaseObjectsCacher extends GenericMessageCacher {
	
	private static BaseObjectsCacher instance = new BaseObjectsCacher();
	
	protected BaseObjectsCacher() {
		super();
	}

	public static BaseObjectsCacher getInstance() {
	   return instance;
	}

	public void clearCache() {
		int minSize = instance.getMinCacheSize();
		instance.setMinCacheSize(0);
		instance.trimToSize();
		instance.setMinCacheSize(minSize);
	}
}