<?xml version="1.0" ?>
<!-- Generated by XMLGEN Versione="2.0.148" utente="zoccoli" Timestamp="07/12/2001 10.03.56" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<dispenser>
		<road>
			<request>
				<input>	
				    <param name="SERVIZIO"/>
					<param name="VERSIONE"/>
					<param name="COD_BAN"/>
					<param name="COD_BAN_SERV"/>
					<param name="BINWGXXX_PACK"/>
					<param name="BINWG183_ESITO"/>
					<param name="BINWG183_DESC_ESITO"/>
					<param name="BINWG183_TIPO_INVIO"/>
					<param name="BINWG183_OPERAZIONE"/>
					<param name="BINWG183_NDG_CONTR"/>
					<param name="BINWG183_COD_OPER"/>
					<param name="BINWG183_SPT_OPER"/>
					<param name="BINWG183_PROD_GEN"/>
					<param name="BINWG183_PROD_BIN_1"/>
					<param name="BINWG183_PROD_BIN_2"/>
					<param name="BINWG183_PROD_BIN_3"/>
					<param name="BINWG183_PROD_BIN_4"/>
					<param name="BINWG183_PACCHETTO"/>
					<param name="BINWG183_RATEAZ"/>
					<param name="BINWG183_TOT_PROD"/>
					<param name="BINWG183_DURM_FIN"/>
					<param name="BINWG183_SPT_FIN"/>
					<param name="BINWG183_NUM_FIN"/>
					<param name="BINWG183_TOT_ASSIC"/>
					<param name="BINWG183_NDG_ASSIC_1"/>
					<param name="BINWG183_NDG_ASSIC_2"/>
					<param name="BINWG183_NDG_ASSIC_3"/>
					<param name="BINWG183_NDG_ASSIC_4"/>
					<param name="BINWG183_NDG_ASSIC_5"/>
					<param name="BINWG183_STATO_ASSIC_1"/>
					<param name="BINWG183_STATO_ASSIC_2"/>
					<param name="BINWG183_STATO_ASSIC_3"/>
					<param name="BINWG183_STATO_ASSIC_4"/>
					<param name="BINWG183_STATO_ASSIC_5"/>
					<param name="BINWG183_IMP_ASSIC_1"/>
					<param name="BINWG183_IMP_ASSIC_2"/>
					<param name="BINWG183_IMP_ASSIC_3"/>
					<param name="BINWG183_IMP_ASSIC_4"/>
					<param name="BINWG183_IMP_ASSIC_5"/>
					<param name="BINWG183_FLAG_PIN_1"/>
					<param name="BINWG183_FLAG_PIN_2"/>
					<param name="BINWG183_FLAG_PIN_3"/>
					<param name="BINWG183_FLAG_PIN_4"/>
					<param name="BINWG183_FLAG_PIN_5"/>
					<param name="BINWG183_SPT_CONTO"/>
					<param name="BINWG183_CONTO"/>
					<param name="BINWG183_TIPO_CONTO"/>
					<param name="BINWG183_PREMIO_1"/>
					<param name="BINWG183_PREMIO_2"/>
					<param name="BINWG183_PREMIO_3"/>
					<param name="BINWG183_PREMIO_4"/>
					<param name="BINWG183_PREMIO_5"/>
					<param name="BINWG183_IMP_FIN"/>
					<param name="BINWG183_NOME_1"/>
					<param name="BINWG183_NOME_2"/>
					<param name="BINWG183_NOME_3"/>
					<param name="BINWG183_NOME_4"/>
					<param name="BINWG183_NOME_5"/>
					<param name="BINWG183_COGNOME_1"/>
					<param name="BINWG183_COGNOME_2"/>
					<param name="BINWG183_COGNOME_3"/>
					<param name="BINWG183_COGNOME_4"/>
					<param name="BINWG183_COGNOME_5"/>
					<param name="BINWG183_DT_NASC_1"/>
					<param name="BINWG183_DT_NASC_2"/>
					<param name="BINWG183_DT_NASC_3"/>
					<param name="BINWG183_DT_NASC_4"/>
					<param name="BINWG183_DT_NASC_5"/>
					<param name="BINWG183_SESSO_1"/>
					<param name="BINWG183_SESSO_2"/>
					<param name="BINWG183_SESSO_3"/>
					<param name="BINWG183_SESSO_4"/>
					<param name="BINWG183_SESSO_5"/>
					<param name="BINWG183_ABI_CC_1"/>
					<param name="BINWG183_ABI_CC_2"/>
					<param name="BINWG183_ABI_CC_3"/>
					<param name="BINWG183_ABI_CC_4"/>
					<param name="BINWG183_ABI_CC_5"/>
					<param name="BINWG183_CAB_CC_1"/>
					<param name="BINWG183_CAB_CC_2"/>
					<param name="BINWG183_CAB_CC_3"/>
					<param name="BINWG183_CAB_CC_4"/>
					<param name="BINWG183_CAB_CC_5"/>
					<param name="BINWG183_SPT_CC_1"/>
					<param name="BINWG183_SPT_CC_2"/>
					<param name="BINWG183_SPT_CC_3"/>
					<param name="BINWG183_SPT_CC_4"/>
					<param name="BINWG183_SPT_CC_5"/>
					<param name="BINWG183_NUM_CC_1"/>
					<param name="BINWG183_NUM_CC_2"/>
					<param name="BINWG183_NUM_CC_3"/>
					<param name="BINWG183_NUM_CC_4"/>
					<param name="BINWG183_NUM_CC_5"/>
					<param name="BINWG183_MOD_REG_1"/>
					<param name="BINWG183_MOD_REG_2"/>
					<param name="BINWG183_MOD_REG_3"/>
					<param name="BINWG183_MOD_REG_4"/>
					<param name="BINWG183_MOD_REG_5"/>
					<param name="BINWG183_FL_SALUTE_1"/>
					<param name="BINWG183_FL_SALUTE_2"/>
					<param name="BINWG183_FL_SALUTE_3"/>
					<param name="BINWG183_FL_SALUTE_4"/>
					<param name="BINWG183_FL_SALUTE_5"/>
					<param name="BINWG183_FL_DEROGA"/>
					<param name="BINWG183_IMP_TOT_POL"/> 
					<param name="BINWG183_CAPIT_1"/>
					<param name="BINWG183_CAPIT_2"/>
					<param name="BINWG183_CAPIT_3"/>
					<param name="BINWG183_CAPIT_4"/>
					<param name="BINWG183_CAPIT_5"/>
					<param name="BINWG183_COD_CONV_1"/>
					<param name="BINWG183_COD_CONV_2"/>
					<param name="BINWG183_COD_CONV_3"/>
					<param name="BINWG183_COD_CONV_4"/>
					<param name="BINWG183_COD_CONV_5"/>
					<param name="BINWG183_COD_PACC"/>
					<param name="BINWG183_DT_DEC_POL"/>
					<param name="BINWG183_DT_DEC_FIN"/>
					<param name="BINWG183_DT_EROG_FIN"/>
					<param name="BINWG183_DT_SCAD_FIN"/>
					<param name="BINWG183_DT_SCAD_POL"/>
					<param name="BINWG183_DT_STIP_FIN"/>
					<param name="BINWG183_DURA_POL"/>
					<param name="BINWG183_DURA_FIN"/>
					<param name="BINWG183_DURM_POL"/>
					<param name="BINWG183_DURM_PREAM"/>
					<param name="BINWG183_ETA_1"/>
					<param name="BINWG183_ETA_2"/>
					<param name="BINWG183_ETA_3"/>
					<param name="BINWG183_ETA_4"/>
					<param name="BINWG183_ETA_5"/>
					<param name="BINWG183_FL_FINANZ"/>
					<param name="BINWG183_FL_TAEG"/>
					<param name="BINWG183_IMP_FIN_LORDO"/>
					<param name="BINWG183_IMP_FIN_NETTO"/>
					<param name="BINWG183_IMP_RATA_1"/>
					<param name="BINWG183_IMP_RATA_2"/>
					<param name="BINWG183_IMP_RATA_3"/>
					<param name="BINWG183_IMP_RATA_4"/>
					<param name="BINWG183_IMP_RATA_5"/>
					<param name="BINWG183_MESI_CAP80"/>
					<param name="BINWG183_OPER_SPT_FIN"/>
					<param name="BINWG183_OPZ_1"/>
					<param name="BINWG183_OPZ_2"/>
					<param name="BINWG183_OPZ_3"/>
					<param name="BINWG183_OPZ_4"/>
					<param name="BINWG183_OPZ_5"/>
					<param name="BINWG183_PERC_LTV"/>
					<param name="BINWG183_RATA_FIN"/>
					<param name="BINWG183_RATEAZ_FIN"/>
					<param name="BINWG183_RETE_FIN"/>
					<param name="BINWG183_RICHIESTA"/>
					<param name="BINWG183_STATO_FIN"/>
					<param name="BINWG183_TAN"/>
					<param name="BINWG183_TIPO_FIN"/>
					<param name="BINWG183_TIPO_INTEST"/>
					<param name="BINWG183_TIPO_TASSO"/>
					<param name="BINWG183_VAL_STIMA"/>
					<param name="BINWG183_CAP_IMM"/>
					<param name="BINWG183_COMUNE_IMM"/>
					<param name="BINWG183_INDIR_IMM"/>
					<param name="BINWG183_PROV_IMM"/>
					<param name="BINWG183_FL_ASS123"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.GaussProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<hostService/>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>01</servBankNumber>
			<version>1</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WPZ0</transaction>
			<timeout>60000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>

