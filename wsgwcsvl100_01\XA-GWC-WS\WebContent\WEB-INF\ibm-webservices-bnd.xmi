<?xml version="1.0" encoding="UTF-8"?>
<com.ibm.etools.webservice.wsbnd:WSBinding xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:com.ibm.etools.webservice.wsbnd="http://www.ibm.com/websphere/appserver/schemas/5.0.2/wsbnd.xmi" xmi:id="WSBinding_1411998472852">
  <wsdescBindings xmi:id="WSDescBinding_1411998472852" wsDescNameLink="CallServiceService">
    <pcBindings xmi:id="PCBinding_1411998472852" pcNameLink="CallService">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412001087598">
        <loginMappings xmi:id="LoginMapping_1412063891692" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412063891692" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039003" wsDescNameLink="CallServiceFreeService">
    <pcBindings xmi:id="PCBinding_1412000039003" pcNameLink="CallServiceFree">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412069664704">
        <loginMappings xmi:id="LoginMapping_1412069664704" authMethod="LTPA" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412069664704" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
          <tokenValueType xmi:id="TokenValueType_1412069664704" uri="http://www.ibm.com/websphere/appserver/tokentype/5.0.2" localName="LTPA"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039004" wsDescNameLink="CallServiceTokenService">
    <pcBindings xmi:id="PCBinding_1412000039004" pcNameLink="CallServiceToken">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412060999928">
        <loginMappings xmi:id="LoginMapping_1412064235115" authMethod="BasicAuth" configName="PGTOKEN">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412064235115" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039005" wsDescNameLink="NPR_AnagraficaService">
    <pcBindings xmi:id="PCBinding_1412000039005" pcNameLink="NPR_Anagrafica">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575850">
        <loginMappings xmi:id="LoginMapping_1412078575850" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575850" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039006" wsDescNameLink="NPR_AnagraficaAce6Service">
    <pcBindings xmi:id="PCBinding_1412000039006" pcNameLink="NPR_AnagraficaAce6">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575851">
        <loginMappings xmi:id="LoginMapping_1412078575851" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575851" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039007" wsDescNameLink="NPR_BigliettoPGAService">
    <pcBindings xmi:id="PCBinding_1412000039007" pcNameLink="NPR_BigliettoPGA">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575852">
        <loginMappings xmi:id="LoginMapping_1412078575852" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575852" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039008" wsDescNameLink="NPR_DatiFidiPraticaService">
    <pcBindings xmi:id="PCBinding_1412000039008" pcNameLink="NPR_DatiFidiPratica">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575853">
        <loginMappings xmi:id="LoginMapping_1412078575853" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575853" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039010" wsDescNameLink="NPR_EsitoPGAService">
    <pcBindings xmi:id="PCBinding_1412000039010" pcNameLink="NPR_EsitoPGA">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575854">
        <loginMappings xmi:id="LoginMapping_1412078575854" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575854" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039011" wsDescNameLink="NPR_Fidi_AggiornaRigaService">
    <pcBindings xmi:id="PCBinding_1412000039011" pcNameLink="NPR_Fidi_AggiornaRiga">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575855">
        <loginMappings xmi:id="LoginMapping_1412078575855" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575855" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039012" wsDescNameLink="NPR_Fidi_AnnullamentoPropostaService">
    <pcBindings xmi:id="PCBinding_1412000039012" pcNameLink="NPR_Fidi_AnnullamentoProposta">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575856">
        <loginMappings xmi:id="LoginMapping_1412078575856" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575856" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039013" wsDescNameLink="NPR_Fidi_Autorizzazione_NegativaService">
    <pcBindings xmi:id="PCBinding_1412000039013" pcNameLink="NPR_Fidi_Autorizzazione_Negativa">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575857">
        <loginMappings xmi:id="LoginMapping_1412078575857" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575857" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039014" wsDescNameLink="NPR_Fidi_AutorizzazioneService">
    <pcBindings xmi:id="PCBinding_1412000039014" pcNameLink="NPR_Fidi_Autorizzazione">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575858">
        <loginMappings xmi:id="LoginMapping_1412078575858" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575858" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039015" wsDescNameLink="NPR_Fidi_AvocaturaService">
    <pcBindings xmi:id="PCBinding_1412000039015" pcNameLink="NPR_Fidi_Avocatura">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575859">
        <loginMappings xmi:id="LoginMapping_1412078575859" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575859" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039016" wsDescNameLink="NPR_Fidi_BloccoService">
    <pcBindings xmi:id="PCBinding_1412000039016" pcNameLink="NPR_Fidi_Blocco">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575860">
        <loginMappings xmi:id="LoginMapping_1412078575860" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575860" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039017" wsDescNameLink="NPR_Fidi_CancellaService">
    <pcBindings xmi:id="PCBinding_1412000039017" pcNameLink="NPR_Fidi_Cancella">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575861">
        <loginMappings xmi:id="LoginMapping_1412078575861" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575861" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039018" wsDescNameLink="NPR_Fidi_CompletamentoService">
    <pcBindings xmi:id="PCBinding_1412000039018" pcNameLink="NPR_Fidi_Completamento">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575862">
        <loginMappings xmi:id="LoginMapping_1412078575862" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575862" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039019" wsDescNameLink="NPR_Fidi_DeclinoService">
    <pcBindings xmi:id="PCBinding_1412000039019" pcNameLink="NPR_Fidi_Declino">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575863">
        <loginMappings xmi:id="LoginMapping_1412078575863" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575863" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039020" wsDescNameLink="NPR_Fidi_DeliberaService">
    <pcBindings xmi:id="PCBinding_1412000039020" pcNameLink="NPR_Fidi_Delibera">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575864">
        <loginMappings xmi:id="LoginMapping_1412078575864" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575864" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039021" wsDescNameLink="NPR_Fidi_Inquiry_DatoVarioService">
    <pcBindings xmi:id="PCBinding_1412000039021" pcNameLink="NPR_Fidi_Inquiry_DatoVario">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575865">
        <loginMappings xmi:id="LoginMapping_1412078575865" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575865" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039022" wsDescNameLink="NPR_Fidi_IterPropostaService">
    <pcBindings xmi:id="PCBinding_1412000039022" pcNameLink="NPR_Fidi_IterProposta">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575866">
        <loginMappings xmi:id="LoginMapping_1412078575866" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575866" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039023" wsDescNameLink="NPR_Fidi_LogonService">
    <pcBindings xmi:id="PCBinding_1412000039023" pcNameLink="NPR_Fidi_Logon">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575867">
        <loginMappings xmi:id="LoginMapping_1412078575867" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575867" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039024" wsDescNameLink="NPR_Fidi_PenninoService">
    <pcBindings xmi:id="PCBinding_1412000039024" pcNameLink="NPR_Fidi_Pennino">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575868">
        <loginMappings xmi:id="LoginMapping_1412078575868" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575868" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039025" wsDescNameLink="NPR_Fidi_PerfezionamentoGaranziaService">
    <pcBindings xmi:id="PCBinding_1412000039025" pcNameLink="NPR_Fidi_PerfezionamentoGaranzia">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575869">
        <loginMappings xmi:id="LoginMapping_1412078575869" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575869" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039026" wsDescNameLink="NPR_Fidi_PreCompletamentoService">
    <pcBindings xmi:id="PCBinding_1412000039026" pcNameLink="NPR_Fidi_PreCompletamento">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575870">
        <loginMappings xmi:id="LoginMapping_1412078575870" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575870" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039027" wsDescNameLink="NPR_Fidi_PropostaService">
    <pcBindings xmi:id="PCBinding_1412000039027" pcNameLink="NPR_Fidi_Proposta">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575871">
        <loginMappings xmi:id="LoginMapping_1412078575871" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575871" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039028" wsDescNameLink="NPR_Fidi_RecuperoNdgService">
    <pcBindings xmi:id="PCBinding_1412000039028" pcNameLink="NPR_Fidi_RecuperoNdg">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575872">
        <loginMappings xmi:id="LoginMapping_1412078575872" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575872" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039029" wsDescNameLink="NPR_FidiIterPropostaNewService">
    <pcBindings xmi:id="PCBinding_1412000039029" pcNameLink="NPR_FidiIterPropostaNew">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575873">
        <loginMappings xmi:id="LoginMapping_1412078575873" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575873" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039030" wsDescNameLink="NPR_Fin_DettaglioService">
    <pcBindings xmi:id="PCBinding_1412000039030" pcNameLink="NPR_Fin_Dettaglio">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575874">
        <loginMappings xmi:id="LoginMapping_1412078575874" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575874" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039031" wsDescNameLink="NPR_Fin_TassoService">
    <pcBindings xmi:id="PCBinding_1412000039031" pcNameLink="NPR_Fin_Tasso">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575875">
        <loginMappings xmi:id="LoginMapping_1412078575875" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575875" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039032" wsDescNameLink="NPR_ListaFidiService">
    <pcBindings xmi:id="PCBinding_1412000039032" pcNameLink="NPR_ListaFidi">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575876">
        <loginMappings xmi:id="LoginMapping_1412078575876" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575876" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039033" wsDescNameLink="NPR_LoginService">
    <pcBindings xmi:id="PCBinding_1412000039033" pcNameLink="NPR_Login">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575877">
        <loginMappings xmi:id="LoginMapping_1412078575877" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575877" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039034" wsDescNameLink="NPR_PfaService">
    <pcBindings xmi:id="PCBinding_1412000039034" pcNameLink="NPR_Pfa">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575878">
        <loginMappings xmi:id="LoginMapping_1412078575878" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575878" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039035" wsDescNameLink="NPR_RatingService">
    <pcBindings xmi:id="PCBinding_1412000039035" pcNameLink="NPR_Rating">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575879">
        <loginMappings xmi:id="LoginMapping_1412078575879" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575879" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039036" wsDescNameLink="NPR_SEM_ZivnoService">
    <pcBindings xmi:id="PCBinding_1412000039036" pcNameLink="NPR_SEM_Zivno">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575880">
        <loginMappings xmi:id="LoginMapping_1412078575880" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575880" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039037" wsDescNameLink="NPR_SinteticaPGAService">
    <pcBindings xmi:id="PCBinding_1412000039037" pcNameLink="NPR_SinteticaPGA">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575881">
        <loginMappings xmi:id="LoginMapping_1412078575881" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575881" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039038" wsDescNameLink="NPR_SinteticaPGASempliceService">
    <pcBindings xmi:id="PCBinding_1412000039038" pcNameLink="NPR_SinteticaPGASemplice">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575882">
        <loginMappings xmi:id="LoginMapping_1412078575882" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575882" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039040" wsDescNameLink="NPX_Fidi_CompletamentoService">
    <pcBindings xmi:id="PCBinding_1412000039040" pcNameLink="NPX_Fidi_Completamento">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575883">
        <loginMappings xmi:id="LoginMapping_1412078575883" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575883" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039041" wsDescNameLink="NPX_Fidi_InserimentoFidiService">
    <pcBindings xmi:id="PCBinding_1412000039041" pcNameLink="NPX_Fidi_InserimentoFidi">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575884">
        <loginMappings xmi:id="LoginMapping_1412078575884" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575884" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039042" wsDescNameLink="NPX_Fidi_LogonService">
    <pcBindings xmi:id="PCBinding_1412000039042" pcNameLink="NPX_Fidi_Logon">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575885">
        <loginMappings xmi:id="LoginMapping_1412078575885" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575885" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412000039043" wsDescNameLink="NPX_Fidi_PreCompletamentoService">
    <pcBindings xmi:id="PCBinding_1412000039043" pcNameLink="NPX_Fidi_PreCompletamento">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575886">
        <loginMappings xmi:id="LoginMapping_1412078575886" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575886" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412077372605" wsDescNameLink="NPX_Fidi_AperturaPropostaService">
    <pcBindings xmi:id="PCBinding_1412077372605" pcNameLink="NPX_Fidi_AperturaProposta">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575887">
        <loginMappings xmi:id="LoginMapping_1412078575887" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575887" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1412077726315" wsDescNameLink="NPR_DossierService">
    <pcBindings xmi:id="PCBinding_1412077726315" pcNameLink="NPR_Dossier">
      <securityRequestReceiverBindingConfig xmi:id="SecurityRequestReceiverBindingConfig_1412078575888">
        <loginMappings xmi:id="LoginMapping_1412078575888" authMethod="BasicAuth" configName="WSLogin">
          <callbackHandlerFactory xmi:id="CallbackHandlerFactory_1412078575888" classname="com.ibm.wsspi.wssecurity.auth.callback.WSCallbackHandlerFactoryImpl"/>
        </loginMappings>
      </securityRequestReceiverBindingConfig>
    </pcBindings>
  </wsdescBindings>
  <wsdescBindings xmi:id="WSDescBinding_1417768686852" wsDescNameLink="Rating_CreditBureauService">
    <pcBindings xmi:id="PCBinding_1417768686852" pcNameLink="Rating_CreditBureau"/>
  </wsdescBindings>
</com.ibm.etools.webservice.wsbnd:WSBinding>
