<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_AnnullamentoProposta">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_AnnullamentoPropostaResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_AnnullamentoPropostaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_AnnullamentoPropostaResponse">

      <wsdl:part element="impl:nprFidi_AnnullamentoPropostaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_AnnullamentoPropostaRequest">

      <wsdl:part element="impl:nprFidi_AnnullamentoProposta" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_AnnullamentoProposta_SEI">

      <wsdl:operation name="nprFidi_AnnullamentoProposta">

         <wsdl:input message="impl:nprFidi_AnnullamentoPropostaRequest" name="nprFidi_AnnullamentoPropostaRequest"/>

         <wsdl:output message="impl:nprFidi_AnnullamentoPropostaResponse" name="nprFidi_AnnullamentoPropostaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_AnnullamentoPropostaSoapBinding" type="impl:NPR_Fidi_AnnullamentoProposta_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_AnnullamentoProposta">

         <wsdlsoap:operation soapAction="nprFidi_AnnullamentoProposta"/>

         <wsdl:input name="nprFidi_AnnullamentoPropostaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_AnnullamentoPropostaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_AnnullamentoPropostaService">

      <wsdl:port binding="impl:NPR_Fidi_AnnullamentoPropostaSoapBinding" name="NPR_Fidi_AnnullamentoProposta">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_AnnullamentoProposta"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
