<?xml version='1.0'?>
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="internet">
<route appOwner="GWC" serviceName="ADALYAListaAttributiFIN" active="true"/>
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="PROGR-CHIAM"/>
                    <param name="INP-BANCA"/>
                    <param name="INP-CONV-TIPO"/>
                    <param name="INP-CONV-COD"/>
                    <param name="INP-PRODOTTO"/>
                    <param name="INP-AGGREGATO"/>
                    <param name="SW-DEBUG"/>
                </input>
                <output>
                    <xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request" xmlns:myxsl="http://www.w3.org/1999/XSL/Transform" version="1.0">
                        <xsl:output method="xml"/>
                        <xsl:template match="/">
                            <XP0C032>
                                <ROCH-03-HEADER>
                                    <ROCH-03-STRUCID>ROCH</ROCH-03-STRUCID>
                                    <ROCH-03-VERSION>0003</ROCH-03-VERSION>
                                    <ROCH-03-BSNAME>RBS-XX-XP0-LISTA-ATTRIBUTI</ROCH-03-BSNAME>
                                    <ROCH-03-RETURNCODE>0000</ROCH-03-RETURNCODE>
                                    <ROCH-03-UOWCONTROL>0000</ROCH-03-UOWCONTROL>
                                    <ROCH-03-ABEND-CODE/>
                                    <ROCH-03-MSG-LENGHT/>
                                    <ROCH-03-MSG-FORMAT/>
                                    <ROCH-03-REQID/>
                                    <ROCH-03-ERROR-MSG/>
                                    <ROCH-03-TOKEN/>
                                </ROCH-03-HEADER>
                                <XP0R32-INPUT>
                                    <XP0R32-PROGR-CHIAM>
                                        <xsl:value-of select="xgen:request/xgen:param[@name='PROGR-CHIAM']/text()"/>
                                    </XP0R32-PROGR-CHIAM>
                                    <XP0R32-INP-BANCA>
                                        <xsl:value-of select="xgen:request/xgen:param[@name='INP-BANCA']/text()"/>
                                    </XP0R32-INP-BANCA>
                                    <XP0R32-INP-CONV>
                                        <XP0R32-INP-CONV-TIPO>
                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-CONV-TIPO']/text()"/>
                                        </XP0R32-INP-CONV-TIPO>
                                        <XP0R32-INP-CONV-COD>
                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-CONV-COD']/text()"/>
                                        </XP0R32-INP-CONV-COD>
                                    </XP0R32-INP-CONV>
                                    <XP0R32-INP-PRODOTTO>
                                        <xsl:value-of select="xgen:request/xgen:param[@name='INP-PRODOTTO']/text()"/>
                                    </XP0R32-INP-PRODOTTO>
                                    <XP0R32-INP-AGGREGATO>
                                        <xsl:value-of select="xgen:request/xgen:param[@name='INP-AGGREGATO']/text()"/>
                                    </XP0R32-INP-AGGREGATO>
                                    <XP0R32-SW-DEBUG>
                                        <xsl:value-of select="xgen:request/xgen:param[@name='SW-DEBUG']/text()"/>
                                    </XP0R32-SW-DEBUG>
                                </XP0R32-INPUT>
                                <XP0R32-OUTPUT>
                                    <XP0R32-RC>1</XP0R32-RC>
                                    <XP0R32-MESS>_XP0R32-MESS_</XP0R32-MESS>
                                    <XP0R32-OUT-NUM-ELE>1</XP0R32-OUT-NUM-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                    <XP0R32-OUT-ELE>
                                        <XP0R32-OUT-ATTRIBUTO>_XP0R32-OUT-ATTRI</XP0R32-OUT-ATTRIBUTO>
                                        <XP0R32-OUT-SPESA>1</XP0R32-OUT-SPESA>
                                        <XP0R32-OUT-DESCR>_XP0R32-OUT-DESCR_</XP0R32-OUT-DESCR>
                                        <XP0R32-OUT-ATTR-PROG>1</XP0R32-OUT-ATTR-PROG>
                                        <XP0R32-OUT-DEFAULT>_XP0R32-OUT-DEFAULT</XP0R32-OUT-DEFAULT>
                                        <XP0R32-OUT-DESCR-VAL>_XP0R32-OUT-DESCR-VAL_</XP0R32-OUT-DESCR-VAL>
                                        <XP0R32-OUT-VALORE-DA>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-DA>
                                        <XP0R32-OUT-VALORE-A>_XP0R32-OUT-VALORE-</XP0R32-OUT-VALORE-A>
                                        <XP0R32-OUT-PASSO>_XP0R32-OUT-PASSO_</XP0R32-OUT-PASSO>
                                        <XP0R32-OUT-COMPETENZA>_SZ0</XP0R32-OUT-COMPETENZA>
                                        <XP0R32-OUT-ORDINE>1</XP0R32-OUT-ORDINE>
                                    </XP0R32-OUT-ELE>
                                </XP0R32-OUTPUT>
                            </XP0C032>
                        </xsl:template>
                    </xsl:stylesheet>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.RomaProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass/>
        <channelclass>it.usi.webfactory.channels.RomaStandardChannel</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params/>
    </protocol>
    <channel>
        <params>
            <client>Client_SZ0_XX</client>
            <service>RBS-XX-XP0-LISTA-ATTRIBUTI</service>
            <format>XP0C032-XML</format>
            <timeout>999000</timeout>
        </params>
    </channel>
</service>