<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprSinteticaPGASemplice">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
      <element name="x07" nillable="true" type="xsd:string"/>
      <element name="x97" nillable="true" type="xsd:string"/>
      <element name="x98" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprSinteticaPGASempliceResponse">
    <complexType>
     <sequence>
      <element name="nprSinteticaPGASempliceReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprSinteticaPGASempliceRequest">

      <wsdl:part element="impl:nprSinteticaPGASemplice" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprSinteticaPGASempliceResponse">

      <wsdl:part element="impl:nprSinteticaPGASempliceResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_SinteticaPGASemplice_SEI">

      <wsdl:operation name="nprSinteticaPGASemplice">

         <wsdl:input message="impl:nprSinteticaPGASempliceRequest" name="nprSinteticaPGASempliceRequest"/>

         <wsdl:output message="impl:nprSinteticaPGASempliceResponse" name="nprSinteticaPGASempliceResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_SinteticaPGASempliceSoapBinding" type="impl:NPR_SinteticaPGASemplice_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprSinteticaPGASemplice">

         <wsdlsoap:operation soapAction="nprSinteticaPGASemplice"/>

         <wsdl:input name="nprSinteticaPGASempliceRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprSinteticaPGASempliceResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_SinteticaPGASempliceService">

      <wsdl:port binding="impl:NPR_SinteticaPGASempliceSoapBinding" name="NPR_SinteticaPGASemplice">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_SinteticaPGASemplice"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
