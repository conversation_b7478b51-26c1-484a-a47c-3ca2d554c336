package it.usi.xframe.gwc.wsutil;

public class CallServiceTokenProxy implements it.usi.xframe.gwc.wsutil.CallServiceToken {
  private boolean _useJNDI = true;
  private String _endpoint = null;
  private it.usi.xframe.gwc.wsutil.CallServiceToken callServiceToken = null;
  
  public CallServiceTokenProxy() {
    _initCallServiceTokenProxy();
  }
  
  private void _initCallServiceTokenProxy() {
  
  if (_useJNDI) {
    try{
      javax.naming.InitialContext ctx = new javax.naming.InitialContext();
      callServiceToken = ((it.usi.xframe.gwc.wsutil.CallServiceTokenService)ctx.lookup("java:comp/env/service/CallServiceTokenService")).getCallServiceToken();
      }
    catch (javax.naming.NamingException namingException) {}
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  if (callServiceToken == null) {
    try{
      callServiceToken = (new it.usi.xframe.gwc.wsutil.CallServiceTokenServiceLocator()).getCallServiceToken();
      }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  if (callServiceToken != null) {
    if (_endpoint != null)
      ((javax.xml.rpc.Stub)callServiceToken)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    else
      _endpoint = (String)((javax.xml.rpc.Stub)callServiceToken)._getProperty("javax.xml.rpc.service.endpoint.address");
  }
  
}


public void useJNDI(boolean useJNDI) {
  _useJNDI = useJNDI;
  callServiceToken = null;
  
}

public String getEndpoint() {
  return _endpoint;
}

public void setEndpoint(String endpoint) {
  _endpoint = endpoint;
  if (callServiceToken != null)
    ((javax.xml.rpc.Stub)callServiceToken)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
  
}

public it.usi.xframe.gwc.wsutil.CallServiceToken getCallServiceToken() {
  if (callServiceToken == null)
    _initCallServiceTokenProxy();
  return callServiceToken;
}

public java.lang.String callServiceTokenPWD(java.lang.String service, java.lang.String params, java.lang.String sep1, java.lang.String sep2, String userid, String pwd) throws java.rmi.RemoteException{
  if (callServiceToken == null)
  	_initCallServiceTokenProxy();
    
  ((javax.xml.rpc.Stub)callServiceToken)._setProperty("userid", userid);
  ((javax.xml.rpc.Stub)callServiceToken)._setProperty("pwd", pwd);
  
  return this.callServiceToken(service, params, sep1, sep2);
}

public java.lang.String callServiceToken(java.lang.String service, java.lang.String params, java.lang.String sep1, java.lang.String sep2) throws java.rmi.RemoteException{
  if (callServiceToken == null)
    _initCallServiceTokenProxy();
  return callServiceToken.callServiceToken(service, params, sep1, sep2);
}


}