<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Proposta">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x97" nillable="true" type="xsd:string"/>
      <element name="x98" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_PropostaResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_PropostaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_PropostaResponse">

      <wsdl:part element="impl:nprFidi_PropostaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_PropostaRequest">

      <wsdl:part element="impl:nprFidi_Proposta" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Proposta_SEI">

      <wsdl:operation name="nprFidi_Proposta">

         <wsdl:input message="impl:nprFidi_PropostaRequest" name="nprFidi_PropostaRequest"/>

         <wsdl:output message="impl:nprFidi_PropostaResponse" name="nprFidi_PropostaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_PropostaSoapBinding" type="impl:NPR_Fidi_Proposta_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Proposta">

         <wsdlsoap:operation soapAction="nprFidi_Proposta"/>

         <wsdl:input name="nprFidi_PropostaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_PropostaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_PropostaService">

      <wsdl:port binding="impl:NPR_Fidi_PropostaSoapBinding" name="NPR_Fidi_Proposta">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Proposta"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
