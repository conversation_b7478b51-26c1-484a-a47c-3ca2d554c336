/*
 * Created on May 4, 2009
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.base;

import it.usi.xframe.ifg.bfutil.IfgGateway;
import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.utl.bfutil.IParams;
import it.usi.xframe.utl.bfutil.wm.handler.ServiceHandler;

import java.io.ByteArrayOutputStream;
import java.io.StringReader;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Hashtable;
import java.util.Map;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.xerces.parsers.DOMParser;
import org.apache.xml.serialize.OutputFormat;
import org.apache.xml.serialize.XMLSerializer;
import org.w3c.dom.Document;
import org.xml.sax.InputSource;
import org.xml.sax.XMLReader;

public abstract class BaseWebServiceManager implements IBaseConstDefinitions {

	private Log logger = LogFactory.getLog(this.getClass());
	private String serviceName = null;
	private ServiceHandler sh;
	protected Hashtable parameters;
	protected String xmlResponse;
	protected BaseResponseClass product;
	
	// Abstract methods:

	/**
	 * Deve essere implementata dalle classi Manager derivate, per impostare
	 * una HashTable contenente i parametri del Servizio Host da chiamare.
	 * @param iParams
	 * @param oParams
	 * @return nessun valore restituito
	 */
	protected abstract void setWSInputParameters(IParams iParams, Hashtable oParams) throws XFRException;

	protected void cleanWSInputParameters (Hashtable oParams) throws XFRException
	{}

	/**
	 * Deve essere implementata dalle classi Manager derivate, per 
	 * creare un oggetto contenente i dati ottenuti dalla chiamata al Servizio
	 * Host, strutturati nelle classi Value-Object appropriate.
	 * @return nessun valore restituito
	 * @throws XFRException
	 */
	protected abstract void buildResponseClass() throws XFRException;

	// public methods:
	public BaseWebServiceManager()
	{
		sh = new ServiceHandler();
		getServiceHandler().setFormatter(new BaseHostDataFormatting());
	}
	
	public BaseResponseClass getResponse() {
		return product;
	}	

	public String okTag()
	{
		return "";
	}
	
	public String koTag()
	{
		return "E";
	}
	
	public String getMessageCodeTag() {
		return "MESS_CODE";
	}

	public String getMessageTypeTag() {
		return "MESS_TYPE";
	}

	public String getMessageDescriptionTag() {
		return "MESS_DESCRIPTION";
	}

	public String getStatusTagName() {
		return "CXR_MESSAGE";
	}
	
	/**
	 * Restituisce il nome del servizio Host impostato dalla classe Manager derivata.
	 * @return String contenente il nome del Servizio Host
	 */
	public String getServiceName() {
		return serviceName;	
	}

	public void on_error_notify() throws XFRException {
			
	}

	/**
	 * Restituisce il riferimento all'oggetto {@link it.usi.xframe.utl.bfutil.wm.handler.ServiceHandler}
	 * @return l'oggetto ServiceHandler
	 */
	public ServiceHandler getServiceHandler() {
		return sh;	
	}
	
	public void execute(IParams inParams) throws XFRException, Exception {

		if (getFileName() == null)
		{
			Hashtable params = new Hashtable();

			setWSInputParameters(inParams, params);
			cleanWSInputParameters(params);		
			this.parameters = params;	
			callService(params);	  
			checkError();
			buildResponseClass();
		}	
		else
			testMsg(inParams);
	}

	protected void checkError() throws XFRException, Exception {
			
			if (getServiceHandler().seek((String) getStatusTagName()))
			{
				if (getServiceHandler().seekFromCurrent("record")) 
				{
					if (getServiceHandler().getString(getMessageTypeTag()).equalsIgnoreCase( okTag()))
					{
						product.setResult(true );
					}						 
					else if (getServiceHandler().getString(getMessageTypeTag()).equalsIgnoreCase(koTag()) )
					{
						on_error_notify();
						String s = getServiceHandler().getString(getMessageDescriptionTag());
						throw new XFRException(s);
					}
					else
					{
						// Se l'oggetto warning non esiste lo creo.
						BaseWarning war = product.getWarning();
						war.setCode(getServiceHandler().getString("MESS_TYPE"));							
						war.addDescription((getServiceHandler().getString("MESS_DESCRIPTION").equals("")) ? "EMPTY DESCRIPTION" : getServiceHandler().getString("MESS_DESCRIPTION"));
					}
				}
			}	

	}

	
	protected void callService(Map params) throws XFRException {
		try {
			logger.debug("Service [" + serviceName + "] - params [" + params.toString() + "]");
			IfgGateway gateway = new IfgGateway();
			String xmlResponse = gateway.perform(serviceName, params);
			this.xmlResponse = xmlResponse; 
			SAXParserFactory parserFactory = SAXParserFactory.newInstance();
			SAXParser parser = parserFactory.newSAXParser();
			XMLReader reader = parser.getXMLReader();
			logger.debug("Service [" + serviceName + "] - response [" + xmlResponse + "]");			
			reader.setContentHandler(sh.getParserHandler());
			reader.parse( new InputSource( new StringReader(xmlResponse))	);
		}
		catch (Exception e) {
			throw new XFRException(e);
		}
	}	

	protected void testMsg(IParams inParams) throws XFRException, Exception {

		Hashtable params = new Hashtable();

		logger.info("Simulation Call Service ------------------");
		logger.info("Service [" + getServiceName() + "] - params [" + params.toString() + "]");
		setWSInputParameters(inParams, params);

		String xmlResponse = getXmlFromFileSystem(getFileName());
			
		SAXParserFactory parserFactory = SAXParserFactory.newInstance();
		SAXParser saxParser = parserFactory.newSAXParser();
		XMLReader reader = saxParser.getXMLReader();

		logger.info("Service [" + getServiceName() + "] - response [" + xmlResponse + "]");
		logger.info("End Simulation Call Service --------------");
			
		reader.setContentHandler(getServiceHandler().getParserHandler());
		reader.parse( new InputSource( new StringReader(xmlResponse)) );
		
		checkError();
		
		if (getFileName() != null && product != null &&  product.getWarning() != null)
			product.getWarning().addDescription("Caution!! XML test file simulation");

		buildResponseClass();
	}	
	
	protected void setServiceName(String s) {
		serviceName = s;
	}

	protected String getFileName() {
		return null;
	}
	
	// private methods:
	
	// Errors
	private static String getXmlFromFileSystem(String fileName) throws XFRException {
	  try{
		DOMParser parser = new DOMParser();
		parser.parse(fileName);
		Document myDoc = parser.getDocument();
	
		String xmlResponse = getStringFromDocument(myDoc);
		return 	xmlResponse;
	  }
	  catch (Exception ex){
		throw new XFRException("HOST SIMULATION -- XML test file not found");
	  }
	}

	private static String getStringFromDocument( Document objDom ) throws Exception{
   
	   OutputFormat format;
	   ByteArrayOutputStream stream;
	   XMLSerializer serial;
	   String dati = "";
   
	   format = new OutputFormat(objDom);
	   format.setIndenting(false);
	   format.setLineSeparator("\n");
	   stream = new ByteArrayOutputStream();
	   serial = new XMLSerializer(stream, format);
	   serial.asDOMSerializer();
	   serial.serialize(objDom.getDocumentElement());
	   dati = stream.toString();

	   return dati;
	}
	
	public String date_2_host(Date in)
	{
		SimpleDateFormat sdf = new SimpleDateFormat(HOST_DATE_FORMAT);
		return ((in!=null)?sdf.format(in):DATE_EMPTY);
	}

	public Date date_2_web(String in)
	{
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(HOST_DATE_FORMAT);
			return ((in.equals(DATE_EMPTY))?null:sdf.parse(in));
		} catch (ParseException e) {
			return null;
		}
	}
	
	public Date timestamp_2_web(String in)
	{
		try {
			SimpleDateFormat sdf = new SimpleDateFormat(HOST_TIMESTAMP_FORMAT);
			return ((in.equals(TIMESTAMP_EMPTY))?null:sdf.parse(in));
		} catch (ParseException e) {
			return null;
		}
	}

	public String string_2_web(String in)
	{
		if (in==null) return "";
		else return in;
	}

	public String string_2_host(String in)
	{
		if (in==null) return "";
		else return in;
	}

	public String decimal_2_host(BigDecimal in)
	{
		if (in==null) return "";
		else return in.toString();
	}

	public String select_2_web(String in)
	{
		if (in==null || in.trim().equals(""))
			return SELECT_CODE_NONE;
		else return in;
	}


	public BigDecimal bigdecimal69_2_web(BigDecimal in)
	{
		if (in!=null && BIGDECIMALNULLVALUE6_9.compareTo(in)==0)
			return null;
		else 
			return in;
	}
	
	public BigDecimal bigdecimal99_2_web(BigDecimal in)
	{
		if (in!=null && BIGDECIMALNULLVALUE9_9.compareTo(in)==0)
			return null;
		else 
			return in;
	}	

}
