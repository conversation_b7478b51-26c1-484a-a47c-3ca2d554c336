/**
 * GetHeadersResponse.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * cf190823.02 v62608112801
 */

package it.usi.xframe.gwc.wsutil;

public class GetHeadersResponse  implements java.io.Serializable {
    private javax.xml.namespace.QName[] getHeadersReturn;

    public GetHeadersResponse() {
    }

    public javax.xml.namespace.QName[] getGetHeadersReturn() {
        return getHeadersReturn;
    }

    public void setGetHeadersReturn(javax.xml.namespace.QName[] getHeadersReturn) {
        this.getHeadersReturn = getHeadersReturn;
    }

    public javax.xml.namespace.QName getGetHeadersReturn(int i) {
        return getHeadersReturn[i];
    }

    public void setGetHeadersReturn(int i, javax.xml.namespace.QName value) {
        this.getHeadersReturn[i] = value;
    }

    private transient java.lang.ThreadLocal __history;
    public boolean equals(java.lang.Object obj) {
        if (obj == null) { return false; }
        if (obj.getClass() != this.getClass()) { return false;}
        if (__history == null) {
            synchronized (this) {
                if (__history == null) {
                    __history = new java.lang.ThreadLocal();
                }
            }
        }
        GetHeadersResponse history = (GetHeadersResponse) __history.get();
        if (history != null) { return (history == obj); }
        if (this == obj) return true;
        __history.set(obj);
        GetHeadersResponse other = (GetHeadersResponse) obj;
        boolean _equals;
        _equals = true
            && ((this.getHeadersReturn==null && other.getGetHeadersReturn()==null) || 
             (this.getHeadersReturn!=null &&
              java.util.Arrays.equals(this.getHeadersReturn, other.getGetHeadersReturn())));
        if (!_equals) {
            __history.set(null);
            return false;
        };
        __history.set(null);
        return true;
    }

    private transient java.lang.ThreadLocal __hashHistory;
    public int hashCode() {
        if (__hashHistory == null) {
            synchronized (this) {
                if (__hashHistory == null) {
                    __hashHistory = new java.lang.ThreadLocal();
                }
            }
        }
        GetHeadersResponse history = (GetHeadersResponse) __hashHistory.get();
        if (history != null) { return 0; }
        __hashHistory.set(this);
        int _hashCode = 1;
        if (getGetHeadersReturn() != null) {
            for (int i=0;
                 i<java.lang.reflect.Array.getLength(getGetHeadersReturn());
                 i++) {
                java.lang.Object obj = java.lang.reflect.Array.get(getGetHeadersReturn(), i);
                if (obj != null &&
                    !obj.getClass().isArray()) {
                    _hashCode += obj.hashCode();
                }
            }
        }
        __hashHistory.set(null);
        return _hashCode;
    }

}
