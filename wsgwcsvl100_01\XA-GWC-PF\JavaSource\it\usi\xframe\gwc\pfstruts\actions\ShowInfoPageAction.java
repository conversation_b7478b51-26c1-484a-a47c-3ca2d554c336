package it.usi.xframe.gwc.pfstruts.actions;

import it.usi.xframe.system.bfinfo.BaselineInfo;
import it.usi.xframe.system.bfinfo.BaselineInfoFactory;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */

public class ShowInfoPageAction extends Action {
	
	private Log logger = LogFactory.getLog(this.getClass());

	public class BaselineProject 
	{
		private BaselineInfo baselineInfo;			

		public BaselineInfo getBaselineInfo() 				{ 	return baselineInfo;		}
		public void setBaselineInfo(BaselineInfo info) 		{ 	baselineInfo = info;		}
	}


	public ActionForward execute(
		ActionMapping forward,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {
				
		BaselineProject infoProject = new BaselineProject();
		infoProject.setBaselineInfo(	BaselineInfoFactory.getInstance().getInfo("gwc")	);
		
		if(infoProject != null)
			request.setAttribute("baselineInfo", infoProject.getBaselineInfo());

		String referer = request.getHeader("referer");
		logger.info("referer = [" + referer + "]");
		request.setAttribute("testReferrer", referer);

		return forward.findForward("ok");
	}
}