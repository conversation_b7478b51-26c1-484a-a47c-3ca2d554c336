<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE webservices PUBLIC "-//IBM Corporation, Inc.//DTD J2EE Web services 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_web_services_1_0.dtd">
<webservices>
	<webservice-description>
		<webservice-description-name>CallServiceService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/CallService.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/CallService_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>CallService</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>CallService</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallService_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_CallService</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>CallServiceFreeService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/CallServiceFree.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/CallServiceFree_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>CallServiceFree</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>CallServiceFree</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallServiceFree_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_CallServiceFree</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>CallServiceTokenService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/CallServiceToken.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/CallServiceToken_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>CallServiceToken</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>CallServiceToken</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallServiceToken_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_CallServiceToken</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_AnagraficaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Anagrafica.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Anagrafica_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Anagrafica</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Anagrafica</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Anagrafica_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Anagrafica</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_AnagraficaAce6Service</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_AnagraficaAce6.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_AnagraficaAce6_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_AnagraficaAce6</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_AnagraficaAce6</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_AnagraficaAce6_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_AnagraficaAce6</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_BigliettoPGAService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_BigliettoPGA.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_BigliettoPGA_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_BigliettoPGA</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_BigliettoPGA</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_BigliettoPGA_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_BigliettoPGA</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_DatiFidiPraticaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_DatiFidiPratica.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_DatiFidiPratica_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_DatiFidiPratica</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_DatiFidiPratica</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_DatiFidiPratica_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_DatiFidiPratica</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_EsitoPGAService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_EsitoPGA.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_EsitoPGA_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_EsitoPGA</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_EsitoPGA</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_EsitoPGA_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_EsitoPGA</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_AggiornaRigaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_AggiornaRiga.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_AggiornaRiga_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_AggiornaRiga</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_AggiornaRiga</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_AggiornaRiga_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_AggiornaRiga</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_AnnullamentoPropostaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_AnnullamentoProposta.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_AnnullamentoProposta_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_AnnullamentoProposta</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_AnnullamentoProposta</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_AnnullamentoProposta_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_AnnullamentoProposta</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_Autorizzazione_NegativaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Autorizzazione_Negativa.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Autorizzazione_Negativa_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Autorizzazione_Negativa</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Autorizzazione_Negativa</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Autorizzazione_Negativa_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Autorizzazione_Negativa</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_AutorizzazioneService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Autorizzazione.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Autorizzazione_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Autorizzazione</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Autorizzazione</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Autorizzazione_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Autorizzazione</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_AvocaturaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Avocatura.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Avocatura_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Avocatura</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Avocatura</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Avocatura_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Avocatura</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_BloccoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Blocco.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Blocco_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Blocco</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Blocco</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Blocco_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Blocco</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_CancellaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Cancella.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Cancella_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Cancella</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Cancella</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Cancella_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Cancella</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_CompletamentoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Completamento.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Completamento_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Completamento</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Completamento</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Completamento_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Completamento</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_DeclinoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Declino.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Declino_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Declino</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Declino</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Declino_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Declino</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_DeliberaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Delibera.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Delibera_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Delibera</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Delibera</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Delibera_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Delibera</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_Inquiry_DatoVarioService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Inquiry_DatoVario.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Inquiry_DatoVario_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Inquiry_DatoVario</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Inquiry_DatoVario</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Inquiry_DatoVario_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Inquiry_DatoVario</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_IterPropostaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_IterProposta.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_IterProposta_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_IterProposta</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_IterProposta</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_IterProposta_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_IterProposta</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_LogonService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Logon.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Logon_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Logon</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Logon</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Logon_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Logon</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_PenninoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Pennino.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Pennino_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Pennino</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Pennino</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Pennino_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Pennino</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_PerfezionamentoGaranziaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_PerfezionamentoGaranzia.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_PerfezionamentoGaranzia_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_PerfezionamentoGaranzia</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_PerfezionamentoGaranzia</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_PerfezionamentoGaranzia_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_PerfezionamentoGaranzia</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_PreCompletamentoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_PreCompletamento.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_PreCompletamento_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_PreCompletamento</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_PreCompletamento</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_PreCompletamento_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_PreCompletamento</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_PropostaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_Proposta.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_Proposta_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_Proposta</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_Proposta</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Proposta_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_Proposta</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fidi_RecuperoNdgService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fidi_RecuperoNdg.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fidi_RecuperoNdg_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fidi_RecuperoNdg</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fidi_RecuperoNdg</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_RecuperoNdg_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fidi_RecuperoNdg</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_FidiIterPropostaNewService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_FidiIterPropostaNew.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_FidiIterPropostaNew_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_FidiIterPropostaNew</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_FidiIterPropostaNew</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_FidiIterPropostaNew_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_FidiIterPropostaNew</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fin_DettaglioService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fin_Dettaglio.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fin_Dettaglio_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fin_Dettaglio</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fin_Dettaglio</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fin_Dettaglio_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fin_Dettaglio</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_Fin_TassoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Fin_Tasso.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Fin_Tasso_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Fin_Tasso</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Fin_Tasso</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fin_Tasso_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Fin_Tasso</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_ListaFidiService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_ListaFidi.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_ListaFidi_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_ListaFidi</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_ListaFidi</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_ListaFidi_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_ListaFidi</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_LoginService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Login.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Login_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Login</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Login</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Login_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Login</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_PfaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Pfa.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Pfa_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Pfa</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Pfa</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Pfa_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Pfa</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_RatingService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Rating.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Rating_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Rating</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Rating</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Rating_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Rating</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_SEM_ZivnoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_SEM_Zivno.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_SEM_Zivno_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_SEM_Zivno</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_SEM_Zivno</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_SEM_Zivno_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_SEM_Zivno</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_SinteticaPGAService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_SinteticaPGA.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_SinteticaPGA_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_SinteticaPGA</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_SinteticaPGA</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_SinteticaPGA_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_SinteticaPGA</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_SinteticaPGASempliceService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_SinteticaPGASemplice.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_SinteticaPGASemplice_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_SinteticaPGASemplice</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_SinteticaPGASemplice</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_SinteticaPGASemplice_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_SinteticaPGASemplice</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPX_Fidi_CompletamentoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPX_Fidi_Completamento.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPX_Fidi_Completamento_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPX_Fidi_Completamento</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPX_Fidi_Completamento</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPX_Fidi_Completamento_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPX_Fidi_Completamento</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPX_Fidi_InserimentoFidiService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPX_Fidi_InserimentoFidi.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPX_Fidi_InserimentoFidi_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPX_Fidi_InserimentoFidi</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPX_Fidi_InserimentoFidi</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPX_Fidi_InserimentoFidi_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPX_Fidi_InserimentoFidi</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPX_Fidi_LogonService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPX_Fidi_Logon.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPX_Fidi_Logon_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPX_Fidi_Logon</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPX_Fidi_Logon</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPX_Fidi_Logon_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPX_Fidi_Logon</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPX_Fidi_PreCompletamentoService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPX_Fidi_PreCompletamento.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPX_Fidi_PreCompletamento_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPX_Fidi_PreCompletamento</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPX_Fidi_PreCompletamento</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPX_Fidi_PreCompletamento_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPX_Fidi_PreCompletamento</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPX_Fidi_AperturaPropostaService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPX_Fidi_AperturaProposta.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPX_Fidi_AperturaProposta_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPX_Fidi_AperturaProposta</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPX_Fidi_AperturaProposta</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPX_Fidi_AperturaProposta_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPX_Fidi_AperturaProposta</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>NPR_DossierService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/NPR_Dossier.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/NPR_Dossier_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>NPR_Dossier</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>NPR_Dossier</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Dossier_SEI</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_NPR_Dossier</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
	<webservice-description>
		<webservice-description-name>Rating_CreditBureauService</webservice-description-name>
		<wsdl-file>WEB-INF/wsdl/Rating_CreditBureau.wsdl</wsdl-file>
		<jaxrpc-mapping-file>WEB-INF/Rating_CreditBureau_mapping.xml</jaxrpc-mapping-file>
		<port-component>
			<port-component-name>Rating_CreditBureau</port-component-name>
			<wsdl-port>
				<namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
				<localpart>Rating_CreditBureau</localpart>
			</wsdl-port>
			<service-endpoint-interface>it.usi.xframe.gwc.wsutil.Rating_CreditBureau</service-endpoint-interface>
			<service-impl-bean>
				<servlet-link>
				it_usi_xframe_gwc_wsutil_Rating_CreditBureau</servlet-link>
			</service-impl-bean>
		</port-component>
	</webservice-description>
</webservices>
