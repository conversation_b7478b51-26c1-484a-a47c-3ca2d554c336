/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_Pfa {
	
	//	Default Constructor - RSA8 migration prerequisites	
	public NPR_Pfa(){
			
	}	

	public String nprPfa(String x01, String n001, String x02, String n002, String x03, String x04, String x95, String x96) throws Exception {

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprPfa(x01, n001, x02, n002, x03, x04, x95, x96);
	}     
}
