<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts-config PUBLIC "-//Apache Software Foundation//DTD Struts Configuration 1.1//EN"
                               "http://jakarta.apache.org/struts/dtds/struts-config_1_1.dtd">

<struts-config>

	<!-- Data Sources -->
	<data-sources>
	</data-sources>

	<!-- Form Beans -->

	<!-- Global Exceptions -->
	<form-beans>
		<form-bean name="inputForm" type="it.usi.xframe.gwc.pfstruts.forms.InputForm">
		</form-bean>
		<form-bean name="getFieldForm" type="it.usi.xframe.gwc.pfstruts.forms.GetFieldForm">
		</form-bean>
	</form-beans>
	<global-exceptions>
	</global-exceptions>

	<!-- Global Forwards -->
	<global-forwards>
	</global-forwards>

	<!-- Action Mappings -->
	<action-mappings>
		<action path="/index" validate="false" type="it.usi.xframe.gwc.pfstruts.actions.ShowInfoPageAction">
			<forward name="ok" path="showInfo.page">
			</forward>
		</action>
		<action path="/startBios" type="it.usi.xframe.gwc.pfstruts.actions.StartBios">
			<forward name="ok" redirect="true" path='../../XA-PGE-PF/private/pgeStartApp.do?applicationToStart=token&amp;timeout=1200000&amp;pgeMenuId=startTest&amp;backUrl=https://bios.cerved.com/bios/Login&amp;banca=var_banca&amp;sportello=var_sportello&amp;userid=var_user&amp;profilo_bios=var_profilo&amp;user_div=var_user_div&amp;branch_div=var_branch_div'>
			</forward>
			<forward name="okCollaudo" redirect="true" path="../../XA-PGE-PF/private/pgeStartApp.do?applicationToStart=token&amp;timeout=1200000&amp;pgeMenuId=startTest&amp;backUrl=http://biostest.cervedgroup.com/bios/Login&amp;banca=var_banca&amp;sportello=var_sportello&amp;userid=var_user&amp;profilo_bios=var_profilo&amp;user_div=var_user_div&amp;branch_div=var_branch_div">
			</forward>
		</action>
		<action path="/startBiosV2" type="it.usi.xframe.gwc.pfstruts.actions.StartBiosV2">
			<forward name="ok" path="startBiosV2.page">
			</forward>
		</action>
		<action path="/testPge" type="it.usi.xframe.gwc.pfstruts.actions.TestPge">
			<forward name="ok" path="testPge.page">
			</forward>
		</action>
		<action path="/startPgeTest" type="it.usi.xframe.gwc.pfstruts.actions.StartBios">
			<forward name="ok" redirect="true" path="../../XA-PGE-PF/private/pgeStartApp.do?applicationToStart=token&amp;timeout=1200000&amp;pgeMenuId=startTest&amp;backUrl=../../XA-GWC-PF/testPge.do&amp;banca=var_banca&amp;sportello=var_sportello&amp;userid=var_user&amp;profilo_bios=var_profilo">
			</forward>
			<forward name="okCollaudo" redirect="true" path="../../XA-PGE-PF/private/pgeStartApp.do?applicationToStart=token&amp;timeout=1200000&amp;pgeMenuId=startTest&amp;backUrl=../../XA-GWC-PF/testPge.do&amp;banca=var_banca&amp;sportello=var_sportello&amp;userid=var_user&amp;profilo_bios=var_profilo">
			</forward>
		</action>
		<action path="/startAfaTest" type="it.usi.xframe.gwc.pfstruts.actions.StartBios">
			<forward name="ok" path="../../XA-PGE-PF/private/pgeStartApp.do?applicationToStart=token&amp;timeout=1200000&amp;pgeMenuId=startTest&amp;backUrl=http://usw12554:9080/AFA-WSDL-AXIS-TEST/sample/AFAServicesProxy/TestClient.jsp&amp;banca=var_banca&amp;sportello=var_sportello&amp;userid=var_user&amp;profilo_bios=var_profilo" redirect="true">
			</forward>
			<forward name="okCollaudo" redirect="true" path="../../XA-PGE-PF/private/pgeStartApp.do?applicationToStart=token&amp;timeout=1200000&amp;pgeMenuId=startTest&amp;backUrl=http://usw12554:9080/AFA-WSDL-AXIS-TEST/sample/AFAServicesProxy/TestClient.jsp&amp;banca=var_banca&amp;sportello=var_sportello&amp;userid=var_user&amp;profilo_bios=var_profilo">
			</forward>
		</action>
		<action path="/testMsg" type="it.usi.xframe.gwc.pfstruts.actions.InputTestAction">
			<forward name="ok" path="tiles/inputPage.jsp">
			</forward>
		</action>
		<action path="/executeMsg" type="it.usi.xframe.gwc.pfstruts.actions.ExecuteAction" name="inputForm">
			<forward name="ok" path="tiles/resultPage.jsp">
			</forward>
		</action>
		<action path="/getField" type="it.usi.xframe.gwc.pfstruts.actions.GetFieldAction" name="getFieldForm">
			<forward name="ok" path="tiles/resultFieldPage.jsp">
			</forward>
		</action>
		<action path="/UCF_Copier" type="it.usi.xframe.gwc.pfstruts.actions.UCF_Copier_Action">
			<forward name="ok" path="UCF_Copier.page">
			</forward>
		</action>
		<action path="/sessionExpired" type="org.apache.struts.actions.ForwardAction" parameter="session_expired.page">
			
		</action>
		
		<action path="/gauss_form" type="it.usi.xframe.gwc.pfstruts.actions.GaussFormAction">
			<forward name="ok" path="gauss.form.page">
			</forward>
		</action>
		<action path="/gauss_call" type="it.usi.xframe.gwc.pfstruts.actions.GaussCallAction">
			<forward name="ok" path="gauss.xml.page">
			</forward>
		</action>
		<action path="/testPwd" type="it.usi.xframe.gwc.pfstruts.actions.TestPwd">
			<forward name="ok" redirect="true" path="../../XA-PGE-PF/private/pgeStartApp.do?applicationToStart=npp&amp;backUrl=%2FXA-GWC-PF%2FreadPwd.do&amp;userid=US00465">
			</forward>
		</action>
		<action path="/readPwd" type="it.usi.xframe.gwc.pfstruts.actions.ReadPwd">
			<forward name="ok" path="readPwd.page">
			</forward>
		</action>
		<action path="/testCallService" type="it.usi.xframe.gwc.pfstruts.actions.TestCallService">
			<forward name="ok" path="sample/CallServiceProxy/TestClient.jsp">
			</forward>
		</action>
		<action path="/testCallServiceToken" type="it.usi.xframe.gwc.pfstruts.actions.TestCallServiceToken">
			<forward name="ok" path="../../XA-PGE-PF/private/pgeStartApp.do?userid=var_user&amp;applicationToStart=finage&amp;timeout=1200000&amp;backUrl=../../XA-GWC-PF/exeCallServiceToken.do&amp;pgetokname=attributo" redirect="true">
			</forward>
		</action>
		<action path="/exeCallServiceToken" type="it.usi.xframe.gwc.pfstruts.actions.ExeCallServiceToken">
			<forward name="ok" path="sample/CallServiceProxy/TestClient.jsp">
			</forward>
		</action>
		<action path="/testCallServiceFree" type="it.usi.xframe.gwc.pfstruts.actions.TestCallServiceFree">
			<forward name="ok" path="sample/CallServiceProxy/TestClient.jsp">
			</forward>
		</action>			
	</action-mappings>


	<!-- Plug In -->
	<plug-in className="org.apache.struts.tiles.TilesPlugin">
		<set-property value="/WEB-INF/cpt/cal/tiles-defs.xml,/WEB-INF/tiles-defs.xml" property="definitions-config"/>
		<set-property value="true" property="definitions-parser-validate"/>
		<set-property value="true" property="moduleAware"/>
	</plug-in>

</struts-config>
