<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R71I-PROPOSAL-ID"/>
					<param name="R71I-FUNCTION"/>
					<param name="R71I-SCREEN-ID"/>
					<param name="R71I-USER-ID"/> 
					<param name="R71I-DT-BALANCE-SHEET"/>
					<param name="R71I-TYPE-BALANCE-SHEET"/>
					<param name="R71I-COMPANION-CODE"/>
					<param name="R71I-SNDG"/>
					<param name="R71I-IN-LE"/>
					<param name="R71I-IN-CUSTOMER-ID"/> 
					<param name="R71I-COD-COUNTRY"/>
					<param name="R71I-SERVER"/>
					<param name="R71I-DT-RAT-ASS-FROM"/> 
					<param name="R71I-DELETE-MOT-A"/>
					<param name="R71I-DELETE-MOT-M"/>
					<param name="R71I-SEGM-USER"/>       
					<param name="R71I-SEGM-MOT-A"/>
					<param name="R71I-SEGM-MOT-M"/>
					<param name="R71I-TS-CALC"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB71-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB71</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
