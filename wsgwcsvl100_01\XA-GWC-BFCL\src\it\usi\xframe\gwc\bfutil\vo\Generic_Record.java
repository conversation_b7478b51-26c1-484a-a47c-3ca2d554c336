/*
 * Created on Apr 7, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.vo;

import it.usi.xframe.utl.bfutil.DataValue;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;



public class Generic_Record extends DataValue implements Serializable {

	Map fields = new HashMap();

	public Generic_Record() {
	}

	/**
	 * @return
	 */
	public Map getFields() {
		return fields;
	}

	/**
	 * @param map
	 */
	public void setFields(Map map) {
		fields = map;
	}

	public Collection getFieldsName()
	{
		return fields.keySet();
	}
}
