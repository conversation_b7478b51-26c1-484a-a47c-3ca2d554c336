<%@ taglib uri="http://jakarta.apache.org/struts/tags-tiles" prefix="tiles" %>
<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jstl/fmt" prefix="fmt" %>

<fmt:setLocale value="${sessionScope['org.apache.struts.action.LOCALE']}"/>
<tiles:importAttribute />

<c:choose>
	<c:when test="${inputField == ''}">
		<fmt:message key="cpt.cal.message.calendar.error"><fmt:param value="inputField"/></fmt:message>
	</c:when>
	<c:otherwise>
		<script type="text/javascript">
			Calendar.setup(
				{
					<tiles:insert definition="cpt.cal.calendar.test.base">
						<tiles:put name="ifFormat"><tiles:getAsString name="ifFormat"/></tiles:put>
						<tiles:put name="daFormat"><tiles:getAsString name="daFormat"/></tiles:put>
						<tiles:put name="dateStatusFunc"><tiles:getAsString name="dateStatusFunc"/></tiles:put>
						<tiles:put name="firstDay"><tiles:getAsString name="firstDay"/></tiles:put>
						<tiles:put name="weekNumbers"><tiles:getAsString name="weekNumbers"/></tiles:put>
						<tiles:put name="align"><tiles:getAsString name="align"/></tiles:put>
						<tiles:put name="range"><tiles:getAsString name="range"/></tiles:put>
						<tiles:put name="onSelect"><tiles:getAsString name="onSelect"/></tiles:put>
						
						<tiles:put name="date"><tiles:getAsString name="date"/></tiles:put>
						<tiles:put name="showsTime"><tiles:getAsString name="showsTime"/></tiles:put>
						<tiles:put name="timeFormat"><tiles:getAsString name="timeFormat"/></tiles:put>
						<tiles:put name="electric"><tiles:getAsString name="electric"/></tiles:put>
						<tiles:put name="position"><tiles:getAsString name="position"/></tiles:put>
						<tiles:put name="cache"><tiles:getAsString name="cache"/></tiles:put>
						<tiles:put name="showOthers"><tiles:getAsString name="showOthers"/></tiles:put>
					</tiles:insert>
					<c:if test="${displayArea != ''}">
						displayArea : "<tiles:getAsString name="displayArea"/>",
					</c:if>
					<c:if test="${eventName != ''}">
						eventName : "<tiles:getAsString name="eventName"/>",
					</c:if>
					<c:if test="${singleClick != ''}">
						singleClick : <tiles:getAsString name="singleClick"/>,
					</c:if>
					<c:if test="${button != ''}">
						button : "<tiles:getAsString name="button"/>",
					</c:if>
					<c:if test="${onClose != ''}">
						onClose : <tiles:getAsString name="onClose"/>,
					</c:if>
					inputField : "<tiles:getAsString name="inputField"/>"
					
				}
			)
		</script>
	</c:otherwise>
</c:choose>