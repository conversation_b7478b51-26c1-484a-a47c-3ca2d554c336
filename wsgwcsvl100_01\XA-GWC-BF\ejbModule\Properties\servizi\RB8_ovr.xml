<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R92I-PROPOSAL-ID"/>
					<param name="R92I-FUNCTION"/>
					<param name="R92I-FLAG-ENV"/>
					<param name="R92I-OVR-STATUS"/>
					<param name="R92I-USER-ID"/>
					<param name="R92I-AUTH-LEV"/>
					<param name="R92I-FLAG-OVR-PD"/>
					<param name="R92I-FLAG-OVR-LGD"/>
					<param name="R92I-FLAG-OVR-LGD-J"/>
					<param name="R92I-OVR-COD-RAT-TR"/>
					<param name="R92I-OVR-LGD-LOANS"/>
					<param name="R92I-OVR-LGD-BONDS"/>
					<param name="R92I-OVR-LGD-JUNIOR"/>
					<param name="R92I-OVR-NOTE"/>
					<param name="R92I-OVR-MOTIV-M-PD"/>
					<param name="R92I-OVR-MOTIV-M-LGD"/>
					<param name="R92I-OVR-MOTIV-M-LGD-J"/>
					<param name="R92I-MOTIVATION-LIST"/>
			    </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB92-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB92</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
