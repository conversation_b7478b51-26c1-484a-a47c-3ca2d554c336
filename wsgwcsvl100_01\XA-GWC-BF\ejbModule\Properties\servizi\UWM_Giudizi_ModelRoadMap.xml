<?xml version="1.0"?>
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="UWMC051I_BANCA"/>
					<param name="UWMC051I_NDG"/>
					<param name="UWMC051I_QUEST_DOM_RISP_1"/>
					<param name="UWMC051I_QUEST_DOM_RISP_2"/>
					<param name="UWMC051I_QUEST_DOM_RISP_3"/>
					<param name="UWMC051I_QUEST_DOM_RISP_4"/>
					<param name="UWMC051I_QUEST_DOM_RISP_5"/>
					<param name="UWMC051I_QUEST_DOM_RISP_6"/>
					<param name="UWMC051I_QUEST_DOM_RISP_7"/>
					<param name="UWMC051I_QUEST_DOM_RISP_8"/>
					<param name="UWMC051I_QUEST_DOM_RISP_9"/>
					<param name="UWMC051I_QUEST_DOM_RISP_10"/>
					<param name="UWMC051I_QUEST_DOM_RISP_11"/>
					<param name="UWMC051I_QUEST_DOM_RISP_12"/>
					<param name="UWMC051I_QUEST_DOM_RISP_13"/>
					<param name="UWMC051I_QUEST_DOM_RISP_14"/>
					<param name="UWMC051I_QUEST_DOM_RISP_15"/>
					<param name="UWMC051I_QUEST_DOM_RISP_16"/>
					<param name="UWMC051I_QUEST_DOM_RISP_17"/>
					<param name="UWMC051I_QUEST_DOM_RISP_18"/>
					<param name="UWMC051I_QUEST_DOM_RISP_19"/>
					<param name="UWMC051I_QUEST_DOM_RISP_20"/>
					<param name="UWMC051I_QUEST_DOM_RISP_21"/>
					<param name="UWMC051I_QUEST_DOM_RISP_22"/>
					<param name="UWMC051I_QUEST_DOM_RISP_23"/>
					<param name="UWMC051I_QUEST_DOM_RISP_24"/>
					<param name="UWMC051I_QUEST_DOM_RISP_25"/>
					<param name="UWMC051I_QUEST_DOM_RISP_26"/>
					<param name="UWMC051I_QUEST_DOM_RISP_27"/>
					<param name="UWMC051I_QUEST_DOM_RISP_28"/>
					<param name="UWMC051I_QUEST_DOM_RISP_29"/>
					<param name="UWMC051I_QUEST_DOM_RISP_30"/>
					<param name="UWMC051I_MATRICOLA"/>
					<param name="UWMC051I_COGNOME"/>
					<param name="UWMC051I_NOME"/>
					<param name="XF_GAUSS_ID"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		 <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
    </provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<hostService>UWM_GIUDIZI</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUW1</transaction>
			<program>PC00WUW1</program>
			<timeout>15000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>