<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>

<script type="text/javascript">
	function onBack() {
	    location.href = "testMsg.do";
	}

	function onOk() {
		document.getFieldForm.action="getField.do";
		document.getFieldForm.submit();
	}
</script>


<form name="getFieldForm" enctype="multipart/form-data" method ="post">
<TABLE width="100%" border="0" cellpadding="2" style="margin-bottom: 2px">
	<TR>
		<TD align="left">
			<c:out value="${requestScope.responseClass.xmlResponse}"/>
		</TD>
	</TR>
	<tr>
		<td>
			Tag From :
		</td>
		<TD align="left">
			<INPUT type="text" id="tagFrom" name="tagFrom">
		</TD>
	</tr>
	<tr>
		<td>
			Tag :
		</td>
		<TD align="left">
			<INPUT type="text" id="tag" name="tag">
		</TD>
	</tr>
	<tr>
		<td>
			Record :
		</td>
		<TD align="left">
			<INPUT type="text" id="record" name="record">
		</TD>
	</tr>
	<tr>
	  <td align="center">
		<button id="back" name="back" title="Back" onclick="javascript:onBack()">Back</button>
	  </td>
	  <td align="center">
		<button id="ok" name="ok" title="Legge il campo specificato" onclick="javascript:onOk()">OK</button>
	  </td>
    </tr>
</TABLE>
</form>
