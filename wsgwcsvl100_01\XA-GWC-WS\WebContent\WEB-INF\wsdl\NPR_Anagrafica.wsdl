<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprAnagrafica">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x97" nillable="true" type="xsd:string"/>
      <element name="x98" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprAnagraficaResponse">
    <complexType>
     <sequence>
      <element name="nprAnagraficaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprAnagraficaResponse">

      <wsdl:part element="impl:nprAnagraficaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprAnagraficaRequest">

      <wsdl:part element="impl:nprAnagrafica" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Anagrafica_SEI">

      <wsdl:operation name="nprAnagrafica">

         <wsdl:input message="impl:nprAnagraficaRequest" name="nprAnagraficaRequest"/>

         <wsdl:output message="impl:nprAnagraficaResponse" name="nprAnagraficaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_AnagraficaSoapBinding" type="impl:NPR_Anagrafica_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprAnagrafica">

         <wsdlsoap:operation soapAction="nprAnagrafica"/>

         <wsdl:input name="nprAnagraficaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprAnagraficaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_AnagraficaService">

      <wsdl:port binding="impl:NPR_AnagraficaSoapBinding" name="NPR_Anagrafica">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Anagrafica"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
