package it.usi.xframe.gwc.pfstruts.actions;

import it.usi.xframe.gwc.bfutil.vo.BankData;
import it.usi.xframe.gwc.bfutil.vo.BanksList;
import it.usi.xframe.gwc.pfutil.GwcDelegate;
import it.usi.xframe.ifg.bfutil.IfgServiceFactory;
import it.usi.xframe.ifg.bfutil.wpro.UserData;
import it.usi.xframe.pre.bfutil.PreServiceFactory;
import it.usi.xframe.pre.bfutil.PreUserData;
import it.usi.xframe.pre.bfutil.PreUserInfo;
import it.usi.xframe.system.errors.XFRSevereException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class StartBios extends Action {

	private Log logger = LogFactory.getLog(this.getClass());

	public static final String REQUESTPARAM_NOTVALID = "error.params.invalid";
	public static final String LOGIN_ERROR = "error.login";

	private Object[] getArrayFromString(String s) {
		Object[] a = new Object[1];
		if (s==null) s="???";
		a[0] = s; 
		return a;
	}

	public static final String STORAGE_OBJECT_NAME = "sessionData";

	// Execute Implementation
	public ActionForward execute(
		ActionMapping mapping,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {

		// Retrieve logged user information
		UserData userData;
		PreUserInfo profile;
		PreUserData profileData;
		
		try {
			userData = IfgServiceFactory.getInstance().getIfgServiceFacade().getUserInfo();
			//profile = PreServiceFactory.getInstance().getPreServiceFacade().getPreUserInfo(userData.getCodIstituto(), userData.getCodOperatore(), "ZA");
			profile = PreServiceFactory.getInstance().getPreServiceFacade().retrievePreUserInfo(userData.getCodIstituto(), userData.getCodOperatore(), "ZA");
			profileData = PreServiceFactory.getInstance().getPreServiceFacade().retrievePreUserData("ZA");
		} catch (Exception e) {
			throw new XFRSevereException(LOGIN_ERROR, getArrayFromString(e.getMessage()));
		}

		if (userData == null)
			throw new XFRSevereException(REQUESTPARAM_NOTVALID, getArrayFromString("[userData]"));

		logger.info("Banca = [" + profile.getPG_COD_IST() + "]");
		logger.info("Utente = [" + userData.getCodOperatore() + "]");
		logger.info("User First Name = [" + profile.getPG_NOME_OPER() + "]");
		logger.info("User Last  Name = [" + profile.getPG_COGNOME_OPER() + "]");
		logger.info("User Role = [" + profile.getPG_COD_AUTHOR() + "]");  
		logger.info("User New Tower = [" + profile.getPG_CODISA_INQ() + "]");  
		logger.info("User New Branch = [" + profile.getPG_COD_SPORT_CONT() + "]");
		logger.info("User Division = [" + profileData.getUserDivision() + "]");
		logger.info("User Branch Division = [" + profileData.getBranchDivision() + "]");

		String sNewTower = profile.getPG_CODISA_INQ();
		String sNewBranch = profile.getPG_COD_SPORT_CONT();

		String localForward = "ok";

		if (request.getServerName().indexOf("sportello") == -1 &
			request.getServerName().indexOf("gd.validazione") == -1 &
			request.getServerName().indexOf("gc.validazione") == -1) {
			localForward = "okCollaudo";
		}
		
		String path = mapping.findForward(localForward).getPath();
		
		if ("AC".equalsIgnoreCase(userData.getCodOperatore().substring(0,2)) ||
			"A3".equalsIgnoreCase(userData.getCodOperatore().substring(0,2)) ) {

			if (sNewTower == null || "".equals(sNewTower)) {

				if ("AC".equalsIgnoreCase(userData.getCodOperatore().substring(0,2))) {
					path = path.replaceFirst("var_banca", "25");
				}
				else if ("A3".equalsIgnoreCase(userData.getCodOperatore().substring(0,2))) {
					path = path.replaceFirst("var_banca", "13");
				}
				
			}
			else {
				GwcDelegate dlg = new GwcDelegate();
				BanksList banks = dlg.getBanks();
				BankData bank = banks.getBankFromTowerKey(sNewTower);
				
					path = path.replaceFirst("var_banca", profile.getPG_COD_IST());
				
			}

			if (sNewBranch == null || "".equals(sNewBranch)) {

				path = path.replaceFirst("var_sportello", "99999");
			}
			else {
				path = path.replaceFirst("var_sportello", sNewBranch.trim());
			}
		}
		else {
			path = path.replaceFirst("var_banca", profile.getPG_COD_IST());
			
			path = path.replaceFirst("var_sportello", profile.getPG_COD_SPORT_CONT());
		}
		
		path = path.replaceFirst("var_user", userData.getCodOperatore());
		String sProfileTxt = profile.getPG_COD_AUTHOR();
		if (sProfileTxt != null) {
			sProfileTxt = sProfileTxt.trim();
		}
		path = path.replaceFirst("var_profilo", sProfileTxt);
		if(request.getServerName().indexOf("gd") > -1 ||
			request.getServerName().indexOf("gc") > -1){
			path = path.replaceFirst("var_user_div", "CM");
		}else{
			path = path.replaceFirst("var_user_div", profileData.getUserDivision());
		}
		path = path.replaceFirst("var_branch_div", profileData.getBranchDivision());

		logger.info("executing " + this.getClass() + " path = [" + path + "]");
		
		ActionForward forward = new ActionForward();
		
		forward.setName(mapping.findForward(localForward).getName());
		forward.setRedirect(mapping.findForward(localForward).getRedirect());
		forward.setPath(path);

		return forward;		
	}
	
}
