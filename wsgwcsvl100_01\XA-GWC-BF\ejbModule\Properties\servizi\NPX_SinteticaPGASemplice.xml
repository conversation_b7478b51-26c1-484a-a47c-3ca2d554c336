<?xml version="1.0"?>
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="X01" />
                    <param name="X02" />
                    <param name="X03" />
                    <param name="X04" />
                    <param name="X05" />
                    <param name="X07" />
                    <param name="X97" />
                    <param name="X98" />
                </input>
                <output>
                    <xsl:stylesheet version="1.0" 
                        xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request" >
                        <xsl:output method="xml" />
                        <xsl:template match="xgen:request" >
                            <xsl:copy>
                                <xsl:apply-templates select="@*" />
                                <xsl:apply-templates select="*" />
                            </xsl:copy>
                        </xsl:template>
                        <xsl:template match="xgen:param" >
                            <xsl:copy>
                                <xsl:choose>
                                    <xsl:when test="substring(@name, 6, 1) = 'N' and string(text())=''" >
                                        <xsl:attribute name="name" >
                                            <xsl:value-of select="@name" />
                                        </xsl:attribute>
                                        <xsl:text>0</xsl:text>
                                    </xsl:when>
                                    <xsl:when test="substring(@name, 6, 1) = 'X' and string(text())=''" >
                                        <xsl:attribute name="name" >
                                            <xsl:value-of select="@name" />
                                        </xsl:attribute>
                                        <xsl:value-of select="' '" />
                                    </xsl:when>
                                    <xsl:otherwise>
                                        <xsl:apply-templates select="*|@*|comment()|processing-instruction()|text()" />
                                    </xsl:otherwise>
                                </xsl:choose>
                            </xsl:copy>
                        </xsl:template>
                        <xsl:template match="*|@*|comment()|processing-instruction()|text()" >
                            <xsl:copy>
                                <xsl:apply-templates select="*|@*|comment()|processing-instruction()|text()" />
                            </xsl:copy>
                        </xsl:template>
                    </xsl:stylesheet>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NRussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="GLA8P027"/>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>GL40</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>GL40</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>