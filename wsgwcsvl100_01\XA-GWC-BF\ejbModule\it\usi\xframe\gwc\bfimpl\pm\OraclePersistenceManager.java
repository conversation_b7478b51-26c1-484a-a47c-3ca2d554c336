package it.usi.xframe.gwc.bfimpl.pm;

import it.usi.xframe.gwc.bfutil.UCF_Table_Records;
import it.usi.xframe.gwc.bfutil.params.Oracle_row_params;
import it.usi.xframe.gwc.bfutil.vo.Field_Detail;
import it.usi.xframe.gwc.bfutil.vo.Generic_Record;
import it.usi.xframe.system.bfutil.db.DBConnectionFactory;
import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.system.errors.XFRRTCodedException;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.naming.NamingException;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;



public class OraclePersistenceManager extends PersistenceManager {
	
	private static final String UCF_DB = "jdbc/UCF_oracleds";
	
	private static OraclePersistenceManager instance = null;
	
	/**	Logger for debugging */
	private Log logger = LogFactory.getLog(this.getClass());
			
	private OraclePersistenceManager() 
	{}

	/**
	* @return singleton
	*/
	public static synchronized OraclePersistenceManager getInstance() {
		if (instance == null)
			instance = new OraclePersistenceManager();
		
		return instance;
	}

	protected Connection getConnection() throws XFRException {
		Connection conn = null;
		try {
			conn = DBConnectionFactory.getInstance().retrieveConnection(UCF_DB);
//			logger.info("Db connected::" + UCF_DB);
		}
		catch(NamingException e) {
			throw new XFRException(e);
		}
		catch(SQLException e) {
			throw new XFRException(e);
		}
		return conn;
	}

	protected DataValue load(Map record) {
		return new DataValue(((String) record.get("NAME")).trim(),(String) record.get("DATA_LENGTH"));
	}
	
	public boolean do_copy(String owner, UCF_Table_Records source) throws XFRException
	{
		Iterator tables_iter = source.getTable_records().keySet().iterator();
		while(tables_iter.hasNext())
		{
			String current_table = (String) tables_iter.next();
			Collection current_table_columns = (Collection) source.getTable_columns().get(current_table);
			Collection current_table_records = (Collection) source.getTable_records().get(current_table);
			Collection target_table_columns = get_colums_for(owner,current_table);
			
			// Build an hashmap where hm[name]=filed_length
			HashMap target_table_columns_hm = new HashMap();
			Iterator iter4 = target_table_columns.iterator();
			while(iter4.hasNext())
			{
				DataValue dv = (DataValue) iter4.next();
				target_table_columns_hm.put(dv.getCode(),new Integer(Integer.parseInt(dv.getDescription())) );
			}
			
			// Test if target table exists
			if (target_table_columns.size()==0)
				throw new XFRRTCodedException("La tabella "+current_table+" non esiste.");
				
			// Test if source #colums corresponds to target #columns
			if (current_table_columns.size() != target_table_columns.size())
				throw new XFRRTCodedException("Il numero di campi della tabella ["+current_table+
					"] sorgente non corrisponde a quelli della tabella ["+
					current_table+"] destinazione ("+current_table_columns.size()+
					":"+target_table_columns.size()+").");
			
			// Empty target table
			delete_table(owner,current_table);			
			
			// foreach record
			int counter = 0;
			Iterator iter2 = current_table_records.iterator();
			while(iter2.hasNext())
			{					
				Oracle_row_params par = new Oracle_row_params();
				par.setOwner(owner);
				par.setTable(current_table);
				
				HashMap hm = new HashMap();
				Map gr = ((Generic_Record) iter2.next()).getFields();
					
				// Foreach field
				Iterator iter3 = current_table_columns.iterator();
				while(iter3.hasNext())
				{
					Field_Detail fd = (Field_Detail) iter3.next();
					HashMap field = new HashMap();
					field.put(Oracle_row_params.TYPE,fd.getType());
					field.put(Oracle_row_params.VALUE, (String) gr.get(fd.getName()) );
					field.put(Oracle_row_params.LENGTH, target_table_columns_hm.get(fd.getName()) );
					hm.put(fd.getName(),field);
				}
				par.setFields(hm);
					
				// Insert the row just created
				boolean insert_res = insert_row(par);
					
				logger.info("[NPF table copier] \t\tRecord["+counter+"] written to table [" + current_table + "].");
					
				if (!insert_res) 
					throw new XFRRTCodedException("Inserimento in tabella ["+current_table+"] fallito al record " + counter + ".");
			
				counter++;
			}			
				
			logger.info("[NPF table copier] \tTable [" + current_table + "] copied.");			
		}	
		
		return true;				
	}
		
	private Collection get_colums_for(String owner,String table) throws XFRException {

		String query = "SELECT COLUMN_NAME AS NAME, DATA_LENGTH FROM ALL_TAB_COLUMNS WHERE TABLE_NAME='"+table+"' AND OWNER='"+owner+"' ORDER BY NAME ASC";
		logger.info("[Oracle Manager] Query = #"+query+"#");
		setSqlList(query);
		List dvl = (List) executeList(new PersistenceManager.SqlParam[0]);
		logger.info("[Oracle Manager] Records = #"+dvl.size()+"#");
		
		return dvl;
	}

	// Deletes all record from the table arguments
	private boolean delete_table(String owner, String table) throws XFRException {

		String query = "DELETE FROM " + owner + "." + table;
		logger.info("[Oracle Manager] Query = #"+query+"#");
		setSqlDelete(query);
		logger.info("[Oracle Manager] Done");
		executeDelete(new PersistenceManager.SqlParam[0]);
		return true;
	}

	// resultset by TABLE_NAME from ALL_TAB_COLUMNS 
	private boolean insert_row(Oracle_row_params params) throws XFRException {

		String insert_query =
			"INSERT INTO "
				+ params.getOwner()
				+ "."
				+ params.getTable()
				+ " (";

		int counter = 0;
		Iterator iter = params.getFields().keySet().iterator();

		while(iter.hasNext())
		{
			String key = (String) iter.next();
			insert_query += ((counter == 0) ? "" : ",") + key;
			counter++;
		}

		insert_query += ") VALUES (";

		iter = params.getFields().keySet().iterator();
		counter=0;
		while(iter.hasNext())
		{
			String key = (String) iter.next();
			HashMap obj = (HashMap) params.getFields().get(key);
			String tipo = (String) obj.get(Oracle_row_params.TYPE);
			String value = (String) obj.get(Oracle_row_params.VALUE);
			int len = ((Integer) obj.get(Oracle_row_params.LENGTH)).intValue();
			
			if (counter > 0) insert_query += ",";
		
			// gestione tipi                        
			if (tipo.trim().equals("CHAR")
				|| (tipo.trim().equals("VARCHAR"))) {
		
				if (value != null) {
					String buffer1 = value.replaceAll("'", "''");
					if (buffer1.length()>len) buffer1 = buffer1.substring(0,len);
					insert_query += "'" + buffer1 + "'";
				} else
					insert_query += "NULL";
		
			} else if (tipo.trim().equals("DATE")) {
		
				if (value != null)
					insert_query += "TO_DATE('"
						+ value
						+ "','yyyy-mm-dd')";
				else
					insert_query += "NULL";
			} else {
				insert_query += value;
			}			
			
			counter++;
		}

		insert_query += ")";

		setSqlInsert(insert_query);
		int out = executeInsert(new PersistenceManager.SqlParam[0]);
		if (out>0) return true;			
			
	    return false;
	}
}