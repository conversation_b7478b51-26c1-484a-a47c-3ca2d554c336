/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPR_SEM_Zivno {

	//	Default Constructor - RSA8 migration prerequisites	
	public NPR_SEM_Zivno(){
			
	}		

	public String nprSemZivno(String n001, String n002, String x01, String n003, String x02, String n004, String x03, String n005, String x04, String n006, String x05, String n007, String x06, String n008, 
							String x07, String n009, String x08, String n010, String x09, String n011, String x10, String n012, String x11, String n013, String x12, String n014, String x13, String n015,
							String n016, String n017, String x14, String x15, String x16, String x17, String n018, String n019, String n020, String n021, String n022, String x18, String x19, String n023,
							String x20, String x21, String n024, String x22, String x23, String n025, String x24, String n026, String n027, String n028, String x25, String x26, String x27, String x28, String x29,
							String x30, String n029, String x31, String n030) throws Exception { 

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().nprSemZivno(n001, n002, x01, n003, x02, n004, x03, n005, x04, n006, x05, n007, x06, n008, x07, n009, x08, n010, x09, n011, x10, n012, x11, n013, x12, n014, x13, n015,
																					 n016, n017, x14, x15, x16, x17, n018, n019, n020, n021, n022, x18, x19, n023, x20, x21, n024, x22, x23, n025, x24, n026, n027, n028, x25, x26, x27, x28, x29, x30, n029, x31, n030);
	}     
}
