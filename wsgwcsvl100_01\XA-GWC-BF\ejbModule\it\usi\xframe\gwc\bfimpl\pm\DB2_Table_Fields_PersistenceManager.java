/*
 * Created on June 4, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfimpl.pm;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import it.usi.xframe.gwc.bfutil.vo.Field_Detail;
import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */




public class DB2_Table_Fields_PersistenceManager extends PersistenceManager {
	
	private static DB2_Table_Fields_PersistenceManager instance = null;
	
	/**	Logger for debugging */
	private Log logger = LogFactory.getLog(this.getClass());
			
	private DB2_Table_Fields_PersistenceManager() 
	{ 
	}			
	
	/**
	* @return singleton
	*/
	public static synchronized DB2_Table_Fields_PersistenceManager getInstance() {

		if (instance == null)
			instance = new DB2_Table_Fields_PersistenceManager();
			
		return instance;
	}
	
	protected DataValue load(Map record) {
		Field_Detail fd = new Field_Detail();
		fd.setName(((String) record.get("NAME")).trim());
		fd.setType(((String) record.get("COLTYPE")).trim());
		return fd;
	}

	public Collection get_colums_for(String owner,String table) throws XFRException 
	{
		String query = "SELECT NAME, COLTYPE FROM SYSIBM.SYSCOLUMNS WHERE TBNAME=? AND TBCREATOR=? ORDER BY name ASC";
						
		PersistenceManager.SqlParam[] params = new PersistenceManager.SqlParam[2];						
		params[0] = sqlParamString(table);
		params[1] = sqlParamString(owner);

		setSqlList(query);

										
		List dvl = (List) executeList(params);

		logger.info(query);		
		logger.info("Owner = #"+owner+"#");
		logger.info("Name = #"+ table+"#");
		logger.info("Fields Count = #"+ dvl.size() +"#");
		
		return dvl;	
	}	

	
}