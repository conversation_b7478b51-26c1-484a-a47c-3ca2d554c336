<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="SERVIZIO"/>                 
                    <param name="VERSIONE"/>
                    <param name="COD_BAN"/>
                    <param name="COD_BAN_SERV"/>
                    <param name="SARI_TIPO_DOC"/>
                    <param name="SARI_ID_PROPOSAL"/>
                    <param name="SARI_GROUP_SNDG"/>
                    <param name="SARI_UCG_SUBGROUP"/>                 
                    <param name="XF_GAUSS_ID"/>
                    
                    <param name="PAGI_LINE"/>
                    <param name="PAGI_PAGE"/>
			    </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService/>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0001</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>WBF0</transaction>
            <program>BFI_STA_ANNUAL_REVIEW_3</program>
            <timeout>30000</timeout>
            <applid>CICS</applid>
            <synclevel>0</synclevel>
        </params>
    </channel>
</service>
