<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprListaFidi">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprListaFidiResponse">
    <complexType>
     <sequence>
      <element name="nprListaFidiReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprListaFidiResponse">

      <wsdl:part element="impl:nprListaFidiResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprListaFidiRequest">

      <wsdl:part element="impl:nprListaFidi" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_ListaFidi_SEI">

      <wsdl:operation name="nprListaFidi">

         <wsdl:input message="impl:nprListaFidiRequest" name="nprListaFidiRequest"/>

         <wsdl:output message="impl:nprListaFidiResponse" name="nprListaFidiResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_ListaFidiSoapBinding" type="impl:NPR_ListaFidi_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprListaFidi">

         <wsdlsoap:operation soapAction="nprListaFidi"/>

         <wsdl:input name="nprListaFidiRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprListaFidiResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_ListaFidiService">

      <wsdl:port binding="impl:NPR_ListaFidiSoapBinding" name="NPR_ListaFidi">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_ListaFidi"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
