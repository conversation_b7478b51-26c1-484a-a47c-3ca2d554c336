<?xml version="1.0"?>
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="internet">
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="PI70_X01"/>
                    <param name="PI70_X02"/>
                    <param name="PI70_X03"/>
                    <param name="PI70_X04"/>
                    <param name="PI70_X05"/>
                    <param name="PI70_X06"/>
                    <param name="PI70_N001"/>
                    <param name="PI70_N002"/>
                    <param name="PI70_N003"/>
                    <param name="PI70_N004"/>
                    <param name="PI70_N005"/>
                    <param name="PI70_N006"/>
                    <param name="PI70_N007"/>
                    <param name="PI70_N008"/>
                    <param name="PI70_N009"/>
                    <param name="PI70_N010"/>
                    <param name="PI70_N011"/>
                    <param name="PI70_N012"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NRussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="PI70I070">
                        <param select="PI70_X01" name="X01"/>
                        <param select="PI70_X02" name="X02"/>
                        <param select="PI70_X03" name="X03"/>
                        <param select="PI70_X04" name="X04"/>
                        <param select="PI70_X05" name="X05"/>
                        <param select="PI70_X06" name="X06"/>
                        <param select="PI70_N001" name="N001"/>
                        <param select="PI70_N002" name="N002"/>
                        <param select="PI70_N003" name="N003"/>
                        <param select="PI70_N004" name="N004"/>
                        <param select="PI70_N005" name="N005"/>
                        <param select="PI70_N006" name="N006"/>
                        <param select="PI70_N007" name="N007"/>
                        <param select="PI70_N008" name="N008"/>
                        <param select="PI70_N009" name="N009"/>
                        <param select="PI70_N010" name="N010"/>
                        <param select="PI70_N011" name="N011"/>
                        <param select="PI70_N012" name="N012"/>
                    </hostService>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>PIIL</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>PIIL</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
