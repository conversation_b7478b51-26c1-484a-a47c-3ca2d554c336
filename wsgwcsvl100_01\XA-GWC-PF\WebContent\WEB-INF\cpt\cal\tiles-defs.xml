<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE tiles-definitions PUBLIC "-//Apache Software Foundation//DTD Tiles Configuration//EN" "http://jakarta.apache.org/struts/dtds/tiles-config.dtd">
<tiles-definitions>
<!-- 
=======================================================
Tiles definitions of SuperClient components

Message Bundle key used:

cpt.cal.error.calendar		The error message on missing paramters
-->

  <!-- ************************ Complex table extensions ************************** -->


	<definition name="cpt.cal.calendar.head" path="/cpt/cal/calendarHead.jsp"/>
	<definition name="cpt.cal.calendar.base">
		<put name="ifFormat" type="string" value=""/>
		<put name="daFormat" type="string" value=""/>
		<put name="dateStatusFunc" type="string" value=""/>
		<put name="firstDay" type="string" value=""/>
		<put name="weekNumbers" type="string" value=""/>
		<put name="align" type="string" value=""/>
		<put name="range" type="string" value=""/>
		<put name="date" type="string" value=""/>
		<put name="showsTime" type="string" value=""/>
		<put name="timeFormat" type="string" value=""/>
		<put name="electric" type="string" value=""/>
		<put name="position" type="string" value=""/>
		<put name="cache" type="string" value=""/>
		<put name="showOthers" type="string" value=""/>
		<put name="onSelect" type="string" value=""/>
	</definition>
	<definition name="cpt.cal.calendar.test.base" path="/cpt/cal/calendarTestBase.jsp" extends="cpt.cal.calendar.base"/>
	<definition name="cpt.cal.calendar.popup" path="/cpt/cal/calendarPopup.jsp" extends="cpt.cal.calendar.base">
		<put name="inputField" type="string" value=""/>
		<put name="button" type="string" value=""/>
		<put name="displayArea" type="string" value=""/>
		<put name="eventName" type="string" value=""/>
		<put name="singleClick" type="string" value=""/>
		<put name="onClose" type="string" value=""/>
	</definition>
	<definition name="cpt.cal.calendar.flat" path="/cpt/cal/calendarFlat.jsp" extends="cpt.cal.calendar.base">
		<put name="flat" type="string" value=""/>
		<put name="flatCallback" type="string" value=""/>
		<put name="flatCallbackScript" type="string" value=""/>
	</definition>	
</tiles-definitions>
