package it.usi.xframe.gwc.pfstruts.actions;

import it.usi.xframe.gwc.wsutil.CallServiceProxy;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class TestCallService extends TestCallServiceBase {

	private Log logger = LogFactory.getLog(this.getClass());

	// Execute Implementation
	public ActionForward execute(
		ActionMapping mapping,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {

		logger.info("executing = [" + this.getClass() + "]");

		request.getSession().setAttribute("testMode", "PASSWORD");
		
		String endPoint = getLocatorString(request, "testCallService.do", "CallService");		
		logger.info(this.getClass() + " Web Service URL = " + endPoint);

		CallServiceProxy proxy = new CallServiceProxy();
		proxy.setEndpoint(endPoint);
		
		request.getSession().setAttribute("CallServiceProxyid", proxy);
		
		return mapping.findForward("ok");
	}
	
}
