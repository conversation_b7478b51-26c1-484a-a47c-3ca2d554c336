package it.usi.xframe.gwc.wsutil.dto;

import java.io.Serializable;

public class ExperianX030OutputMap implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String requestsNumber = "";
	private String valTotRich = "";
	private String refusedRequestsNumber = "";
	private String waivedRequestsNumber = "";
	private String acceptedRequestsNumber = "";
	private String numContratti = "";
	private String impegnoMensileRate = "";
	private String saldoTotRateChiro = "";
	private String saldoTotIpotec = "";
	private String peggStatoUlt12Mesi = "";
	private String peggStatoAnteUlt12Mesi = "";
	private String peggStatoAccens = "";
	private String rateMoroseMagg7 = "";
	private String rateMoroseDa3A6 = "";
	private String rateMoroseDa1A2 = "";
	private String delphiScore = "";
	private String gruppoDiRischio = "";

	public ExperianX030OutputMap() {
	}

	public String getRequestsNumber() {
		return requestsNumber;
	}

	public void setRequestsNumber(String requestsNumber) {
		this.requestsNumber = requestsNumber;
	}

	public String getValTotRich() {
		return valTotRich;
	}

	public void setValTotRich(String valTotRich) {
		this.valTotRich = valTotRich;
	}

	public String getRefusedRequestsNumber() {
		return refusedRequestsNumber;
	}

	public void setRefusedRequestsNumber(String refusedRequestsNumber) {
		this.refusedRequestsNumber = refusedRequestsNumber;
	}

	public String getWaivedRequestsNumber() {
		return waivedRequestsNumber;
	}

	public void setWaivedRequestsNumber(String waivedRequestsNumber) {
		this.waivedRequestsNumber = waivedRequestsNumber;
	}

	public String getAcceptedRequestsNumber() {
		return acceptedRequestsNumber;
	}

	public void setAcceptedRequestsNumber(String acceptedRequestsNumber) {
		this.acceptedRequestsNumber = acceptedRequestsNumber;
	}

	public String getNumContratti() {
		return numContratti;
	}

	public void setNumContratti(String numContratti) {
		this.numContratti = numContratti;
	}

	public String getImpegnoMensileRate() {
		return impegnoMensileRate;
	}

	public void setImpegnoMensileRate(String impegnoMensileRate) {
		this.impegnoMensileRate = impegnoMensileRate;
	}

	public String getSaldoTotRateChiro() {
		return saldoTotRateChiro;
	}

	public void setSaldoTotRateChiro(String saldoTotRateChiro) {
		this.saldoTotRateChiro = saldoTotRateChiro;
	}

	public String getSaldoTotIpotec() {
		return saldoTotIpotec;
	}

	public void setSaldoTotIpotec(String saldoTotIpotec) {
		this.saldoTotIpotec = saldoTotIpotec;
	}

	public String getPeggStatoUlt12Mesi() {
		return peggStatoUlt12Mesi;
	}

	public void setPeggStatoUlt12Mesi(String peggStatoUlt12Mesi) {
		this.peggStatoUlt12Mesi = peggStatoUlt12Mesi;
	}

	public String getPeggStatoAnteUlt12Mesi() {
		return peggStatoAnteUlt12Mesi;
	}

	public void setPeggStatoAnteUlt12Mesi(String peggStatoAnteUlt12Mesi) {
		this.peggStatoAnteUlt12Mesi = peggStatoAnteUlt12Mesi;
	}

	public String getPeggStatoAccens() {
		return peggStatoAccens;
	}

	public void setPeggStatoAccens(String peggStatoAccens) {
		this.peggStatoAccens = peggStatoAccens;
	}

	public String getRateMoroseMagg7() {
		return rateMoroseMagg7;
	}

	public void setRateMoroseMagg7(String rateMoroseMagg7) {
		this.rateMoroseMagg7 = rateMoroseMagg7;
	}

	public String getRateMoroseDa3A6() {
		return rateMoroseDa3A6;
	}

	public void setRateMoroseDa3A6(String rateMoroseDa3A6) {
		this.rateMoroseDa3A6 = rateMoroseDa3A6;
	}

	public String getRateMoroseDa1A2() {
		return rateMoroseDa1A2;
	}

	public void setRateMoroseDa1A2(String rateMoroseDa1A2) {
		this.rateMoroseDa1A2 = rateMoroseDa1A2;
	}

	public String getDelphiScore() {
		return delphiScore;
	}

	public void setDelphiScore(String delphiScore) {
		this.delphiScore = delphiScore;
	}

	public String getGruppoDiRischio() {
		return gruppoDiRischio;
	}

	public void setGruppoDiRischio(String gruppoDiRischio) {
		this.gruppoDiRischio = gruppoDiRischio;
	}
}
