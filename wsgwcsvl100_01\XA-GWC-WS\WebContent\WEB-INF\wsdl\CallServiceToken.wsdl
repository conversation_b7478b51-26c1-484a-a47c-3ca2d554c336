<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="callServiceToken">
    <complexType>
     <sequence>
      <element name="service" nillable="true" type="xsd:string"/>
      <element name="params" nillable="true" type="xsd:string"/>
      <element name="sep1" nillable="true" type="xsd:string"/>
      <element name="sep2" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="callServiceTokenResponse">
    <complexType>
     <sequence>
      <element name="callServiceTokenReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="callServiceTokenResponse">

      <wsdl:part element="impl:callServiceTokenResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="callServiceTokenRequest">

      <wsdl:part element="impl:callServiceToken" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="CallServiceToken_SEI">

      <wsdl:operation name="callServiceToken">

         <wsdl:input message="impl:callServiceTokenRequest" name="callServiceTokenRequest"/>

         <wsdl:output message="impl:callServiceTokenResponse" name="callServiceTokenResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="CallServiceTokenSoapBinding" type="impl:CallServiceToken_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="callServiceToken">

         <wsdlsoap:operation soapAction="callServiceToken"/>

         <wsdl:input name="callServiceTokenRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="callServiceTokenResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="CallServiceTokenService">

      <wsdl:port binding="impl:CallServiceTokenSoapBinding" name="CallServiceToken">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/CallServiceToken"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
