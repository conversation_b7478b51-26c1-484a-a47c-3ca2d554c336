<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Inquiry_DatoVario">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_Inquiry_DatoVarioResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_Inquiry_DatoVarioReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_Inquiry_DatoVarioResponse">

      <wsdl:part element="impl:nprFidi_Inquiry_DatoVarioResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_Inquiry_DatoVarioRequest">

      <wsdl:part element="impl:nprFidi_Inquiry_DatoVario" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Inquiry_DatoVario_SEI">

      <wsdl:operation name="nprFidi_Inquiry_DatoVario">

         <wsdl:input message="impl:nprFidi_Inquiry_DatoVarioRequest" name="nprFidi_Inquiry_DatoVarioRequest"/>

         <wsdl:output message="impl:nprFidi_Inquiry_DatoVarioResponse" name="nprFidi_Inquiry_DatoVarioResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_Inquiry_DatoVarioSoapBinding" type="impl:NPR_Fidi_Inquiry_DatoVario_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Inquiry_DatoVario">

         <wsdlsoap:operation soapAction="nprFidi_Inquiry_DatoVario"/>

         <wsdl:input name="nprFidi_Inquiry_DatoVarioRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_Inquiry_DatoVarioResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_Inquiry_DatoVarioService">

      <wsdl:port binding="impl:NPR_Fidi_Inquiry_DatoVarioSoapBinding" name="NPR_Fidi_Inquiry_DatoVario">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Inquiry_DatoVario"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
