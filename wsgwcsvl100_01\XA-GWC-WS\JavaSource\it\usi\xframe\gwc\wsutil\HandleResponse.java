/**
 * HandleResponse.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * cf190823.02 v62608112801
 */

package it.usi.xframe.gwc.wsutil;

public class HandleResponse  implements java.io.Serializable {
    private java.lang.Object messageContext;

    public HandleResponse() {
    }

    public java.lang.Object getMessageContext() {
        return messageContext;
    }

    public void setMessageContext(java.lang.Object messageContext) {
        this.messageContext = messageContext;
    }

    private transient java.lang.ThreadLocal __history;
    public boolean equals(java.lang.Object obj) {
        if (obj == null) { return false; }
        if (obj.getClass() != this.getClass()) { return false;}
        HandleResponse other = (HandleResponse) obj;
        boolean _equals;
        _equals = true
            && ((this.messageContext==null && other.getMessageContext()==null) || 
             (this.messageContext!=null &&
              this.messageContext.equals(other.getMessageContext())));
        if (!_equals) { return false; }
        if (__history == null) {
            synchronized (this) {
                if (__history == null) {
                    __history = new java.lang.ThreadLocal();
                }
            }
        }
        HandleResponse history = (HandleResponse) __history.get();
        if (history != null) { return (history == obj); }
        if (this == obj) return true;
        __history.set(obj);
        __history.set(null);
        return true;
    }

    private transient java.lang.ThreadLocal __hashHistory;
    public int hashCode() {
        if (__hashHistory == null) {
            synchronized (this) {
                if (__hashHistory == null) {
                    __hashHistory = new java.lang.ThreadLocal();
                }
            }
        }
        HandleResponse history = (HandleResponse) __hashHistory.get();
        if (history != null) { return 0; }
        __hashHistory.set(this);
        int _hashCode = 1;
        if (getMessageContext() != null) {
            _hashCode += getMessageContext().hashCode();
        }
        __hashHistory.set(null);
        return _hashCode;
    }

}
