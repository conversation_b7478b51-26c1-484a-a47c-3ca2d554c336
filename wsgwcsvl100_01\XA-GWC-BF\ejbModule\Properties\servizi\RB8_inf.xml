<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R83I-PROPOSAL-ID"/>
					<param name="R83I-FUNCTION"/>
					<param name="R83I-FLAG-ENV"/>
					<param name="R83I-INF-STATUS"/>
					<param name="R83I-USER-ID"/>
					<param name="R83I-AUTH-LEV"/>
					<param name="R83I-CHECK-1-A"/>
					<param name="R83I-CHECK-1-M"/>
					<param name="R83I-CHECK-2-A"/>
					<param name="R83I-CHECK-2-M"/>
					<param name="R83I-CHECK-3-A"/>
					<param name="R83I-CHECK-3-M"/>
					<param name="R83I-COD-CNT-M"/>
					<param name="R83I-COD-INDUSTRY-M"/>
					<param name="R83I-NACE-CODE-M"/>
					<param name="R83I-FITCH-CP-M"/>
					<param name="R83I-MOODY-CP-M"/>
					<param name="R83I-SANDP-CP-M"/>
					<param name="R83I-CNT-NAME-M"/>
					<param name="R83I-NACE-DESCR-M"/>
					<param name="R83I-FITCH-CP-M-RAT"/>
					<param name="R83I-MOODY-CP-M-RAT"/>
					<param name="R83I-SANDP-CP-M-RAT"/>
			    </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB83-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB83</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
