package it.usi.xframe.gwc.wsutil;

public class CallServiceFreeProxy implements it.usi.xframe.gwc.wsutil.CallServiceFree {
  private boolean _useJNDI = true;
  private String _endpoint = null;
  private it.usi.xframe.gwc.wsutil.CallServiceFree callServiceFree = null;
  
  public CallServiceFreeProxy() {
    _initCallServiceFreeProxy();
  }
  
  private void _initCallServiceFreeProxy() {
  
  if (_useJNDI) {
    try{
      javax.naming.InitialContext ctx = new javax.naming.InitialContext();
      callServiceFree = ((it.usi.xframe.gwc.wsutil.CallServiceFreeService)ctx.lookup("java:comp/env/service/CallServiceFreeService")).getCallServiceFree();
      }
    catch (javax.naming.NamingException namingException) {}
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  if (callServiceFree == null) {
    try{
      callServiceFree = (new it.usi.xframe.gwc.wsutil.CallServiceFreeServiceLocator()).getCallServiceFree();
      }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  if (callServiceFree != null) {
    if (_endpoint != null)
      ((javax.xml.rpc.Stub)callServiceFree)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    else
      _endpoint = (String)((javax.xml.rpc.Stub)callServiceFree)._getProperty("javax.xml.rpc.service.endpoint.address");
  }
  
}


public void useJNDI(boolean useJNDI) {
  _useJNDI = useJNDI;
  callServiceFree = null;
  
}

public String getEndpoint() {
  return _endpoint;
}

public void setEndpoint(String endpoint) {
  _endpoint = endpoint;
  if (callServiceFree != null)
    ((javax.xml.rpc.Stub)callServiceFree)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
  
}

public it.usi.xframe.gwc.wsutil.CallServiceFree getCallServiceFree() {
  if (callServiceFree == null)
    _initCallServiceFreeProxy();
  return callServiceFree;
}

public java.lang.String callServiceFreePWD(java.lang.String service, java.lang.String params, java.lang.String sep1, java.lang.String sep2, String userid) throws java.rmi.RemoteException{
  if (callServiceFree == null)
  _initCallServiceFreeProxy();
    
  ((javax.xml.rpc.Stub)callServiceFree)._setProperty("userid", userid);
  
  return this.callServiceFree(service, params, sep1, sep2);
}

public java.lang.String callServiceFree(java.lang.String service, java.lang.String params, java.lang.String sep1, java.lang.String sep2) throws java.rmi.RemoteException{
  if (callServiceFree == null)
    _initCallServiceFreeProxy();
  return callServiceFree.callServiceFree(service, params, sep1, sep2);
}


}