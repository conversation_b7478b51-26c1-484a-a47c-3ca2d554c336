<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="3.3.192" utente="tommasi" Timestamp="04/03/2002 15.06.22" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="UANK06CE-STATUS-AGG"/>
					<param name="UANK06CE-TELEFONI-AGG"/>
					<param name="UANK06CE-INDIR-CORR-AGG"/>
					<param name="UANK06CE-ALTRI-DATI-AGG"/>
					<param name="UANK06CE-CODICI-VARI-AGG"/>
					<param name="UANK06CE-SEDE-FISCALE-AGG"/>
					<param name="UANK06CE-DATI-PRIVACY-AGG"/>
					<param name="UANK06CE-DATI-GENERALI-AGG"/>
					<param name="UANK06CE-DATI-AZIENDALI-AGG"/>
					<param name="UANK06CE-PROFESSIONI-ALT-AGG"/>
					<param name="UANK06CE-INTESTAZIONE-LONG-AGG"/>
					<param name="UANK06CE-CLASSIFICAZIONE-ALT-AG"/>
					<param name="UANK06CE-NDG"/>
					<param name="UANK06CE-CONTROLLO-INT"/>
					<param name="UANK06CE-CODICE-FISCALE"/>
					<param name="UANK06CE-PARTITA-IVA"/>
					<param name="UANK06CE-SPORTELLO-CAPOFILA"/>
					<param name="UANK06CE-INTESTAZIONE-A"/>
					<param name="UANK06CE-INTESTAZIONE-B"/>
					<param name="UANK06CE-TIPO-NDG"/>
					<param name="UANK06CE-SETTORISTA"/>
					<param name="UANK06CE-PSEUDONIMO"/>
					<param name="UANK06CE-STATO-RES"/>
					<param name="UANK06CE-COMUNE-RES"/>
					<param name="UANK06CE-LOCALITA-RES"/>
					<param name="UANK06CE-PROVINCIA-RES"/>
					<param name="UANK06CE-CAP-RES"/>
					<param name="UANK06CE-VIA-RES"/>
					<param name="UANK06CE-PRESSO-RES"/>
					<param name="UANK06CE-INTESTAZIONE-CORR"/>
					<param name="UANK06CE-STATO-CORR"/>
					<param name="UANK06CE-COMUNE-CORR"/>
					<param name="UANK06CE-LOCALITA-CORR"/>
					<param name="UANK06CE-PROVINCIA-CORR"/>
					<param name="UANK06CE-CAP-CORR"/>
					<param name="UANK06CE-VIA-CORR"/>
					<param name="UANK06CE-PRESSO-CORR"/>
					<param name="UANK06CE-UFFICIO-CORR"/>
					<param name="UANK06CE-CASELLARIO-CORR"/>
					<param name="UANK06CE-CODICE-FISCALE-ESTERO"/>
					<param name="UANK06CE-RAMO"/>
					<param name="UANK06CE-SETTORE"/>
					<param name="UANK06CE-RAMO-ALT"/>
					<param name="UANK06CE-SETTORE-ALT"/>
					<param name="UANK06CE-COMUNE-ALT"/>
					<param name="UANK06CE-PROVINCIA-ALT"/>
					<param name="UANK06CE-STATO-ALT"/>
					<param name="UANK06CE-SEGMENTO"/>
					<param name="UANK06CE-SIT-GIURIDICA"/>
					<param name="UANK06CE-STATUS"/>
					<param name="UANK06CE-UBIC-DOC-RIFERIM"/>
					<param name="UANK06CE-UBIC-DOC-SPORTELLO"/>
					<param name="UANK06CE-TEL-OPERAZIONE-1"/>
					<param name="UANK06CE-TEL-PROGR-1"/>
					<param name="UANK06CE-TIPO-TELEFONO-1"/>
					<param name="UANK06CE-NUMERO-TELEFONO-1"/>
					<param name="UANK06CE-TEL-OPERAZIONE-2"/>
					<param name="UANK06CE-TEL-PROGR-2"/>
					<param name="UANK06CE-TIPO-TELEFONO-2"/>
					<param name="UANK06CE-NUMERO-TELEFONO-2"/>
					<param name="UANK06CE-TEL-OPERAZIONE-3"/>
					<param name="UANK06CE-TEL-PROGR-3"/>
					<param name="UANK06CE-TIPO-TELEFONO-3"/>
					<param name="UANK06CE-NUMERO-TELEFONO-3"/>
					<param name="UANK06CE-TEL-OPERAZIONE-4"/>
					<param name="UANK06CE-TEL-PROGR-4"/>
					<param name="UANK06CE-TIPO-TELEFONO-4"/>
					<param name="UANK06CE-NUMERO-TELEFONO-4"/>
					<param name="UANK06CE-TEL-OPERAZIONE-5"/>
					<param name="UANK06CE-TEL-PROGR-5"/>
					<param name="UANK06CE-TIPO-TELEFONO-5"/>
					<param name="UANK06CE-NUMERO-TELEFONO-5"/>
					<param name="UANK06CE-TEL-OPERAZIONE-6"/>
					<param name="UANK06CE-TEL-PROGR-6"/>
					<param name="UANK06CE-TIPO-TELEFONO-6"/>
					<param name="UANK06CE-NUMERO-TELEFONO-6"/>
					<param name="UANK06CE-TEL-OPERAZIONE-7"/>
					<param name="UANK06CE-TEL-PROGR-7"/>
					<param name="UANK06CE-TIPO-TELEFONO-7"/>
					<param name="UANK06CE-NUMERO-TELEFONO-7"/>
					<param name="UANK06CE-TEL-OPERAZIONE-8"/>
					<param name="UANK06CE-TEL-PROGR-8"/>
					<param name="UANK06CE-TIPO-TELEFONO-8"/>
					<param name="UANK06CE-NUMERO-TELEFONO-8"/>
					<param name="UANK06CE-TEL-OPERAZIONE-9"/>
					<param name="UANK06CE-TEL-PROGR-9"/>
					<param name="UANK06CE-TIPO-TELEFONO-9"/>
					<param name="UANK06CE-NUMERO-TELEFONO-9"/>
					<param name="UANK06CE-TEL-OPERAZIONE-10"/>
					<param name="UANK06CE-TEL-PROGR-10"/>
					<param name="UANK06CE-TIPO-TELEFONO-10"/>
					<param name="UANK06CE-NUMERO-TELEFONO-10"/>
					<param name="UANK06CE-CONSENSO-1"/>
					<param name="UANK06CE-DT-CONSENSO-1"/>
					<param name="UANK06CE-CONSENSO-2"/>
					<param name="UANK06CE-DT-CONSENSO-2"/>
					<param name="UANK06CE-CONSENSO-3"/>
					<param name="UANK06CE-DT-CONSENSO-3"/>
					<param name="UANK06CE-CONSENSO-4"/>
					<param name="UANK06CE-DT-CONSENSO-4"/>
					<param name="UANK06CE-CONSENSO-5"/>
					<param name="UANK06CE-DT-CONSENSO-5"/>
					<param name="UANK06CE-CONSENSO-6"/>
					<param name="UANK06CE-DT-CONSENSO-6"/>
					<param name="UANK06CE-INVIO-INFORMATIVA"/>
					<param name="UANK06CE-ESENZ-FISC-DATA-EM"/>
					<param name="UANK06CE-ESENZ-FISC-DATA-SCAD"/>
					<param name="UANK06CE-ESENZ-FISC-TIPO"/>
					<param name="UANK06CE-OPERATORE"/>
					<param name="UANK06CO-OPERAZIONE-1"/>
					<param name="UANK06CO-NDG-COLLEGATO-1"/>
					<param name="UANK06CO-COD-COLLEG-1"/>
					<param name="UANK06CO-OPERAZIONE-2"/>
					<param name="UANK06CO-NDG-COLLEGATO-2"/>
					<param name="UANK06CO-COD-COLLEG-2"/>
					<param name="UANK06CO-OPERAZIONE-3"/>
					<param name="UANK06CO-NDG-COLLEGATO-3"/>
					<param name="UANK06CO-COD-COLLEG-3"/>
					<param name="UANK06CO-OPERAZIONE-4"/>
					<param name="UANK06CO-NDG-COLLEGATO-4"/>
					<param name="UANK06CO-COD-COLLEG-4"/>
					<param name="UANK06CO-OPERAZIONE-5"/>
					<param name="UANK06CO-NDG-COLLEGATO-5"/>
					<param name="UANK06CO-COD-COLLEG-5"/>
					<param name="UANK06CO-OPERAZIONE-6"/>
					<param name="UANK06CO-NDG-COLLEGATO-6"/>
					<param name="UANK06CO-COD-COLLEG-6"/>
					<param name="UANK06CO-OPERAZIONE-7"/>
					<param name="UANK06CO-NDG-COLLEGATO-7"/>
					<param name="UANK06CO-COD-COLLEG-7"/>
					<param name="UANK06CO-OPERAZIONE-8"/>
					<param name="UANK06CO-NDG-COLLEGATO-8"/>
					<param name="UANK06CO-COD-COLLEG-8"/>
					<param name="UANK06CO-OPERAZIONE-9"/>
					<param name="UANK06CO-NDG-COLLEGATO-9"/>
					<param name="UANK06CO-COD-COLLEG-9"/>
					<param name="UANK06CO-OPERAZIONE-10"/>
					<param name="UANK06CO-NDG-COLLEGATO-10"/>
					<param name="UANK06CO-COD-COLLEG-10"/>
					<param name="UANK06CO-OPERAZIONE-11"/>
					<param name="UANK06CO-NDG-COLLEGATO-11"/>
					<param name="UANK06CO-COD-COLLEG-11"/>
					<param name="UANK06CO-OPERAZIONE-12"/>
					<param name="UANK06CO-NDG-COLLEGATO-12"/>
					<param name="UANK06CO-COD-COLLEG-12"/>
					<param name="UANK06CO-OPERAZIONE-13"/>
					<param name="UANK06CO-NDG-COLLEGATO-13"/>
					<param name="UANK06CO-COD-COLLEG-13"/>
					<param name="UANK06CO-OPERAZIONE-14"/>
					<param name="UANK06CO-NDG-COLLEGATO-14"/>
					<param name="UANK06CO-COD-COLLEG-14"/>
					<param name="UANK06CO-OPERAZIONE-15"/>
					<param name="UANK06CO-NDG-COLLEGATO-15"/>
					<param name="UANK06CO-COD-COLLEG-15"/>
					<param name="UANK06CO-OPERAZIONE-16"/>
					<param name="UANK06CO-NDG-COLLEGATO-16"/>
					<param name="UANK06CO-COD-COLLEG-16"/>
					<param name="UANK06CO-OPERAZIONE-17"/>
					<param name="UANK06CO-NDG-COLLEGATO-17"/>
					<param name="UANK06CO-COD-COLLEG-17"/>
					<param name="UANK06CO-OPERAZIONE-18"/>
					<param name="UANK06CO-NDG-COLLEGATO-18"/>
					<param name="UANK06CO-COD-COLLEG-18"/>
					<param name="UANK06CO-OPERAZIONE-19"/>
					<param name="UANK06CO-NDG-COLLEGATO-19"/>
					<param name="UANK06CO-COD-COLLEG-19"/>
				</input>
				<output>
					<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request">
						<xsl:output method="xml"/>
						<xsl:template match="/">
							<UANKA006>
								<ROCH-HEADER>
								    <ROCH-STRUCID>ROCH</ROCH-STRUCID>
								    <ROCH-VERSION>0002</ROCH-VERSION>
                                    <ROCH-BSNAME>RBS-XX-EGU-CENSIMENTO-ANAG-NORTF</ROCH-BSNAME>
								    <ROCH-RETURNCODE>0000</ROCH-RETURNCODE>
								    <ROCH-UOWCONTROL>0000</ROCH-UOWCONTROL>
								    <ROCH-ABEND-CODE/>
								    <ROCH-AREA-FREE/>
								</ROCH-HEADER>
								<UANK06-SYS>
									<UANK06-SYS-CO-TERMINALE/>
									<UANK06-SYS-CO-OPERATORE/>
									<UANK06-SYS-CO-FIL-OPER/>
									<UANK06-SYS-CO-BANCA>41</UANK06-SYS-CO-BANCA>
									<UANK06-SYS-FIL/>
									<UANK06-SYS-CO-APPL>AG</UANK06-SYS-CO-APPL>
									<UANK06-SYS-SERVIZIO>AFA-CENSIMENTO-CO</UANK06-SYS-SERVIZIO>
									<UANK06-SYS-FILLER/>
								</UANK06-SYS>
								<UANK06-ERR>
									<UANK06-ERR-RC>S</UANK06-ERR-RC>
									<UANK06-ERR-RC-DESC>0000</UANK06-ERR-RC-DESC>
									<UANK06-ERR-RC-DESC-ERRORE/>
									<UANK06-ERR-CODICE-ABEND/>
									<UANK06-ERR-SQLCODE>000000000</UANK06-ERR-SQLCODE>
									<UANK06-ERR-SQLERRMC/>
									<UANK06-ERR-LABEL/>
									<UANK06-ERR-TABEL/>
									<UANK06-ERR-FUNZIONE/>
									<UANK06-ERR-PGM/>
									<UANK06-ERR-CAMPI/>
								</UANK06-ERR>
								<UANK06-SYS-LU-DATI>05590</UANK06-SYS-LU-DATI>
								<UANK06-DATI>
									<UANK06-INPUT>
										<UANK06-AREACENS>
											<UANK06CE-STATUS-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-STATUS-AGG']/text()"/>
											</UANK06CE-STATUS-AGG>
											<UANK06CE-TELEFONI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TELEFONI-AGG']/text()"/>
											</UANK06CE-TELEFONI-AGG>
											<UANK06CE-INDIR-CORR-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-INDIR-CORR-AGG']/text()"/>
											</UANK06CE-INDIR-CORR-AGG>
											<UANK06CE-ALTRI-DATI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UUANK06CE-ALTRI-DATI-AGG']/text()"/>
											</UANK06CE-ALTRI-DATI-AGG>
											<UANK06CE-CODICI-VARI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CODICI-VARI-AGG']/text()"/>
											</UANK06CE-CODICI-VARI-AGG>
											<UANK06CE-SEDE-FISCALE-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-SEDE-FISCALE-AGG']/text()"/>
											</UANK06CE-SEDE-FISCALE-AGG>
											<UANK06CE-DATI-PRIVACY-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DATI-PRIVACY-AGG']/text()"/>
											</UANK06CE-DATI-PRIVACY-AGG>
											<UANK06CE-DATI-GENERALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DATI-GENERALI-AGG']/text()"/>
											</UANK06CE-DATI-GENERALI-AGG>
											<UANK06CE-DATI-AZIENDALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DATI-AZIENDALI-AGG']/text()"/>
											</UANK06CE-DATI-AZIENDALI-AGG>
											<UANK06CE-PROFESSIONI-ALT-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PROFESSIONI-ALT-AGG']/text()"/>
											</UANK06CE-PROFESSIONI-ALT-AGG>
											<UANK06CE-INTESTAZIONE-LONG-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-INTESTAZIONE-LONG-AGG']/text()"/>
											</UANK06CE-INTESTAZIONE-LONG-AGG>
											<UANK06CE-CLASSIFICAZIONE-ALT-AG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CLASSIFICAZIONE-ALT-AG']/text()"/>
											</UANK06CE-CLASSIFICAZIONE-ALT-AG>
											<UANK06CE-FUNZIONE>S</UANK06CE-FUNZIONE>
											<UANK06CE-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NDG']/text()"/>
											</UANK06CE-NDG>
											<UANK06CE-CONTROLLO-INT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CONTROLLO-INT']/text()"/>
											</UANK06CE-CONTROLLO-INT>
											<UANK06CE-CODICE-FISCALE>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CODICE-FISCALE']/text()"/>
											</UANK06CE-CODICE-FISCALE>
											<UANK06CE-PARTITA-IVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PARTITA-IVA']/text()"/>
											</UANK06CE-PARTITA-IVA>
											<UANK06CE-SPORTELLO-CAPOFILA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-SPORTELLO-CAPOFILA']/text()"/>
											</UANK06CE-SPORTELLO-CAPOFILA>
											<UANK06CE-INTESTAZIONE-A>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-INTESTAZIONE-A']/text()"/>
											</UANK06CE-INTESTAZIONE-A>
											<UANK06CE-INTESTAZIONE-B>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-INTESTAZIONE-B']/text()"/>
											</UANK06CE-INTESTAZIONE-B>
											<UANK06CE-TIPO-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-NDG']/text()"/>
											</UANK06CE-TIPO-NDG>
											<UANK06CE-SETTORISTA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-SETTORISTA']/text()"/>
											</UANK06CE-SETTORISTA>
											<UANK06CE-PSEUDONIMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PSEUDONIMO']/text()"/>
											</UANK06CE-PSEUDONIMO>
											<UANK06CE-STATO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-STATO-RES']/text()"/>
											</UANK06CE-STATO-RES>
											<UANK06CE-COMUNE-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-COMUNE-RES']/text()"/>
											</UANK06CE-COMUNE-RES>
											<UANK06CE-LOCALITA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-LOCALITA-RES']/text()"/>
											</UANK06CE-LOCALITA-RES>
											<UANK06CE-PROVINCIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PROVINCIA-RES']/text()"/>
											</UANK06CE-PROVINCIA-RES>
											<UANK06CE-CAP-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CAP-RES']/text()"/>
											</UANK06CE-CAP-RES>
											<UANK06CE-VIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-VIA-RES']/text()"/>
											</UANK06CE-VIA-RES>
											<UANK06CE-PRESSO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PRESSO-RES']/text()"/>
											</UANK06CE-PRESSO-RES>
											<UANK06CE-INTESTAZIONE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-INTESTAZIONE-CORR']/text()"/>
											</UANK06CE-INTESTAZIONE-CORR>
											<UANK06CE-STATO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-STATO-CORR']/text()"/>
											</UANK06CE-STATO-CORR>
											<UANK06CE-COMUNE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-COMUNE-CORR']/text()"/>
											</UANK06CE-COMUNE-CORR>
											<UANK06CE-LOCALITA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-LOCALITA-CORR']/text()"/>
											</UANK06CE-LOCALITA-CORR>
											<UANK06CE-PROVINCIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PROVINCIA-CORR']/text()"/>
											</UANK06CE-PROVINCIA-CORR>
											<UANK06CE-CAP-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CAP-CORR']/text()"/>
											</UANK06CE-CAP-CORR>
											<UANK06CE-VIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-VIA-CORR']/text()"/>
											</UANK06CE-VIA-CORR>
											<UANK06CE-PRESSO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PRESSO-CORR']/text()"/>
											</UANK06CE-PRESSO-CORR>
											<UANK06CE-UFFICIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-UFFICIO-CORR']/text()"/>
											</UANK06CE-UFFICIO-CORR>
											<UANK06CE-CASELLARIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CASELLARIO-CORR']/text()"/>
											</UANK06CE-CASELLARIO-CORR>
											<UANK06CE-CODICE-FISCALE-ESTERO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CODICE-FISCALE-ESTERO']/text()"/>
											</UANK06CE-CODICE-FISCALE-ESTERO>
											<UANK06CE-RAMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-RAMO']/text()"/>
											</UANK06CE-RAMO>
											<UANK06CE-SETTORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-SETTORE']/text()"/>
											</UANK06CE-SETTORE>
											<UANK06CE-RAMO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-RAMO-ALT']/text()"/>
											</UANK06CE-RAMO-ALT>
											<UANK06CE-SETTORE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-SETTORE-ALT']/text()"/>
											</UANK06CE-SETTORE-ALT>
											<UANK06CE-COMUNE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-COMUNE-ALT']/text()"/>
											</UANK06CE-COMUNE-ALT>
											<UANK06CE-PROVINCIA-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-PROVINCIA-ALT']/text()"/>
											</UANK06CE-PROVINCIA-ALT>
											<UANK06CE-STATO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-STATO-ALT']/text()"/>
											</UANK06CE-STATO-ALT>
											<UANK06CE-SEGMENTO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-SEGMENTO']/text()"/>
											</UANK06CE-SEGMENTO>
											<UANK06CE-SIT-GIURIDICA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-SIT-GIURIDICA']/text()"/>
											</UANK06CE-SIT-GIURIDICA>
											<UANK06CE-STATUS>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-STATUS']/text()"/>
											</UANK06CE-STATUS>
											<UANK06CE-UBIC-DOC-RIFERIM>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-UBIC-DOC-RIFERIM']/text()"/>
											</UANK06CE-UBIC-DOC-RIFERIM>
											<UANK06CE-UBIC-DOC-SPORTELLO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-UBIC-DOC-SPORTELLO']/text()"/>
											</UANK06CE-UBIC-DOC-SPORTELLO>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-1']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-1']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-1']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-1']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-2']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-2']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-2']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-2']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-3']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-3']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-3']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-3']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONEC-4']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-4']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-4']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-4']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-5']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-5']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-5']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-5']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-6']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-6']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-6']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-6']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-7']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-7']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-7']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-7']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-8']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-8']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-8']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-8']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-9']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-9']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-9']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-9']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-TELEFONI>
												<UANK06CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-OPERAZIONE-10']/text()"/>
												</UANK06CE-TEL-OPERAZIONE>
												<UANK06CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TEL-PROGR-10']/text()"/>
												</UANK06CE-TEL-PROGR>
												<UANK06CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-TIPO-TELEFONO-10']/text()"/>
												</UANK06CE-TIPO-TELEFONO>
												<UANK06CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-NUMERO-TELEFONO-10']/text()"/>
												</UANK06CE-NUMERO-TELEFONO>
											</UANK06CE-TELEFONI>
											<UANK06CE-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CONSENSO-1']/text()"/>
											</UANK06CE-CONSENSO-1>
											<UANK06CE-DT-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DT-CONSENSO-1']/text()"/>
											</UANK06CE-DT-CONSENSO-1>
											<UANK06CE-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CONSENSO-2']/text()"/>
											</UANK06CE-CONSENSO-2>
											<UANK06CE-DT-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DT-CONSENSO-2']/text()"/>
											</UANK06CE-DT-CONSENSO-2>
											<UANK06CE-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CONSENSO-3']/text()"/>
											</UANK06CE-CONSENSO-3>
											<UANK06CE-DT-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DT-CONSENSO-3']/text()"/>
											</UANK06CE-DT-CONSENSO-3>
											<UANK06CE-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CONSENSO-4']/text()"/>
											</UANK06CE-CONSENSO-4>
											<UANK06CE-DT-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DT-CONSENSO-4']/text()"/>
											</UANK06CE-DT-CONSENSO-4>
											<UANK06CE-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CONSENSO-5']/text()"/>
											</UANK06CE-CONSENSO-5>
											<UANK06CE-DT-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DT-CONSENSO-5']/text()"/>
											</UANK06CE-DT-CONSENSO-5>
											<UANK06CE-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-CONSENSO-6']/text()"/>
											</UANK06CE-CONSENSO-6>
											<UANK06CE-DT-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-DT-CONSENSO-6']/text()"/>
											</UANK06CE-DT-CONSENSO-6>
											<UANK06CE-INVIO-INFORMATIVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-INVIO-INFORMATIVA']/text()"/>
											</UANK06CE-INVIO-INFORMATIVA>
											<UANK06CE-ESENZ-FISC-DATA-EM>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-ESENZ-FISC-DATA-EM']/text()"/>
											</UANK06CE-ESENZ-FISC-DATA-EM>
											<UANK06CE-ESENZ-FISC-DATA-SCAD>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-ESENZ-FISC-DATA-SCAD']/text()"/>
											</UANK06CE-ESENZ-FISC-DATA-SCAD>
											<UANK06CE-ESENZ-FISC-TIPO>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-ESENZ-FISC-TIPO']/text()"/>
											</UANK06CE-ESENZ-FISC-TIPO>
											<UANK06CE-OPERATORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CE-OPERATORE']/text()"/>
											</UANK06CE-OPERATORE>
											<FILLER1/>
										</UANK06-AREACENS>
										<UANK06-AREACOIN>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-1']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-1']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-1']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-2']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-2']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-2']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-3']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-3']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-3']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-4']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-4']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-4']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-5']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-5']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-5']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-6']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-6']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-6']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-7']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-7']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-7']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-8']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-8']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-8']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-9']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-9']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-9']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-10']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-10']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-10']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-10']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-10']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-10']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-11']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-11']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-11']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-12']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-12']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-12']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-13']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-13']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-13']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-14']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-14']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-14']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-15']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-15']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-15']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-16']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-16']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-16']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-17']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-17']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-17']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-18']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-18']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-18']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<UANK06CO-TABELLA>
												<UANK06CO-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-OPERAZIONE-19']/text()"/>
												</UANK06CO-OPERAZIONE>
												<UANK06CO-NDG-COLLEGATO>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-NDG-COLLEGATO-19']/text()"/>
												</UANK06CO-NDG-COLLEGATO>
												<UANK06CO-COD-COLLEG>
													<xsl:value-of select="xgen:request/xgen:param[@name='UANK06CO-COD-COLLEG-19']/text()"/>
												</UANK06CO-COD-COLLEG>
											</UANK06CO-TABELLA>
											<FILLER1/>
										</UANK06-AREACOIN>
									</UANK06-INPUT>
									<UANK06-OUTPUT>
										<UANK06OU-NDG/>
										<UANK06OU-CONTROLLO-INT>S</UANK06OU-CONTROLLO-INT>
										<UANK06OU-CODICE-FISCALE/>
										<UANK06OU-PARTITA-IVA/>
										<UANK06OU-SPORTELLO-CAPOFILA/>
										<UANK06OU-INTESTAZIONE-A/>
										<UANK06OU-INTESTAZIONE-B/>
										<UANK06OU-TIPO-NDG/>
										<UANK06OU-SETTORISTA/>
										<UANK06OU-PSEUDONIMO/>
										<UANK06OU-STATO-RES/>
										<UANK06OU-DESCR-COM-RES/>
										<UANK06OU-CAB-RES>00000</UANK06OU-CAB-RES>
										<UANK06OU-LOCALITA-RES/>
										<UANK06OU-PROVINCIA-RES/>
										<UANK06OU-CAP-RES>00000</UANK06OU-CAP-RES>
										<UANK06OU-VIA-RES/>
										<UANK06OU-PRESSO/>
										<UANK06OU-SETTORE>000</UANK06OU-SETTORE>
										<UANK06OU-RAMO>000</UANK06OU-RAMO>
										<UANK06OU-PROFESSIONE/>
										<UANK06OU-INTESTAZ-CORR/>
										<UANK06OU-STATO-CORR/>
										<UANK06OU-DESCR-COM-CORR/>
										<UANK06OU-LOCALITA-CORR/>
										<UANK06OU-PROVINCIA-CORR/>
										<UANK06OU-CAP-CORR>00000</UANK06OU-CAP-CORR>
										<UANK06OU-VIA-CORR/>
										<UANK06OU-PRESSO-CORR/>
										<UANK06OU-UFFICIO-CORR/>
										<UANK06OU-CASELLARIO-CORR/>
										<UANK06OU-STATUS-TAB>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
											<UANK06OU-STATUS>S</UANK06OU-STATUS>
										</UANK06OU-STATUS-TAB>
										<UANK06OU-COMPLETEZZA-DATI>S</UANK06OU-COMPLETEZZA-DATI>
										<UANK06OU-STATO-NDG>S</UANK06OU-STATO-NDG>
										<UANK06OU-DATA-CENSIMENTO/>
										<UANK06OU-DATA-VA/>
										<UANK06OU-SPORTELLO-ESEC/>
										<UANK06OU-OPERATORE/>
										<UANK06OU-INTESTAZIONE-AGG/>
										<UANK06OU-COD-CR>0000000000000</UANK06OU-COD-CR>
										<UANK06OU-PREF-COGN-ACQUIS/>
										<UANK06OU-COGNOME-ACQUISITO/>
										<UANK06OU-TITOLI/>
										<UANK06OU-SESSO>S</UANK06OU-SESSO>
										<UANK06OU-DATA-NASCITA/>
										<UANK06OU-COMUNE-NASCITA/>
										<UANK06OU-PROVINCIA-NASC/>
										<UANK06OU-DATA-COSTITUZI/>
										<UANK06OU-NUMERO-CCIAA>00000000</UANK06OU-NUMERO-CCIAA>
										<UANK06OU-PROV-CCIAA/>
										<UANK06OU-DATA-ISCR-CCIA/>
										<UANK06OU-NUMERO-AIA>00000000</UANK06OU-NUMERO-AIA>
										<UANK06OU-PROV-AIA/>
										<UANK06OU-CODICE-ABI>00000</UANK06OU-CODICE-ABI>
										<UANK06OU-COD-CONTROLLO>0</UANK06OU-COD-CONTROLLO>
										<UANK06OU-MINCOMES/>
										<UANK06OU-NUM-REG-TRIB>000000000</UANK06OU-NUM-REG-TRIB>
										<UANK06OU-SEDE-TRIBUNALE/>
										<UANK06OU-CODICE-SWIFT/>
										<UANK06OU-COD-OPERAT-EST/>
										<UANK06OU-STATO-SEDE-F/>
										<UANK06OU-LOCALITA-SEDE-F/>
										<UANK06OU-DESCR-COM-SEDE-F/>
										<UANK06OU-PROVINCIA-SEDE-F/>
										<UANK06OU-CAP-SEDE-F>00000</UANK06OU-CAP-SEDE-F>
										<UANK06OU-VIA-SEDE-F/>
										<UANK06OU-PRESSO-SEDE-F/>
										<UANK06OU-DATA-RIF-ADA/>
										<UANK06OU-CLASSE-DIMENSION>00</UANK06OU-CLASSE-DIMENSION>
										<UANK06OU-FATTURATO>0000000</UANK06OU-FATTURATO>
										<UANK06OU-CAPITALE-SOCIALE>0000000</UANK06OU-CAPITALE-SOCIALE>
										<UANK06OU-NUMERO-DIPENDENTI>000000</UANK06OU-NUMERO-DIPENDENTI>
										<UANK06OU-SETTORE-ALT>000</UANK06OU-SETTORE-ALT>
										<UANK06OU-RAMO-ALT>000</UANK06OU-RAMO-ALT>
										<UANK06OU-STATO-ALT/>
										<UANK06OU-COMUNE-ALT/>
										<UANK06OU-PROVINCIA-ALT/>
										<UANK06OU-CAB-ALT>000000</UANK06OU-CAB-ALT>
										<UANK06OU-CONSENSO-1>S</UANK06OU-CONSENSO-1>
										<UANK06OU-DT-CONSENSO-1/>
										<UANK06OU-CONSENSO-2>S</UANK06OU-CONSENSO-2>
										<UANK06OU-DT-CONSENSO-2/>
										<UANK06OU-CONSENSO-3>S</UANK06OU-CONSENSO-3>
										<UANK06OU-DT-CONSENSO-3/>
										<UANK06OU-CONSENSO-4>S</UANK06OU-CONSENSO-4>
										<UANK06OU-DT-CONSENSO-4/>
										<UANK06OU-CONSENSO-5>S</UANK06OU-CONSENSO-5>
										<UANK06OU-DT-CONSENSO-5/>
										<UANK06OU-CONSENSO-6>S</UANK06OU-CONSENSO-6>
										<UANK06OU-DT-CONSENSO-6/>
										<UANK06OU-INVIO-INFORMATIVA/>
										<UANK06OU-PROF-ATTIVITA/>
										<UANK06OU-PROF-ATTIVITA/>
										<UANK06OU-PROF-ATTIVITA/>
										<UANK06OU-PROF-ATTIVITA/>
										<UANK06OU-PROF-ATTIVITA/>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-TELEFONI>
											<UANK06OU-TEL-PROGR>00</UANK06OU-TEL-PROGR>
											<UANK06OU-TIPO-TELEFONO/>
											<UANK06OU-NUMERO-TELEFONO/>
										</UANK06OU-TELEFONI>
										<UANK06OU-DOCUMENTI>
											<UANK06OU-TIPO-DOCUM/>
											<UANK06OU-NUM-DOCUM/>
											<UANK06OU-DATA-RIL-DOCUM/>
											<UANK06OU-PROV-RIL-DOCUM/>
											<UANK06OU-COMUNE-RIL-DOCUM/>
											<UANK06OU-CAB-RIL-DOCUM>000000</UANK06OU-CAB-RIL-DOCUM>
											<UANK06OU-STATO-RIL-DOCUM/>
											<UANK06OU-ENTE-RILAS-DOCUM/>
											<UANK06OU-DATA-SCA-DOCUM/>
										</UANK06OU-DOCUMENTI>
										<UANK06OU-DOCUMENTI>
											<UANK06OU-TIPO-DOCUM/>
											<UANK06OU-NUM-DOCUM/>
											<UANK06OU-DATA-RIL-DOCUM/>
											<UANK06OU-PROV-RIL-DOCUM/>
											<UANK06OU-COMUNE-RIL-DOCUM/>
											<UANK06OU-CAB-RIL-DOCUM>000000</UANK06OU-CAB-RIL-DOCUM>
											<UANK06OU-STATO-RIL-DOCUM/>
											<UANK06OU-ENTE-RILAS-DOCUM/>
											<UANK06OU-DATA-SCA-DOCUM/>
										</UANK06OU-DOCUMENTI>
										<UANK06OU-DOCUMENTI>
											<UANK06OU-TIPO-DOCUM/>
											<UANK06OU-NUM-DOCUM/>
											<UANK06OU-DATA-RIL-DOCUM/>
											<UANK06OU-PROV-RIL-DOCUM/>
											<UANK06OU-COMUNE-RIL-DOCUM/>
											<UANK06OU-CAB-RIL-DOCUM>000000</UANK06OU-CAB-RIL-DOCUM>
											<UANK06OU-STATO-RIL-DOCUM/>
											<UANK06OU-ENTE-RILAS-DOCUM/>
											<UANK06OU-DATA-SCA-DOCUM/>
										</UANK06OU-DOCUMENTI>
										<UANK06OU-DOCUMENTI>
											<UANK06OU-TIPO-DOCUM/>
											<UANK06OU-NUM-DOCUM/>
											<UANK06OU-DATA-RIL-DOCUM/>
											<UANK06OU-PROV-RIL-DOCUM/>
											<UANK06OU-COMUNE-RIL-DOCUM/>
											<UANK06OU-CAB-RIL-DOCUM>000000</UANK06OU-CAB-RIL-DOCUM>
											<UANK06OU-STATO-RIL-DOCUM/>
											<UANK06OU-ENTE-RILAS-DOCUM/>
											<UANK06OU-DATA-SCA-DOCUM/>
										</UANK06OU-DOCUMENTI>
										<UANK06OU-DOCUMENTI>
											<UANK06OU-TIPO-DOCUM/>
											<UANK06OU-NUM-DOCUM/>
											<UANK06OU-DATA-RIL-DOCUM/>
											<UANK06OU-PROV-RIL-DOCUM/>
											<UANK06OU-COMUNE-RIL-DOCUM/>
											<UANK06OU-CAB-RIL-DOCUM>000000</UANK06OU-CAB-RIL-DOCUM>
											<UANK06OU-STATO-RIL-DOCUM/>
											<UANK06OU-ENTE-RILAS-DOCUM/>
											<UANK06OU-DATA-SCA-DOCUM/>
										</UANK06OU-DOCUMENTI>
										<UANK06OU-SEGMENTO/>
										<UANK06OU-SIT-GIURIDICA/>
										<UANK06OU-EREDITA>S</UANK06OU-EREDITA>
										<UANK06OU-DATA-EREDITA/>
										<UANK06OU-SEDE-CASAMADRE>000000</UANK06OU-SEDE-CASAMADRE>
										<UANK06OU-DATA-ESTINZIONE/>
										<UANK06OU-SPORTELLO-RIF/>
										<UANK06OU-RIFERIMENTO/>
										<UANK06OU-FILLER/>
									</UANK06-OUTPUT>
								</UANK06-DATI>
							</UANKA006>
						</xsl:template>
					</xsl:stylesheet>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.RomaProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass/>
		<channelclass>it.usi.webfactory.channels.RomaBusinnesChannel</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params/>
	</protocol>
	<channel>
		<params>
			<client>Client_EGU_XX</client>
			<service>RBS-XX-EGU-CENSIMENTO-ANAG-NORTF</service>
			<format>UANKA006-XML</format>
			<timeout>30000</timeout>
		</params>
	</channel>
</service>
