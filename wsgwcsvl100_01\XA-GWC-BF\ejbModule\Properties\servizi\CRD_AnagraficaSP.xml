<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="mazzocchi" Timestamp="14/11/2002 15.49.37" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="internet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="X01"/>
					<param name="X97"/>
					<param name="X98"/>
					<param name="flagGr"/>
					<param name="flagGrE1"/>
					<param name="iva"/>
					<param name="fiscale"/>
					<param name="bancaNdg"/>
					<param name="ndgSa"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output>
					<hostService id="1" name="GLF0G150">
						<param select="X01" name="X01"/>
						<param select="X97" name="X97"/>
						<param select="X98" name="X98"/>
						<param select="flagGr" name="X02"/>
						<param select="fiscale" name="X03"/>
						<param select="iva" name="X04"/>
						<param select="bancaNdg" name="X05"/>
						<param select="ndgSa" name="X06"/>
					</hostService>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<scheduler>GL40</scheduler>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>GL40</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>
