<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 
	9.33.19" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
	security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="UB1C020I_FUNZIONE" />
					<param name="UB1C020I_NDG" />
					<param name="UB1C020I_SPORTELLO" />
					<param name="UB1C020I_TIPO_RAP" />
					<param name="UB1C020I_NUM_CONTO" />
					<param name="UB1C020I_FORMA_TECNICA" />
					<param name="UB1C020I_DIVISA" />
					<param name="UB1C020I_DATA_ASSICURAZ" />
					<param name="UB1C020I_INTESTAZIONE_RAP" />
					<param name="UB1C020I_PRESSO_RAP" />
					<param name="UB1C020I_VIA_RAP" />
					<param name="UB1C020I_CAP_RAP" />
					<param name="UB1C020I_DESCR_COM_RAP" />
					<param name="UB1C020I_SIGLA_PROV_RAP" />
					<param name="UB1C020I_LOCALITA_RAP" />
					<param name="UB1C020I_STATO_RAP" />
					<param name="UB1C020I_UFFICIO_RAP" />
					<param name="UB1C020I_CASELLARIO_RAP" />
					<param name="UB1C020I_CAUSALE_ESTINZIONE" />
					<param name="UB1C020I_NOTE_RAP" />
					<param name="UB1C020I_STATUSR1" />
					<param name="UB1C020I_STATUSR2" />
					<param name="UB1C020I_STATUSR3" />
					<param name="UB1C020I_STATUSR4" />
					<param name="UB1C020I_STATUSR5" />
					<param name="UB1C020I_STATUSR6" />
					<param name="UB1C020I_STATUSR7" />
					<param name="UB1C020I_STATUSR8" />
					<param name="UB1C020I_DATA_CENSIMENTO" />
					<param name="UB1C020I_AZIENDA_ESEC" />
					<param name="UB1C020I_SPORTELLO_ESEC" />
					<param name="UB1C020I_SPORTELLO_ATT" />
					<param name="UB1C020I_TIPO_RAP_ATT" />
					<param name="UB1C020I_NUM_CONTO_ATT" />
					<param name="UB1C020I_OPERATORE" />
					<param name="UB1C020I_DATA_VARIAZIONE" />
					<param name="UB1C020I_FLAG_A" />
					<param name="UB1C020I_FLAG_B" />
					<param name="UB1C020I_FLAG_C" />
					<param name="UB1C020I_FLAG_D" />
					<param name="UB1C020I_FLAG_E" />
					<param name="UB1C020I_FLG_SIE" />
					<param name="UB1C020I_ID_SIE_ROW_AML" />
					<param name="UB1C020I_ID_SIE_KEY_OP" />
					<param name="XF_GAUSS_ID" />
				</input>
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input />
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</bridge>
		<params>
			<hostService>UB1_CENS_RAPANA_QINETIC</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUB1</transaction>
			<program>PC00WUB1</program>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>