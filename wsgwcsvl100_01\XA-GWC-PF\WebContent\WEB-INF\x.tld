<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE taglib
  PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN"
  "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">
<taglib>
  <tlib-version>1.0</tlib-version>
  <jsp-version>1.2</jsp-version>
  <short-name>x</short-name>
  <uri>http://java.sun.com/jstl/xml</uri>
  <display-name>JSTL XML</display-name>
  <description>JSTL 1.0 XML library</description>

  <validator>
    <validator-class>
	org.apache.taglibs.standard.tlv.JstlXmlTLV
    </validator-class>
    <init-param>
	<param-name>expressionAttributes</param-name>
	<param-value>
	    out:escapeXml
	    parse:xml
	    parse:systemId
	    parse:filter
	    transform:xml
	    transform:xmlSystemId
	    transform:xslt
	    transform:xsltSystemId
	    transform:result
	</param-value>
	<description>
	    Whitespace-separated list of colon-separated token pairs
	    describing tag:attribute combinations that accept expressions.
	    The validator uses this information to determine which
	    attributes need their syntax validated.
	</description>
     </init-param>
  </validator>

  <tag>
    <name>choose</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.ChooseTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Simple conditional tag that establishes a context for
        mutually exclusive conditional operations, marked by
        &lt;when&gt; and &lt;otherwise&gt;
    </description>
  </tag>

  <tag>
    <name>out</name>
    <tag-class>org.apache.taglibs.standard.tag.el.xml.ExprTag</tag-class>
    <body-content>empty</body-content>
    <description>
	Like &lt;%= ... &gt;, but for XPath expressions.
    </description>
    <attribute>
        <name>select</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>escapeXml</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>if</name>
    <tag-class>org.apache.taglibs.standard.tag.common.xml.IfTag</tag-class>
    <body-content>JSP</body-content>
    <description>
      XML conditional tag, which evalutes its body if the
      supplied XPath expression evalutes to 'true' as a boolean
    </description>
    <attribute>
        <name>select</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>forEach</name>
    <tag-class>org.apache.taglibs.standard.tag.common.xml.ForEachTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	XML iteration tag.
    </description>
    <attribute>
	<name>var</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>select</name>
	<required>true</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>otherwise</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.OtherwiseTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Subtag of &lt;choose&gt; that follows &lt;when&gt; tags
	and runs only if all of the prior conditions evaluated to
	'false'
    </description>
  </tag>

  <tag>
    <name>param</name>
    <tag-class>org.apache.taglibs.standard.tag.el.xml.ParamTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Adds a parameter to a containing 'transform' tag's Transformer
    </description>
    <attribute>
        <name>name</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>parse</name>
    <tag-class>org.apache.taglibs.standard.tag.el.xml.ParseTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.XmlParseTEI</tei-class>
    <body-content>JSP</body-content>
    <description>
	Parses XML content from 'source' attribute or 'body'
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>varDom</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scopeDom</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>xml</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>systemId</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>filter</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>set</name>
    <tag-class>org.apache.taglibs.standard.tag.common.xml.SetTag</tag-class>
    <body-content>empty</body-content>
    <description>
	Saves the result of an XPath expression evaluation in a 'scope'
    </description>
    <attribute>
        <name>var</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>select</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>transform</name>
    <tag-class>org.apache.taglibs.standard.tag.el.xml.TransformTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.XmlTransformTEI</tei-class>
    <body-content>JSP</body-content>
    <description>
	Conducts a transformation given a source XML document
	and an XSLT stylesheet
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>result</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>xml</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>xmlSystemId</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>xslt</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>xsltSystemId</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>when</name>
    <tag-class>org.apache.taglibs.standard.tag.common.xml.WhenTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Subtag of &lt;choose&gt; that includes its body if its
        expression evalutes to 'true'
    </description>
    <attribute>
        <name>select</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

</taglib>
