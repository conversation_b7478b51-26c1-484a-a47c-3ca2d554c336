package it.usi.xframe.gwc.bfintf;

public interface IGwcConstDefinitions {

	public static final String BANKS_LIST_CACHENAME = "GWC_BANKS_LIST";

	// Gauss Service Names
	public static final String GAUSS_NprCardIssueAuthoUpdate    = "NPRCardIssueAuthoUpdate";
	public static final String GAUSS_NprCardChangeAuthoUpdate   = "NPRCardChangeAuthoUpdate";
	public static final String GAUSS_NprCardLimitsChangeUpdate  = "NPRCardLimitsChangeUpdate";
	public static final String GAUSS_NprCardIssueAddDataUpdate  = "NPRCardIssueAddDataUpdate";
	public static final String GAUSS_RbgMultinationalOverruling = "RBG_Multinational_Overruling";
	public static final String GAUSS_RB1_Switch_Service = "RB1_Switch_Service";
	public static final String GAUSS_RB4_Cancel_Motivation = "RB4_Cancel_Motivation";
	public static final String GAUSS_RB4_Counterparty_Info = "RB4_Counterparty_Info";
	public static final String GAUSS_RB4_Country_Ceiling = "RB4_Country_Ceiling";
	public static final String GAUSS_RB4_Country_Ceiling_Summary = "RB4_Country_Ceiling_Summary";
	public static final String GAUSS_RB4_Group_Integration = "RB4_Group_Integration";
	public static final String GAUSS_RB4_Group_Support = "RB4_Group_Support";
	public static final String GAUSS_RB4_Merchant_Utility = "RB4_Merchant_Utility";
	public static final String GAUSS_RB4_ModelSelection = "RB4_ModelSelection";
	public static final String GAUSS_RB4_Override = "RB4_Override";
	public static final String GAUSS_RB4_Qualitative = "RB4_Qualitative";
	public static final String GAUSS_RB4_Quantitative = "RB4_Quantitative";
	public static final String GAUSS_RB4_StandAlone = "RB4_StandAlone";
	public static final String GAUSS_RB4_lgd = "RB4_lgd";
	public static final String GAUSS_RB4_sao = "RB4_sao";
	public static final String GAUSS_RB5_Group_Integration = "RB5_Group_Integration";
	public static final String GAUSS_RB5_Lgd = "RB5_Lgd";
	public static final String GAUSS_RB5_Navigator = "RB5_Navigator";
	public static final String GAUSS_RB5_Override = "RB5_Override";
	public static final String GAUSS_RB5_Qualitative = "RB5_Qualitative";
	public static final String GAUSS_RB5_Treasury_Utility = "RB5_Treasury_Utility";		
	public static final String GAUSS_RB6_cns = "RB6_cns";
	public static final String GAUSS_RB6_proposal_List = "RB6_proposal_List";
	public static final String GAUSS_RB6_utility = "RB6_utility";
	public static final String GAUSS_RIC_Corporate_Utility = "RIC_Corporate_Utility";
	public static final String GAUSS_RIC_OVR = "RIC_OVR";
	public static final String GAUSS_RIC_QLT = "RIC_QLT";
	public static final String GAUSS_RIC_QNT = "RIC_QNT";

	// Roma Service Names	
	public static final String ROMA_Prefix = "FD_";
	public static final String ADALYA_LISTA_PREFIX = "ADALYA_Lista";
	public static final String ADALYA_CENSIMENTO_FIN_R = "ADALYA_Censimento_FIN_R";
	public static final String NPF_SERVIZIO_FIS_PREFIX = "NPF_Servizio_FIS_";
	public static final String NPF_DETTAGLIO_BENE_PREFIX = "NPF_Dettaglio_Bene_";
	public static final String ROMA_CENSIMENTO_PREFIX = "Censimento";		
	public static final String ROMA_LISTAAGGREGATI_FIN_R = "EMP_ListaAggregati_FIN_R";		
	public static final String ROMA_PIANOTAEG_FIN_R = "EMP_PianoTaeg_FIN_R";
	public static final String NPU_CENSIMENTO_REFERTO_R = "NPU_Censimento_Referto_R";	
	public static final String NPU_PRICING_R = "NPU_Pricing_R";
	public static final String EMP_PIANOTAEG_FIN_R = "EMP_PianoTaeg_FIN_R";
	public static final String NPU_FIN_RICERCACONV_R = "NPU_FIN_RicercaConv_R";
	
}