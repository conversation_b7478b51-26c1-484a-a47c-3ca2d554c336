<?xml version="1.0" encoding="UTF-8"?>
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="N001"/>
					<param name="N002"/>
					<param name="N003"/>
					<param name="N004"/>
					<param name="N005"/>
					<param name="N006"/>
					<param name="N007"/>
					<param name="N008"/>
					<param name="N009"/>
					<param name="N010"/>
					<param name="N011"/>
					<param name="N012"/>
					<param name="N013"/>
					<param name="N014"/>
					<param name="N015"/>
					<param name="N016"/>
					<param name="N017"/>
					<param name="N018"/>
					<param name="N019"/>
					<param name="N020"/>
					<param name="N021"/>
					<param name="N022"/>
					<param name="N023"/>
					<param name="N024"/>
					<param name="D01"/>
					<param name="X01"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NRussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output>
					<hostService id="1" name="RB18R18I"/>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<scheduler>RBIL</scheduler>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>RBIL</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>

