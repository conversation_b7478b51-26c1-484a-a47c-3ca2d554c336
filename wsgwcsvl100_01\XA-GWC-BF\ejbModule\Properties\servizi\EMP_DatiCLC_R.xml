<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="EM3I-COD-BANCA"/>
					<param name="EM3I-TIPO-0"/>
					<param name="EM3I-NDG-0"/>
					<param name="EM3I-TIPO-1"/>
					<param name="EM3I-NDG-1"/>
					<param name="EM3I-TIPO-2"/>
					<param name="EM3I-NDG-2"/>
					<param name="EM3I-TIPO-3"/>
					<param name="EM3I-NDG-3"/>
					<param name="EM3I-TIPO-4"/>
					<param name="EM3I-NDG-4"/>
					<param name="EM3I-TIPO-5"/>
					<param name="EM3I-NDG-5"/>
					<param name="EM3I-TIPO-6"/>
					<param name="EM3I-NDG-6"/>
					<param name="EM3I-TIPO-7"/>
					<param name="EM3I-NDG-7"/>
					<param name="EM3I-TIPO-8"/>
					<param name="EM3I-NDG-8"/>
					<param name="EM3I-TIPO-9"/>
					<param name="EM3I-NDG-9"/>
										
			</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>EMP0-DATICLC-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>WEMP</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
