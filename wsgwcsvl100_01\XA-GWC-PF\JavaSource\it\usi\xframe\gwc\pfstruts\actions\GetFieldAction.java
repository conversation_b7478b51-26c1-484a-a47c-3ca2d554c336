package it.usi.xframe.gwc.pfstruts.actions;


import it.usi.xframe.gwc.pfstruts.forms.GetFieldForm;
import it.usi.xframe.gwc.pfutil.GwcDelegate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class GetFieldAction extends Action  {

	private Log logger = LogFactory.getLog(this.getClass());
	
	/* (non-Javadoc)
	 * @see org.apache.struts.action.Action#execute(org.apache.struts.action.ActionMapping, org.apache.struts.action.ActionForm, javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
	 */
	public ActionForward execute(
		ActionMapping mapping,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {

		logger.info("executing GetFieldAction.execute() ... START");

		GetFieldForm myForm = (GetFieldForm) form;
		String xmlOutput = (String) request.getSession().getAttribute("xmlOutput");
		
		GwcDelegate dlg = new GwcDelegate();
		
		String res = "";
		if (myForm.getRecordAsInt() <= 0) {
			res = dlg.getFieldFromResponse(xmlOutput, myForm.getTagFrom(), myForm.getTag());
		}
		else {
			res = dlg.getFieldFromResponse(xmlOutput, myForm.getTagFrom(), myForm.getTag(), myForm.getRecordAsInt());
		}
		logger.info("executing GgetFieldFromResponse : res = ["+res+"]");
	
		request.setAttribute("fieldValue", res);
	
		logger.info("executing GetFieldAction.execute() ... END");

		ActionForward af = mapping.findForward("ok");		
		return af;
	}


}
