<%@ taglib uri="http://jakarta.apache.org/struts/tags-tiles" prefix="tiles" %>
<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>

<tiles:importAttribute name="ifFormat"/>
<tiles:importAttribute name="daFormat"/>
<tiles:importAttribute name="dateStatusFunc"/>
<tiles:importAttribute name="firstDay"/>
<tiles:importAttribute name="weekNumbers"/>
<tiles:importAttribute name="align"/>
<tiles:importAttribute name="range"/>
<tiles:importAttribute name="date"/>
<tiles:importAttribute name="showsTime"/>
<tiles:importAttribute name="timeFormat"/>
<tiles:importAttribute name="electric"/>
<tiles:importAttribute name="position"/>
<tiles:importAttribute name="cache"/>
<tiles:importAttribute name="showOthers"/>
<tiles:importAttribute name="onSelect"/>

<c:if test="${ifFormat != ''}">
	ifFormat : "<tiles:getAsString name="ifFormat"/>",
</c:if>
<c:if test="${daFormat != ''}">
	daFormat : "<tiles:getAsString name="daFormat"/>",
</c:if>
<c:if test="${dateStatusFunc != ''}">
	dateStatusFunc : <tiles:getAsString name="dateStatusFunc"/>,
</c:if>
<c:if test="${firstDay != ''}">
	firstDay : <tiles:getAsString name="firstDay"/>,
</c:if>
<c:if test="${weekNumbers != ''}">
	weekNumbers : <tiles:getAsString name="weekNumbers"/>,
</c:if>
<c:if test="${align != ''}">
	align : "<tiles:getAsString name="align"/>",
</c:if>
<c:if test="${range != ''}">
	range : <tiles:getAsString name="range"/>,
</c:if>
<c:if test="${date != ''}">
	date : <tiles:getAsString name="date"/>,
</c:if>
<c:if test="${showsTime != ''}">
	showsTime : <tiles:getAsString name="showsTime"/>,
</c:if>
<c:if test="${timeFormat != ''}">
	timeFormat : "<tiles:getAsString name="timeFormat"/>",
</c:if>
<c:if test="${electric != ''}">
	electric : <tiles:getAsString name="electric"/>,
</c:if>
<c:if test="${position != ''}">
	position : <tiles:getAsString name="position"/>,
</c:if>
<c:if test="${cache != ''}">
	cache : <tiles:getAsString name="cache"/>,
</c:if>
<c:if test="${showOthers != ''}">
	showOthers : <tiles:getAsString name="showOthers"/>,
</c:if>
<c:if test="${onSelect != ''}">
	onSelect : <tiles:getAsString name="onSelect"/>,
</c:if>
