<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Autorizzazione">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x15" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_AutorizzazioneResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_AutorizzazioneReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_AutorizzazioneResponse">

      <wsdl:part element="impl:nprFidi_AutorizzazioneResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_AutorizzazioneRequest">

      <wsdl:part element="impl:nprFidi_Autorizzazione" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Autorizzazione_SEI">

      <wsdl:operation name="nprFidi_Autorizzazione">

         <wsdl:input message="impl:nprFidi_AutorizzazioneRequest" name="nprFidi_AutorizzazioneRequest"/>

         <wsdl:output message="impl:nprFidi_AutorizzazioneResponse" name="nprFidi_AutorizzazioneResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_AutorizzazioneSoapBinding" type="impl:NPR_Fidi_Autorizzazione_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Autorizzazione">

         <wsdlsoap:operation soapAction="nprFidi_Autorizzazione"/>

         <wsdl:input name="nprFidi_AutorizzazioneRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_AutorizzazioneResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_AutorizzazioneService">

      <wsdl:port binding="impl:NPR_Fidi_AutorizzazioneSoapBinding" name="NPR_Fidi_Autorizzazione">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Autorizzazione"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
