<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprDatiFidiPratica">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprDatiFidiPraticaResponse">
    <complexType>
     <sequence>
      <element name="nprDatiFidiPraticaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprDatiFidiPraticaResponse">

      <wsdl:part element="impl:nprDatiFidiPraticaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprDatiFidiPraticaRequest">

      <wsdl:part element="impl:nprDatiFidiPratica" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_DatiFidiPratica_SEI">

      <wsdl:operation name="nprDatiFidiPratica">

         <wsdl:input message="impl:nprDatiFidiPraticaRequest" name="nprDatiFidiPraticaRequest"/>

         <wsdl:output message="impl:nprDatiFidiPraticaResponse" name="nprDatiFidiPraticaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_DatiFidiPraticaSoapBinding" type="impl:NPR_DatiFidiPratica_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprDatiFidiPratica">

         <wsdlsoap:operation soapAction="nprDatiFidiPratica"/>

         <wsdl:input name="nprDatiFidiPraticaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprDatiFidiPraticaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_DatiFidiPraticaService">

      <wsdl:port binding="impl:NPR_DatiFidiPraticaSoapBinding" name="NPR_DatiFidiPratica">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_DatiFidiPratica"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
