<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:tns2="http://dto.wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://dto.wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <complexType name="GetExperianDataInput">
    <sequence>
     <element name="ndg" nillable="true" type="xsd:string"/>
     <element name="requestType" nillable="true" type="xsd:string"/>
     <element name="requestDataType" nillable="true" type="xsd:string"/>
     <element name="applicant" nillable="true" type="xsd:string"/>
     <element name="currencyFlag" nillable="true" type="xsd:string"/>
     <element name="surname" nillable="true" type="xsd:string"/>
     <element name="name" nillable="true" type="xsd:string"/>
     <element name="gender" nillable="true" type="xsd:string"/>
     <element name="birthDate" nillable="true" type="xsd:string"/>
     <element name="birthPlace" nillable="true" type="xsd:string"/>
     <element name="birthProvince" nillable="true" type="xsd:string"/>
     <element name="segmentFlag2" nillable="true" type="xsd:string"/>
     <element name="street" nillable="true" type="xsd:string"/>
     <element name="streetNumber" nillable="true" type="xsd:string"/>
     <element name="town" nillable="true" type="xsd:string"/>
     <element name="province" nillable="true" type="xsd:string"/>
     <element name="segmentFlag3" nillable="true" type="xsd:string"/>
     <element name="previousStreet" nillable="true" type="xsd:string"/>
     <element name="previousStreetNumber" nillable="true" type="xsd:string"/>
     <element name="previousTown" nillable="true" type="xsd:string"/>
     <element name="previousProvince" nillable="true" type="xsd:string"/>
     <element name="segmentFlag5" nillable="true" type="xsd:string"/>
     <element name="dossierId" nillable="true" type="xsd:string"/>
     <element name="segmentFlag6" nillable="true" type="xsd:string"/>
     <element name="surname6" nillable="true" type="xsd:string"/>
     <element name="name6" nillable="true" type="xsd:string"/>
     <element name="gender6" nillable="true" type="xsd:string"/>
     <element name="birthDate6" nillable="true" type="xsd:string"/>
     <element name="birthPlace6" nillable="true" type="xsd:string"/>
     <element name="birthProvince6" nillable="true" type="xsd:string"/>
     <element name="applicationAlias" nillable="true" type="xsd:string"/>
     <element name="taxCode" nillable="true" type="xsd:string"/>
     <element name="indiType1" nillable="true" type="xsd:string"/>
     <element name="indiType2" nillable="true" type="xsd:string"/>
     <element name="creditType1" nillable="true" type="xsd:string"/>
     <element name="zipCode" nillable="true" type="xsd:string"/>
     <element name="previousZipCode" nillable="true" type="xsd:string"/>
     <element name="creditType2" nillable="true" type="xsd:string"/>
     <element name="creditReason" nillable="true" type="xsd:string"/>
     <element name="requestedAmount" nillable="true" type="xsd:string"/>
     <element name="installmentsNumber" nillable="true" type="xsd:string"/>
     <element name="proposalNumber" nillable="true" type="xsd:string"/>
    </sequence>
   </complexType>
   <complexType name="GetExperianDataOutput">
    <sequence>
     <element name="x030" nillable="true" type="tns2:ExperianX030OutputMap"/>
     <element name="x031" nillable="true" type="tns2:ExperianX031OutputMap"/>
     <element name="x032" nillable="true" type="tns2:ExperianX032OutputMap"/>
     <element name="x033" nillable="true" type="tns2:ExperianX033OutputMap"/>
    </sequence>
   </complexType>
   <complexType name="ExperianX030OutputMap">
    <sequence>
     <element name="requestsNumber" nillable="true" type="xsd:string"/>
     <element name="valTotRich" nillable="true" type="xsd:string"/>
     <element name="refusedRequestsNumber" nillable="true" type="xsd:string"/>
     <element name="waivedRequestsNumber" nillable="true" type="xsd:string"/>
     <element name="acceptedRequestsNumber" nillable="true" type="xsd:string"/>
     <element name="numContratti" nillable="true" type="xsd:string"/>
     <element name="impegnoMensileRate" nillable="true" type="xsd:string"/>
     <element name="saldoTotRateChiro" nillable="true" type="xsd:string"/>
     <element name="saldoTotIpotec" nillable="true" type="xsd:string"/>
     <element name="peggStatoUlt12Mesi" nillable="true" type="xsd:string"/>
     <element name="peggStatoAnteUlt12Mesi" nillable="true" type="xsd:string"/>
     <element name="peggStatoAccens" nillable="true" type="xsd:string"/>
     <element name="rateMoroseMagg7" nillable="true" type="xsd:string"/>
     <element name="rateMoroseDa3A6" nillable="true" type="xsd:string"/>
     <element name="rateMoroseDa1A2" nillable="true" type="xsd:string"/>
     <element name="delphiScore" nillable="true" type="xsd:string"/>
     <element name="gruppoDiRischio" nillable="true" type="xsd:string"/>
    </sequence>
   </complexType>
   <complexType name="ExperianX031OutputMap">
    <sequence>
     <element name="ndg" nillable="true" type="xsd:string"/>
     <element name="statoPratica" nillable="true" type="xsd:string"/>
     <element name="tipoOperazione" nillable="true" type="xsd:string"/>
     <element name="progFinanziatore" nillable="true" type="xsd:string"/>
     <element name="ruolo" nillable="true" type="xsd:string"/>
     <element name="dataStipula" nillable="true" type="xsd:string"/>
     <element name="dataFineContratto" nillable="true" type="xsd:string"/>
     <element name="dataAggiornamento" nillable="true" type="xsd:string"/>
     <element name="profInsoUlt12Mesi" nillable="true" type="xsd:string"/>
     <element name="numRate" nillable="true" type="xsd:string"/>
     <element name="impRateMediaMens" nillable="true" type="xsd:string"/>
     <element name="impRateInso" nillable="true" type="xsd:string"/>
     <element name="numMaxInso" nillable="true" type="xsd:string"/>
    </sequence>
   </complexType>
   <complexType name="ExperianX032OutputMap">
    <sequence>
     <element name="nome" nillable="true" type="xsd:string"/>
     <element name="localita" nillable="true" type="xsd:string"/>
     <element name="via" nillable="true" type="xsd:string"/>
     <element name="provincia" nillable="true" type="xsd:string"/>
     <element name="dataProtesto" nillable="true" type="xsd:string"/>
     <element name="valProt" nillable="true" type="xsd:string"/>
     <element name="tipoEffetto" nillable="true" type="xsd:string"/>
     <element name="indicProt" nillable="true" type="xsd:string"/>
    </sequence>
   </complexType>
   <complexType name="ExperianX033OutputMap">
    <sequence>
     <element name="nome" nillable="true" type="xsd:string"/>
     <element name="via" nillable="true" type="xsd:string"/>
     <element name="luogo" nillable="true" type="xsd:string"/>
     <element name="provincia" nillable="true" type="xsd:string"/>
     <element name="indiDato" nillable="true" type="xsd:string"/>
     <element name="dataRegis" nillable="true" type="xsd:string"/>
     <element name="importo" nillable="true" type="xsd:string"/>
     <element name="soggFavore" nillable="true" type="xsd:string"/>
    </sequence>
   </complexType>
  </schema>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:tns2="http://dto.wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <import namespace="http://dto.wsutil.gwc.xframe.usi.it"/>
   <element name="getExperianData">
    <complexType>
     <sequence>
      <element name="input" nillable="true" type="tns2:GetExperianDataInput"/>
     </sequence>
    </complexType>
   </element>
   <element name="getExperianDataResponse">
    <complexType>
     <sequence>
      <element name="getExperianDataReturn" nillable="true" type="tns2:GetExperianDataOutput"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="getExperianDataResponse">

      <wsdl:part element="impl:getExperianDataResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="getExperianDataRequest">

      <wsdl:part element="impl:getExperianData" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="Rating_CreditBureau">

      <wsdl:operation name="getExperianData">

         <wsdl:input message="impl:getExperianDataRequest" name="getExperianDataRequest"/>

         <wsdl:output message="impl:getExperianDataResponse" name="getExperianDataResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="Rating_CreditBureauSoapBinding" type="impl:Rating_CreditBureau">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="getExperianData">

         <wsdlsoap:operation soapAction="getExperianData"/>

         <wsdl:input name="getExperianDataRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="getExperianDataResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="Rating_CreditBureauService">

      <wsdl:port binding="impl:Rating_CreditBureauSoapBinding" name="Rating_CreditBureau">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/Rating_CreditBureau"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
