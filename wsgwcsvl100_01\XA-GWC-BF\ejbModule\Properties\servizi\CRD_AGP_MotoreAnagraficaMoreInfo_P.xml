<?xml version="1.0"?>
<!-- Generated utente="Monacò Girolamo" Timestamp="04/12/2015 10.59.00" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="X01"/>
                    <param name="X02"/>
                    <param name="X03"/>
					<param name="X04"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="WA12">   
						<param select="LEGALENTITY" name="X01"/>
						<param select="DATARIF" name="X02"/>
						<param select="TIPO_FLUSSO" name="X03"/>
						<param select="DESCR_LEGALENTITY" name="X04"/>
						<param select="PERIODICITA" name="X05"/>
					</hostService>
					<hostService id="1" name="W12C">
						<param select="OK" name="X01"/>
						<param select="MESSAGGIO_OK" name="X02"/>
						<param select="LEGALENTITY" name="X03"/>
						<param select="FLUSSO" name="X04"/>
					</hostService>
					<hostService id="1" name="WB12">
						<param select="LEGALENTITY" name="X01"/>
						<param select="DATARIF" name="X02"/>
						<param select="TIPO_FLUSSO" name="X03"/>
						<param select="DESCR_LEGALENTITY" name="X04"/>
						<param select="PERIODICITA" name="X05"/>
					</hostService>
					<hostService id="1" name="W15A">
						<param select="LEGALENTITY" name="X01"/>
						<param select="NDG" name="X02"/>
						<param select="PRODUCT" name="X03"/>
						<param select="RATING" name="X04"/>
						<param select="EDF" name="X05"/>
						<param select="EXPOSURE_AMOUNT" name="X06"/>
						<param select="LDG" name="X07"/>
						<param select="OUTSTANDING" name="X08"/>
						<param select="NEXT_EXPOSURE" name="X09"/>
						<param select="VERMITTLER" name="X10"/>
						<param select="PRODUKTGRUPPE" name="X11"/>
						<param select="PARTNERNAME" name="X12"/>
						<param select="CLASS-RATING" name="X13"/>
						<param select="DATA-RIF" name="X14"/>
					</hostService>
					<hostService id="1" name="W15C">
						<param select="OK" name="X01"/>
						<param select="MESSAGGIO_OK" name="X02"/>
						<param select="TOTALE_RIGHE" name="001"/>
					</hostService>
					<hostService id="1" name="W58A">
						<param select="SNDG" name="X01"/>
						<param select="PD" name="X02"/>
						<param select="SEP-RATING" name="X03"/>
						<param select="RELEASED-PD-TRISK" name="X04"/>
						<param select="RELEASED-SEP-TRISK" name="X05"/>
						<param select="COD-COUNTRY" name="X06"/>
						<param select="BONDS-LGD" name="X07"/>
						<param select="LOANS-LGD" name="X08"/>
						<param select="CLASSE_RATING" name="X09"/>
						<param select="ABI" name="X10"/>
						<param select="NDG" name="X11"/>
						<param select="FONTE" name="X12"/>
						<param select="DATA-RIF" name="X13"/>
						<param select="RATING" name="X14"/>
						<param select="RATING-TRISK" name="X15"/>
						<param select="RATING-INT-SCALE" name="X16"/>
						<param select="RATING-INT-SCALE-TRISK" name="X17"/>
						<param select="ANNUAL REPORT" name="X18"/>
						<param select="AGING" name="X19"/>
					</hostService>
					<hostService id="1" name="W58B">
						<param select="OK" name="X01"/>
						<param select="MESSAGGIO_OK" name="X02"/>
					</hostService>
					<hostService id="1" name="W26A">
						<param select="DAZEN" name="X01"/>
					</hostService>
					<hostService id="1" name="W26C">
						<param select="OK" name="X01"/>
						<param select="MESSAGGIO_OK" name="X02"/>
					</hostService>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>GLXL</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>GL54</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>