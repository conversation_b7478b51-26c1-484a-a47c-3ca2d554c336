<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1284378909129">
      <package-mapping id="PackageMapping_1284378909145">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1284378909145">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1284378909145">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1284378909145">
         <service-interface>it.usi.xframe.gwc.wsutil.CallServiceTokenService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1284378909145">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceTokenService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1284378909145">
            <port-name>CallServiceToken</port-name>
            <java-port-name>CallServiceToken</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1284378909145">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallServiceToken</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1284378909145">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceToken</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1284378909145">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceTokenSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1284378909145">
            <java-method-name>callServiceToken</java-method-name>
            <wsdl-operation>callServiceToken</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284378909145">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284378909145">
                  <wsdl-message id="WSDLMessage_1284378909145">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceTokenRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>service</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284378909146">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284378909146">
                  <wsdl-message id="WSDLMessage_1284378909146">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceTokenRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>params</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284378909147">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284378909147">
                  <wsdl-message id="WSDLMessage_1284378909147">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceTokenRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep1</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284378909148">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284378909148">
                  <wsdl-message id="WSDLMessage_1284378909148">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceTokenRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep2</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1284378909145">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1284378909149">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>callServiceTokenResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>callServiceTokenReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
