<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="NSW8I-FUNZIONE"/>
					<param name="NSW8I-MASCHERA"/>
					<param name="NSW8I-MATRICOLA"/>
					<param name="NSW8I-CODICE-PRATICA"/>
					<param name="NSW8I-DATA-PROP-REVISIONE"/>
					<param name="NSW8I-STATO-PROCESSO"/>
					<param name="NSW8I-CODICE-SETT-SOTTOSETT"/>
					<param name="NSW8I-LIVELLO-ABILITAZIONE"/>
					<param name="NSW8I-NDG-CLIENTE"/>
					<param name="NSW8I-DESCRIZIONE-CLIENTE"/>
					<param name="NSW8I-NUMERO-PROPOSTA-DOCC"/>
					<param name="NSW8I-NDG-GESTORE"/>
					<param name="NSW8I-INTESTAZIONE-GESTORE"/>
					<param name="NSW8I-GESTORE-TELEFONO"/>
					<param name="NSW8I-GESTORE-E-MAIL"/>
					<param name="NSW8I-NDG-RATING-DESK"/>
					<param name="NSW8I-INTESTAZIONE-RATING-DESK"/>
					<param name="NSW8I-COD-ENTE-PROPONENTE"/>
					<param name="NSW8I-DESCR-ENTE-PROPONENTE"/>
					<param name="NSW8I-CODICE-FILIALE"/>
					<param name="NSW8I-DESCRIZIONE-FILIALE"/>
					<param name="NSW8I-COD-ORGAN-DELIB-CREDIT"/>
					<param name="NSW8I-DESCR-ORGAN-DELIB-CREDIT"/>
					<param name="NSW8I-NDG-GRUPPO-ECONOMICO"/>
					<param name="NSW8I-DESCR-GRUPPO-ECONOMICO"/>
					<param name="NSW8I-ATTIVITA"/>
					<param name="NSW8I-ARRAY-PROPOSTE"/>
					<param name="NSW8I-CODICE-RAE"/>
					<param name="NSW8I-CODICE-SAE"/>
					<param name="NSW8I-ALTRI-RAPPORTI-UCI"/>
					<param name="NSW8I-RATING-ESTERNO-MOODY"/>
					<param name="NSW8I-RATING-ESTERNO-S-P"/>
					<param name="NSW8I-RATING-ESTERNO-FITCH"/>
					<param name="NSW8I-NOTE-ISPETTIVE"/>
					<param name="NSW8I-CBSCORE-DATA-BILANCIO"/>
					<param name="NSW8I-CBSCORE-VALORE-ATTUALE"/>
					<param name="NSW8I-CBSCORE-PROP-REVISIONE"/>
					<param name="NSW8I-CBSCORE-VAL-FORZATURA"/>
					<param name="NSW8I-CBSCORE-SCADENZA-FORZ"/>
					<param name="NSW8I-VAR-CBQUEST-DATA-QUEST"/>
					<param name="NSW8I-VAR-CBQUEST-VAL-ATTUALE"/>
					<param name="NSW8I-VAR-CBQUEST-PROP-REVIS"/>
					<param name="NSW8I-VAR-CBQUEST-VAL-FORZ"/>
					<param name="NSW8I-VAR-CBQUEST-SCAD-FORZ"/>
					<param name="NSW8I-RATING1-VALORE-ATTUALE"/>
					<param name="NSW8I-RATING1-PROP-REVIS"/>
					<param name="NSW8I-RATING1-VAL-FORZ"/>
					<param name="NSW8I-RATING1-SCAD-FORZ"/>
					<param name="NSW8I-VAR-GEOSET-VAL-ATTUALE"/>
					<param name="NSW8I-VAR-GEOSET-PRO-REVIS"/>
					<param name="NSW8I-VAR-GEOSET-VAL-FORZ"/>
					<param name="NSW8I-VAR-GEOSET-SCAD-FORZ"/>
					<param name="NSW8I-RATING2-VALORE-ATTUALE"/>
					<param name="NSW8I-RATING2-PROP-REVIS"/>
					<param name="NSW8I-RATING2-VAL-FORZ"/>
					<param name="NSW8I-RATING2-SCAD-FORZ"/>
					<param name="NSW8I-SMR-DATA-RIFERIMENTO"/>
					<param name="NSW8I-SMR-VAL-ATTUALE"/>
					<param name="NSW8I-SMR-PROP-REVIS"/>
					<param name="NSW8I-SMR-VAL-FORZ"/>
					<param name="NSW8I-SMR-SCAD-FORZ"/>
					<param name="NSW8I-SCORECR-DATA"/>
					<param name="NSW8I-SCORECR-VAL-ATTUALE"/>
					<param name="NSW8I-SCORECR-PROP-REVIS"/>
					<param name="NSW8I-SCORECR-VAL-FORZ"/>
					<param name="NSW8I-SCORECR-SCAD-FORZ"/>
					<param name="NSW8I-RIC-DATA-RIFERIMENTO"/>
					<param name="NSW8I-RIC-VAL-ATTUALE"/>
					<param name="NSW8I-RIC-PROP-REVIS"/>
					<param name="NSW8I-RIC-VAL-FORZ"/>
					<param name="NSW8I-RIC-VAL-SCAD-FORZ"/>
					<param name="NSW8I-NUOVO-RIC"/>
					<param name="NSW8I-DOMANDE-RISPOSTE"/>
					<param name="NSW8I-ALTRO"/>
					<param name="NSW8I-INDIRIZZO-APPLICAZIONE"/>
					<param name="NSW8I-DATA-INIZIO-VALIDITA"/>
					<param name="NSW8I-DATA-FINE-VALIDITA"/>
					<param name="NSW8I-MOTIVAZ-PROPOSTA"/>
					<param name="NSW8I-MOTIVAZ-ADDETTO-RDESK"/>
					<param name="NSW8I-MOTIVAZ-RESP-RDESK"/>
					<param name="NSW8I-MOTIVAZ-COMIT-RDESK"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>NSW8RDES-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>NSW8</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
