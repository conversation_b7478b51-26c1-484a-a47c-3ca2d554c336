package it.usi.xframe.gwc.pfstruts.actions;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class TestPge extends Action {

	private Log logger = LogFactory.getLog(this.getClass());

	public static final String REQUESTPARAM_NOTVALID = "error.params.invalid";
	public static final String LOGIN_ERROR = "error.login";

	public static final String STORAGE_OBJECT_NAME = "sessionData";

	// Execute Implementation
	public ActionForward execute(
		ActionMapping mapping,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {

		Enumeration en = request.getAttributeNames();
		while (en.hasMoreElements()) {
			logger.info("request element = [" + (String) en.nextElement() + "]");
		}

		Enumeration en2 = request.getParameterNames();
		while (en2.hasMoreElements()) {
			logger.info("request element = [" + (String) en2.nextElement() + "]");
		}

		String sportello = request.getParameter("sportello");
		String banca = request.getParameter("banca");
		String user = request.getParameter("userid");
		String profilo_bios = request.getParameter("profilo_bios");
		String token = request.getParameter("token");

		logger.info("executing " + this.getClass() + "    sportello = [" + sportello + "]");
		logger.info("executing " + this.getClass() + "        banca = [" + banca + "]");
		logger.info("executing " + this.getClass() + "         user = [" + user + "]");
		logger.info("executing " + this.getClass() + " profilo_bios = [" + profilo_bios + "]");
		logger.info("executing " + this.getClass() + "        token = [" + token + "]");

		request.setAttribute("param1", sportello);
		request.setAttribute("param2", banca);
		request.setAttribute("param3", user);
		request.setAttribute("param4", profilo_bios);
		request.setAttribute("param5", token);

		return mapping.findForward("ok");
	}
	
}
