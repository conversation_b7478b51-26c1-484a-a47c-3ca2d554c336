<%@ taglib uri="http://jakarta.apache.org/struts/tags-tiles" prefix="tiles" %>
<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>
<%@ taglib uri="http://java.sun.com/jstl/fmt" prefix="fmt" %>

<fmt:setLocale value="${sessionScope['org.apache.struts.action.LOCALE']}"/>
<tiles:importAttribute />

<c:choose>
	<c:when test="${flat == ''}">
		<fmt:message key="cpt.cal.error.calendar"><fmt:param value="flat"/></fmt:message>
	</c:when>
	<c:when test="${flatCallback == ''}">
		<fmt:message key="cpt.cal.error.calendar"><fmt:param value="flatCallback"/></fmt:message>
	</c:when>
	<c:otherwise>
		<script type="text/javascript">
			<tiles:getAsString name="flatCallbackScript"/>
			
			Calendar.setup(
				{
					<tiles:insert definition="cpt.cal.calendar.test.base">
						<tiles:put name="ifFormat"><tiles:getAsString name="ifFormat"/></tiles:put>
						<tiles:put name="daFormat"><tiles:getAsString name="daFormat"/></tiles:put>
						<tiles:put name="dateStatusFunc"><tiles:getAsString name="dateStatusFunc"/></tiles:put>
						<tiles:put name="firstDay"><tiles:getAsString name="firstDay"/></tiles:put>
						<tiles:put name="weekNumbers"><tiles:getAsString name="weekNumbers"/></tiles:put>
						<tiles:put name="align"><tiles:getAsString name="align"/></tiles:put>
						<tiles:put name="range"><tiles:getAsString name="range"/></tiles:put>
						<tiles:put name="onSelect"><tiles:getAsString name="onSelect"/></tiles:put>
						
						<tiles:put name="date"><tiles:getAsString name="date"/></tiles:put>
						<tiles:put name="showsTime"><tiles:getAsString name="showsTime"/></tiles:put>
						<tiles:put name="timeFormat"><tiles:getAsString name="timeFormat"/></tiles:put>
						<tiles:put name="electric"><tiles:getAsString name="electric"/></tiles:put>
						<tiles:put name="position"><tiles:getAsString name="position"/></tiles:put>
						<tiles:put name="cache"><tiles:getAsString name="cache"/></tiles:put>
						<tiles:put name="showOthers"><tiles:getAsString name="showOthers"/></tiles:put>

					</tiles:insert>					
					flat : "<tiles:getAsString name="flat"/>",
					flatCallback : <tiles:getAsString name="flatCallback"/>
				}
			)
		</script>
	</c:otherwise>
</c:choose>