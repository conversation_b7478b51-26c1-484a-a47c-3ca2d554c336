package it.usi.xframe.gwc.wsutil.dto;

import java.io.Serializable;

public class GetExperianDataInput implements Serializable {
	private static final long serialVersionUID = 1L;

	private String ndg = "";
	private String requestType = "";
	private String requestDataType = "";
	private String applicant = "";
	private String currencyFlag = "";
	private String surname = "";
	private String name = "";
	private String gender = "";
	private String birthDate = "";
	private String birthPlace = "";
	private String birthProvince = "";
	private String segmentFlag2 = "";
	private String street = "";
	private String streetNumber = "";
	private String town = "";
	private String province = "";
	private String segmentFlag3 = "";
	private String previousStreet = "";
	private String previousStreetNumber = "";
	private String previousTown = "";
	private String previousProvince = "";
	private String segmentFlag5 = "";
	private String dossierId = "";
	private String segmentFlag6 = "";
	private String surname6 = "";
	private String name6 = "";
	private String gender6 = "";
	private String birthDate6 = "";
	private String birthPlace6 = "";
	private String birthProvince6 = "";
	private String applicationAlias = "";
	private String taxCode = "";
	private String indiType1 = "";
	private String indiType2 = "";
	private String creditType1 = "";
	private String zipCode = "";
	private String previousZipCode = "";
	private String creditType2 = "";
	private String creditReason = "";
	private String requestedAmount = "";
	private String installmentsNumber = "";
	private String proposalNumber = "";
	
	public GetExperianDataInput() {
	}

	public String getNdg() {
		return ndg;
	}

	public void setNdg(String ndg) {
		this.ndg = ndg;
	}

	public String getRequestType() {
		return requestType;
	}

	public void setRequestType(String requestType) {
		this.requestType = requestType;
	}

	public String getRequestDataType() {
		return requestDataType;
	}

	public void setRequestDataType(String requestDataType) {
		this.requestDataType = requestDataType;
	}

	public String getApplicant() {
		return applicant;
	}

	public void setApplicant(String applicant) {
		this.applicant = applicant;
	}

	public String getCurrencyFlag() {
		return currencyFlag;
	}

	public void setCurrencyFlag(String currencyFlag) {
		this.currencyFlag = currencyFlag;
	}

	public String getSurname() {
		return surname;
	}

	public void setSurname(String surname) {
		this.surname = surname;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(String birthDate) {
		this.birthDate = birthDate;
	}

	public String getBirthPlace() {
		return birthPlace;
	}

	public void setBirthPlace(String birthPlace) {
		this.birthPlace = birthPlace;
	}

	public String getBirthProvince() {
		return birthProvince;
	}

	public void setBirthProvince(String birthProvince) {
		this.birthProvince = birthProvince;
	}

	public String getSegmentFlag2() {
		return segmentFlag2;
	}

	public void setSegmentFlag2(String segmentFlag2) {
		this.segmentFlag2 = segmentFlag2;
	}

	public String getStreet() {
		return street;
	}

	public void setStreet(String street) {
		this.street = street;
	}

	public String getStreetNumber() {
		return streetNumber;
	}

	public void setStreetNumber(String streetNumber) {
		this.streetNumber = streetNumber;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getSegmentFlag3() {
		return segmentFlag3;
	}

	public void setSegmentFlag3(String segmentFlag3) {
		this.segmentFlag3 = segmentFlag3;
	}

	public String getPreviousStreet() {
		return previousStreet;
	}

	public void setPreviousStreet(String previousStreet) {
		this.previousStreet = previousStreet;
	}

	public String getPreviousStreetNumber() {
		return previousStreetNumber;
	}

	public void setPreviousStreetNumber(String previousStreetNumber) {
		this.previousStreetNumber = previousStreetNumber;
	}

	public String getPreviousTown() {
		return previousTown;
	}

	public void setPreviousTown(String previousTown) {
		this.previousTown = previousTown;
	}

	public String getPreviousProvince() {
		return previousProvince;
	}

	public void setPreviousProvince(String previousProvince) {
		this.previousProvince = previousProvince;
	}

	public String getSegmentFlag5() {
		return segmentFlag5;
	}

	public void setSegmentFlag5(String segmentFlag5) {
		this.segmentFlag5 = segmentFlag5;
	}

	public String getDossierId() {
		return dossierId;
	}

	public void setDossierId(String dossierId) {
		this.dossierId = dossierId;
	}

	public String getSegmentFlag6() {
		return segmentFlag6;
	}

	public void setSegmentFlag6(String segmentFlag6) {
		this.segmentFlag6 = segmentFlag6;
	}

	public String getSurname6() {
		return surname6;
	}

	public void setSurname6(String surname6) {
		this.surname6 = surname6;
	}

	public String getName6() {
		return name6;
	}

	public void setName6(String name6) {
		this.name6 = name6;
	}

	public String getGender6() {
		return gender6;
	}

	public void setGender6(String gender6) {
		this.gender6 = gender6;
	}

	public String getBirthDate6() {
		return birthDate6;
	}

	public void setBirthDate6(String birthDate6) {
		this.birthDate6 = birthDate6;
	}

	public String getBirthPlace6() {
		return birthPlace6;
	}

	public void setBirthPlace6(String birthPlace6) {
		this.birthPlace6 = birthPlace6;
	}

	public String getBirthProvince6() {
		return birthProvince6;
	}

	public void setBirthProvince6(String birthProvince6) {
		this.birthProvince6 = birthProvince6;
	}

	public String getApplicationAlias() {
		return applicationAlias;
	}

	public void setApplicationAlias(String applicationAlias) {
		this.applicationAlias = applicationAlias;
	}

	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	public String getIndiType1() {
		return indiType1;
	}

	public void setIndiType1(String indiType1) {
		this.indiType1 = indiType1;
	}

	public String getIndiType2() {
		return indiType2;
	}

	public void setIndiType2(String indiType2) {
		this.indiType2 = indiType2;
	}

	public String getCreditType1() {
		return creditType1;
	}

	public void setCreditType1(String creditType1) {
		this.creditType1 = creditType1;
	}

	public String getZipCode() {
		return zipCode;
	}

	public void setZipCode(String zipCode) {
		this.zipCode = zipCode;
	}

	public String getPreviousZipCode() {
		return previousZipCode;
	}

	public void setPreviousZipCode(String previousZipCode) {
		this.previousZipCode = previousZipCode;
	}

	public String getCreditType2() {
		return creditType2;
	}

	public void setCreditType2(String creditType2) {
		this.creditType2 = creditType2;
	}

	public String getCreditReason() {
		return creditReason;
	}

	public void setCreditReason(String creditReason) {
		this.creditReason = creditReason;
	}

	public String getRequestedAmount() {
		return requestedAmount;
	}

	public void setRequestedAmount(String requestedAmount) {
		this.requestedAmount = requestedAmount;
	}

	public String getInstallmentsNumber() {
		return installmentsNumber;
	}

	public void setInstallmentsNumber(String installmentsNumber) {
		this.installmentsNumber = installmentsNumber;
	}

	public String getProposalNumber() {
		return proposalNumber;
	}

	public void setProposalNumber(String proposalNumber) {
		this.proposalNumber = proposalNumber;
	}
}
