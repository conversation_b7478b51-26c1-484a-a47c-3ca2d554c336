<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>
<%@ taglib uri="/WEB-INF/struts-tiles.tld" prefix="tiles" %>
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<HTML>
	<HEAD>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<TITLE>GAUSS test page</TITLE>
		<link rel="stylesheet" href="/XA-UTL-PF/public/css/utl_common.css" type="text/css"/>
		<link rel="stylesheet" href="/XA-GWC-PF/cpt/sc/css/calendar.css" type="text/css"/>
		<SCRIPT type="text/javascript" src="/XA-GWC-PF/cpt/cal/calendar.js"></SCRIPT>
		<SCRIPT type="text/javascript" src="/XA-GWC-PF/cpt/cal/calendar-setup.js"></SCRIPT>
		<SCRIPT type="text/javascript" src="/XA-GWC-PF/cpt/cal/lang/calendar-it.js"></SCRIPT>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/Utilities.js"></script>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/NumberFormat.js"></script>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/TextFieldMask.js"></script>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/DateFormat.js"></script>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/xfrFixed.js"></script>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/xfrMenu.js"></script>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/xfrMenu_addins.js"></script>
		<script type="text/javascript" src="/XA-UTL-PF/public/script/xfrWindowsManager.js"></script>		
		<script type="text/javascript" src="/XA-UTL-PF/public/script/xlcTagUtils.js"></script>
		<script type="text/javascript">
		<!--
			function splitter()
			{
				var ar = document.forms[0].service.value.split("@");
				document.forms[0].transaction.value = ar[0];
				document.forms[0].serviceName.value = ar[1];
				//alert(document.forms[0].transaction.value + "\n" + document.forms[0].serviceName.value);			
			}
		
			function reload()
			{
				splitter();
				document.forms[0].action = "gauss_form.do";
				document.forms[0].submit();
			}
			
			function call()
			{
				splitter();
				document.forms[0].action = "gauss_call.do";
				document.forms[0].submit();
			}
		//-->
		</script>
	</HEAD>
	<BODY>
		<form action="gauss_form.do" method="post">
			<input type="hidden" name="appLang" value="it" />
			<input type="hidden" name="transaction" id ="transaction"/>
			<input type="hidden" name="serviceName" id ="serviceName"/>
			
			<div>
				Carica i campi da : 
				<select name="service" id="service" onchange="reload()">
					<c:forEach items="${responseClass.list}" var="opt">
						<c:choose>
							<c:when test="${opt.code==service}"><c:set var="selected">selected="selected"</c:set> </c:when>
							<c:otherwise><c:set var="selected"></c:set></c:otherwise>
						</c:choose>				
						<option value="<c:out value="${opt.code}"/>" <c:out value="${selected}"/>>
							<c:out value="${opt.description}"/>
						</option>			
					</c:forEach>
				</select>
			</div>

			<div>
				Gauss Type : 
				<select name="gauss_type" id="gauss_type" onchange="reload()">
					<option value="1" <c:if test="${gauss_type=='1'}">selected="selected"</c:if>>Gauss 1</option>			
					<option value="2" <c:if test="${gauss_type=='2'}">selected="selected"</c:if>>Gauss 2</option>			
				</select>
			</div>
			
			<a name="top" style="display: none;"></a> 
			
			<div style="margin-top:20px;">
				<c:if test="${not empty fields}">
				<input type="submit" name="CALL" value="CALL" onclick="call()"/>
				</c:if>
			</div>
		
			<div style="margin-top:20px;">
				<table>
					<tbody>
						<c:forEach items="${fields}" var="field">
							<tr>
							<td>
								<c:out value="${field.nome_camp}"/>
								<input type="hidden" name="<c:out value="${field.nome_camp}"/>_formato" value="<c:out value="${field.formato}"/>" />
								<input type="hidden" name="<c:out value="${field.nome_camp}"/>_len" value="<c:out value="${field.len}"/>" />
							</td>
							<td>
								<c:choose>
									<c:when test="${field.formato=='A' && field.len==10}">
										<input type="text" id="<c:out value="${field.nome_camp}"/>" name="<c:out value="${field.nome_camp}"/>" maxlength="10" size="11" mask="99.99.9999" onpaste="return tbPaste(this);" message=""  onfocus="return tbFocus(this);" onkeydown="return tbMask(this);" onblur="dfFormatDateByLanguage(this,'Data Errata', '<c:out value="${field.nome_camp}"/>')" class="utl_input">
										<img src="/XA-UTL-PF/public/images/calendar.gif" alt="" border="0" id="Date_<c:out value="${field.nome_camp}"/>" style="position:relative;top:1">		
									    <tiles:insert definition="cpt.cal.calendar.popup">
									    	<tiles:put name="inputField"><c:out value="${field.nome_camp}"/></tiles:put>
									        <tiles:put name="button">Date_<c:out value="${field.nome_camp}"/></tiles:put>
									        <tiles:put name="displayArea" value=""/>
									        <tiles:put name="eventName" value="click"/>
									        <tiles:put name="singleClick" value="false"/>
									        <tiles:put name="ifFormat">%d.%m.%Y</tiles:put>
										</tiles:insert>							
									</c:when>
									<c:when test="${field.formato=='N'}">
										<input 	class="utl_input" 
												type="text" 
												name="<c:out value="${field.nome_camp}"/>" 
												id="<c:out value="${field.nome_camp}"/>" 
												size="50" 
												maxlength="<c:out value="${field.len}"/>" 
												onblur="formatNumberByLanguage(this, <c:out value="${field.len_dec}"/>);"
												style="text-align:right"/>								
									</c:when>
									<c:otherwise>
										<input name="<c:out value="${field.nome_camp}"/>" id="<c:out value="${field.nome_camp}"/>" maxlength="<c:out value="${field.len}"/>" type="text" size="50"/>
										[<c:out value="${field.len}"/>]
									</c:otherwise>
								</c:choose>
							</td>
							</tr>
						</c:forEach>
					</tbody>
				</table>
			</div>
					
			<a href="#top">Top</a>
		</form>
	</BODY>
</HTML>

