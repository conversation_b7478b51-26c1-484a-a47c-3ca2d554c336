/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class CallService {
	
	public CallService(){	
	}

	public String callService(String service, String params, String sep1, String sep2) throws Exception { 

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().callService(service, params, sep1, sep2);
	}     
}
