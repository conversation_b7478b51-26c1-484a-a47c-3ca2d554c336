/**
 * HandleRequestResponse.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * cf190823.02 v62608112801
 */

package it.usi.xframe.gwc.wsutil;

public class HandleRequestResponse  implements java.io.Serializable {
    private boolean handleRequestReturn;

    public HandleRequestResponse() {
    }

    public boolean isHandleRequestReturn() {
        return handleRequestReturn;
    }

    public void setHandleRequestReturn(boolean handleRequestReturn) {
        this.handleRequestReturn = handleRequestReturn;
    }

    private transient java.lang.ThreadLocal __history;
    public boolean equals(java.lang.Object obj) {
        if (obj == null) { return false; }
        if (obj.getClass() != this.getClass()) { return false;}
        HandleRequestResponse other = (HandleRequestResponse) obj;
        boolean _equals;
        _equals = true
            && this.handleRequestReturn == other.isHandleRequestReturn();
        if (!_equals) { return false; }
        if (__history == null) {
            synchronized (this) {
                if (__history == null) {
                    __history = new java.lang.ThreadLocal();
                }
            }
        }
        HandleRequestResponse history = (HandleRequestResponse) __history.get();
        if (history != null) { return (history == obj); }
        if (this == obj) return true;
        __history.set(obj);
        __history.set(null);
        return true;
    }

    private transient java.lang.ThreadLocal __hashHistory;
    public int hashCode() {
        if (__hashHistory == null) {
            synchronized (this) {
                if (__hashHistory == null) {
                    __hashHistory = new java.lang.ThreadLocal();
                }
            }
        }
        HandleRequestResponse history = (HandleRequestResponse) __hashHistory.get();
        if (history != null) { return 0; }
        __hashHistory.set(this);
        int _hashCode = 1;
        _hashCode += new Boolean(isHandleRequestReturn()).hashCode();
        __hashHistory.set(null);
        return _hashCode;
    }

}
