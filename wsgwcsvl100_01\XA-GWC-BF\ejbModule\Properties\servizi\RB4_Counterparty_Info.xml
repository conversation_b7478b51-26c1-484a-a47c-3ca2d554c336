<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R52I-PROPOSAL-ID"/>
					<param name="R52I-FUNCTION"/>
					<param name="R52I-USER-ID"/>
					<param name="R52I-MOODY-COUNTRY-M"/>
					<param name="R52I-MOODY-COUNTRY-C"/>
					<param name="R52I-MOODY-COUNTRY-CM"/>
					<param name="R52I-SANDP-COUNTRY-M"/>
					<param name="R52I-SANDP-COUNTRY-C"/>
					<param name="R52I-SANDP-COUNTRY-CM"/>
					<param name="R52I-FITCH-COUNTRY-M"/>
					<param name="R52I-FITCH-COUNTRY-C"/>
					<param name="R52I-FITCH-COUNTRY-CM"/>
					<param name="R52I-MOODY-CP-M"/>
					<param name="R52I-MOODY-CP-C"/>
					<param name="R52I-MOODY-CP-CM"/>
					<param name="R52I-SANDP-CP-M"/>
					<param name="R52I-SANDP-CP-C"/>
					<param name="R52I-SANDP-CP-CM"/>
					<param name="R52I-FITCH-CP-M"/>
					<param name="R52I-FITCH-CP-C"/>
					<param name="R52I-FITCH-CP-CM"/>
					<param name="R52I-MOODY-RS-M"/>
					<param name="R52I-MOODY-RS-C"/>
					<param name="R52I-MOODY-RS-CM"/>
					<param name="R52I-SANDP-RS-M"/>
					<param name="R52I-SANDP-RS-C"/>
					<param name="R52I-SANDP-RS-CM"/>
					<param name="R52I-FITCH-RS-M"/>
					<param name="R52I-FITCH-RS-C"/>
					<param name="R52I-FITCH-RS-CM"/> 
					<param name="R52I-MOTIVATION-A"/>
					<param name="R52I-MOTIVATION-M"/>					               
				</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB52-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB52</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
