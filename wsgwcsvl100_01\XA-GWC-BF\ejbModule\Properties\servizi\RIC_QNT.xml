<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="N52I-PROPOSAL-ID"/>
					<param name="N52I-FUNCTION"/>
					<param name="N52I-FLAG-ENV"/>
					<param name="N52I-QNT-STATUS"/>
					<param name="N52I-USER-ID"/>
					<param name="N52I-AUTH-LEV"/>
					<param name="N52I-NOTE-FIN"/>
					<param name="N52I-NOTE-CR"/>
					<param name="N52I-TIPO-MODELLO"/>
					<param name="N52I-DATA-BIL"/>
					<param name="N52I-IMPORTO-1"/>
					<param name="N52I-IMPORTO-2"/>
					<param name="N52I-IMPORTO-3"/>
					<param name="N52I-IMPORTO-4"/>
					<param name="N52I-IMPORTO-5"/>
					<param name="N52I-IMPORTO-6"/>
					<param name="N52I-IMPORTO-7"/>
					<param name="N52I-IMPORTO-8"/>
					<param name="N52I-IMPORTO-9"/>
					<param name="N52I-IMPORTO-10"/>
					<param name="N52I-IMPORTO-11"/>
					<param name="N52I-IMPORTO-12"/>
					<param name="N52I-IMPORTO-13"/>
					<param name="N52I-IMPORTO-14"/>
					<param name="N52I-IMPORTO-15"/>
					<param name="N52I-IMPORTO-16"/>
					<param name="N52I-IMPORTO-17"/>
					<param name="N52I-IMPORTO-18"/>
					<param name="N52I-IMPORTO-19"/>
					<param name="N52I-IMPORTO-20"/>
					<param name="N52I-IMPORTO-21"/>
					<param name="N52I-IMPORTO-22"/>
					<param name="N52I-IMPORTO-23"/>
					<param name="N52I-IMPORTO-24"/>
					<param name="N52I-NODE-1"/>
					<param name="N52I-NODE-2"/>
					<param name="N52I-NODE-3"/>
					<param name="N52I-NODE-4"/>
					<param name="N52I-NODE-5"/>
					<param name="N52I-NODE-6"/>
					<param name="N52I-NODE-7"/>
					<param name="N52I-NODE-8"/>
					<param name="N52I-NODE-9"/>
					<param name="N52I-NODE-10"/>
					<param name="N52I-NODE-11"/>
					<param name="N52I-NODE-12"/>
					<param name="N52I-NODE-13"/>
					<param name="N52I-NODE-14"/>
					<param name="N52I-NODE-15"/>
					<param name="N52I-NODE-16"/>
					<param name="N52I-NODE-17"/>
					<param name="N52I-NODE-18"/>
					<param name="N52I-NODE-19"/>
					<param name="N52I-NODE-20"/>
					<param name="N52I-NODE-21"/>
					<param name="N52I-NODE-22"/>
					<param name="N52I-NODE-23"/>
					<param name="N52I-NODE-24"/>		
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>NS52-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>NS52</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
