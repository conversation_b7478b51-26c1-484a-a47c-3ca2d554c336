/*
 * Created on Jan 24, 2006
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import it.usi.xframe.gwc.bfutil.GwcServiceFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class NPX_Fidi_InserimentoFidi {
	
	//	Default Constructor - RSA8 migration prerequisites	
	public NPX_Fidi_InserimentoFidi(){
			
	}	

	public String npxFidi_InserimentoFidi(String x00, String x01, String x02, String x03, String x06, String x08, String x09, String x10, String x11 ) throws Exception {

		return GwcServiceFactory.getInstance().getGwcMainServiceFacade().npxFidi_InserimentoFidi(x00, x01, x02, x03, x06, x08, x09, x10, x11 );
	}     
}
