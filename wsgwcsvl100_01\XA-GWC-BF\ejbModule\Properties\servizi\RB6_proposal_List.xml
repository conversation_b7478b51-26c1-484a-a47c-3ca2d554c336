<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R70I-PROPOSAL-ID"/>       
					<param name="R70I-FUNCTION"/>
					<param name="R70I-USER-ID"/>
					<param name="R70I-DT-BALANCE-SHEET"/>
					<param name="R70I-TYPE-BALANCE-SHEET"/>
					<param name="R70I-COMPANION-CODE"/>
					<param name="R70I-SNDG"/>
					<param name="R70I-IN-LE"/>
					<param name="R70I-IN-CUSTOMER-ID"/>
					<param name="R70I-COD-COUNTRY"/>
					<param name="R70I-SERVER"/>
					<param name="R70I-GROUP-SNDG"/>
					<param name="R70I-DT-RAT-ASS-FROM"/>
					<param name="R70I-DT-RAT-ASS-TO"/>
					<param name="R70I-DT-RAT-VAL-FROM"/>
					<param name="R70I-DT-RAT-VAL-TO"/>     
					<param name="R70I-FLAG-LAST-RAT"/>
					<param name="R70I-PROPOSAL-USERID"/>
					<param name="R70I-RATING-USERID"/>
					<param name="R70I-OVERRIDE-USERID"/>
					<param name="R70I-TS-SAVE"/>
					<param name="R70I-SEGM-USER"/>
					<param name="R70I-SEGM-MOT-A"/>
					<param name="R70I-SEGM-MOT-M"/>
					<param name="R70I-N-4-PAGE"/>
					<param name="R70I-N-ROW"/>	
					<param name="R70I-ORDERBY"/>		
				</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB70-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB70</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>