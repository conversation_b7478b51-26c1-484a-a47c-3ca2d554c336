/*
 * Created on May 5, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfimpl.pm;

import it.usi.xframe.gwc.bfutil.vo.BankData;
import it.usi.xframe.gwc.bfutil.vo.BanksList;
import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;

import java.util.ArrayList;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
/**
 * 
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */

public class BankPersistenceManager extends PersistenceManager {
	
	private static BankPersistenceManager instance = null;
	
	/**	Logger for debugging */
	private Log logger = LogFactory.getLog(this.getClass());
			
	/**
	* @return singleton
	*/
	public static synchronized BankPersistenceManager getInstance() {
		if (instance == null)
			instance = new BankPersistenceManager();
			
		return instance;
	}
	
	protected DataValue load(Map record) {
		BankData res = new BankData();
		res.setBankCode(	((String) record.get("TG_COD_IST")).trim());
		res.setTowerCode(	(record.get("TG_COD_PER") == null) ? null : ((String) record.get("TG_COD_PER")).trim());
		res.setDescription(	(record.get("TG_NOM_SOC") == null) ? null : ((String) record.get("TG_NOM_SOC")).trim());
		return res;
	}

	public BanksList retrieveBanksList() throws XFRException	{
		logger.info("retrieveBanksList() - START");
		BanksList bc = new BanksList();
		
		String sqlStr = "SELECT TG_COD_IST, TG_NOM_SOC, TG_COD_PER FROM DB2C.TGTB0135 ORDER BY TG_COD_IST";			
		logger.info("\n QUERY: " + sqlStr);
		setSqlList(sqlStr);			

		PersistenceManager.SqlParam[] params = new PersistenceManager.SqlParam[0];
		logger.debug("\n--- EXECUTE QUERY ---");
		ArrayList dvl = (ArrayList) executeList(params);
				
		bc.setBanks(dvl);
		logger.info("retrieveBanksList() - END");
		return bc;
	}

	private BankPersistenceManager() {
	}
}