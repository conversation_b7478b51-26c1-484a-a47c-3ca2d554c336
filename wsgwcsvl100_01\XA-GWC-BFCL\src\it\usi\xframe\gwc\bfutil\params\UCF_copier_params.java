/*
 * Created on Dec 20, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.params;

import java.io.Serializable;
import java.util.ArrayList;


/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class UCF_copier_params implements Serializable {

	private String db2_owner = "BA3FI";
	private String oracle_owner = "APPA3FNPF";
	private String mailto = "<EMAIL>";
	
	private ArrayList tables = new ArrayList();
	
	public UCF_copier_params()
	{		
		tables.add("GDET_CTBLSIST"); 
		tables.add("GDET_CTBMERCA");
		tables.add("GDET_CTBPARAM"); 
		tables.add("GDET_CTBTCPTZ"); 
		tables.add("GDET_CTBTICON"); 
		tables.add("GDET_CTBVALOP"); 
		tables.add("GDET_PARMSECO");
		tables.add("FFGT_LISIPARA");
		tables.add("FFGT_VALPARLS");
	}

	/**
	 * @return
	 */
	public ArrayList getTables() {
		return tables;
	}

	/**
	 * @param list
	 */
	public void setTables(ArrayList list) {
		tables = list;
	}

	/**
	 * @return
	 */
	public String getDb2_owner() {
		return db2_owner;
	}

	/**
	 * @param string
	 */
	public void setDb2_owner(String string) {
		db2_owner = string;
	}

	/**
	 * @return
	 */
	public String getOracle_owner() {
		return oracle_owner;
	}

	/**
	 * @param string
	 */
	public void setOracle_owner(String string) {
		oracle_owner = string;
	}

	/**
	 * @return
	 */
	public String getMailto() {
		return mailto;
	}

	/**
	 * @param string
	 */
	public void setMailto(String string) {
		mailto = string;
	}

}
