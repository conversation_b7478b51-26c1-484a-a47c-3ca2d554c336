
package it.usi.xframe.gwc.bfutil.params;

import java.io.Serializable;


public class MyParams implements Serializable {
		
	public String x01;
	public String x02;
	public String x03;
	public String x04;
	public String x05;
	public String x06;
	public String x07;
	public String x08;
	public String x97;
	public String x98;
	/**
	 * @return
	 */
	public String getX01() {
		return x01;
	}

	/**
	 * @return
	 */
	public String getX02() {
		return x02;
	}

	/**
	 * @return
	 */
	public String getX03() {
		return x03;
	}

	/**
	 * @return
	 */
	public String getX04() {
		return x04;
	}

	/**
	 * @return
	 */
	public String getX97() {
		return x97;
	}

	/**
	 * @return
	 */
	public String getX98() {
		return x98;
	}

	/**
	 * @param string
	 */
	public void setX01(String string) {
		x01 = string;
	}

	/**
	 * @param string
	 */
	public void setX02(String string) {
		x02 = string;
	}

	/**
	 * @param string
	 */
	public void setX03(String string) {
		x03 = string;
	}

	/**
	 * @param string
	 */
	public void setX04(String string) {
		x04 = string;
	}

	/**
	 * @param string
	 */
	public void setX97(String string) {
		x97 = string;
	}

	/**
	 * @param string
	 */
	public void setX98(String string) {
		x98 = string;
	}

	/**
	 * @return
	 */
	public String getX05() {
		return x05;
	}

	/**
	 * @return
	 */
	public String getX06() {
		return x06;
	}

	/**
	 * @return
	 */
	public String getX07() {
		return x07;
	}

	/**
	 * @return
	 */
	public String getX08() {
		return x08;
	}

	/**
	 * @param string
	 */
	public void setX05(String string) {
		x05 = string;
	}

	/**
	 * @param string
	 */
	public void setX06(String string) {
		x06 = string;
	}

	/**
	 * @param string
	 */
	public void setX07(String string) {
		x07 = string;
	}

	/**
	 * @param string
	 */
	public void setX08(String string) {
		x08 = string;
	}

}

