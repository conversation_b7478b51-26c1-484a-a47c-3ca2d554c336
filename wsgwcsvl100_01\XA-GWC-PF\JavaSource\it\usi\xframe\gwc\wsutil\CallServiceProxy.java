package it.usi.xframe.gwc.wsutil;

public class CallServiceProxy implements it.usi.xframe.gwc.wsutil.CallService {
  private boolean _useJNDI = true;
  private String _endpoint = null;
  private it.usi.xframe.gwc.wsutil.CallService callService = null;
  
  public CallServiceProxy() {
    _initCallServiceProxy();
  }
  
  private void _initCallServiceProxy() {
  
  if (_useJNDI) {
    try{
      javax.naming.InitialContext ctx = new javax.naming.InitialContext();
      callService = ((it.usi.xframe.gwc.wsutil.CallServiceService)ctx.lookup("java:comp/env/service/CallServiceService")).getCallService();
      }
    catch (javax.naming.NamingException namingException) {}
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  if (callService == null) {
    try{
      callService = (new it.usi.xframe.gwc.wsutil.CallServiceServiceLocator()).getCallService();
      }
    catch (javax.xml.rpc.ServiceException serviceException) {}
  }
  if (callService != null) {
    if (_endpoint != null)
      ((javax.xml.rpc.Stub)callService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
    else{
		_endpoint = (String)((javax.xml.rpc.Stub)callService)._getProperty("javax.xml.rpc.service.endpoint.address");
    }
  }
  
  
}


public void useJNDI(boolean useJNDI) {
  _useJNDI = useJNDI;
  callService = null;
  
}

public String getEndpoint() {
  return _endpoint;
}

public void setEndpoint(String endpoint) {
  _endpoint = endpoint;
  if (callService != null)
    ((javax.xml.rpc.Stub)callService)._setProperty("javax.xml.rpc.service.endpoint.address", _endpoint);
  
}

public it.usi.xframe.gwc.wsutil.CallService getCallService() {
  if (callService == null)
    _initCallServiceProxy();
  return callService;
}

public java.lang.String callServicePWD(java.lang.String service, java.lang.String params, java.lang.String sep1, java.lang.String sep2, String userid, String pwd) throws java.rmi.RemoteException{
  if (callService == null)
    _initCallServiceProxy();
    
  ((javax.xml.rpc.Stub)callService)._setProperty("userid", userid);
  ((javax.xml.rpc.Stub)callService)._setProperty("pwd", pwd);
  
  return this.callService(service, params, sep1, sep2);
}

public java.lang.String callService(java.lang.String service, java.lang.String params, java.lang.String sep1, java.lang.String sep2) throws java.rmi.RemoteException{
   
  return callService.callService(service, params, sep1, sep2);
}

}