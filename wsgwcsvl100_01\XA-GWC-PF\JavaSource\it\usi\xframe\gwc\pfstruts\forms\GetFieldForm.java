package it.usi.xframe.gwc.pfstruts.forms;

import javax.servlet.http.HttpServletRequest;

import org.apache.struts.action.ActionErrors;
import org.apache.struts.action.ActionMapping;
import org.apache.struts.validator.ValidatorForm;


public class GetFieldForm extends ValidatorForm {
	
	private String xmlOutput;
	private String tagFrom;
	private String tag;
	private String record;
	
	public void reset(ActionMapping mapping, HttpServletRequest request) {
		xmlOutput = "";
		tagFrom = "";
		tag = "" ;
		record = "0";
	}

	public ActionErrors validate(ActionMapping mapping, HttpServletRequest request) {
		return null;
	}

	/**
	 * @return
	 */
	public String getRecord() {
		return record;
	}
	public int getRecordAsInt() {
		Integer val = new Integer(record);
		return val.intValue();
	}

	/**
	 * @return
	 */
	public String getTag() {
		return tag;
	}

	/**
	 * @return
	 */
	public String getTagFrom() {
		return tagFrom;
	}

	/**
	 * @return
	 */
	public String getXmlOutput() {
		return xmlOutput;
	}

	/**
	 * @param string
	 */
	public void setRecord(String string) {
		record = string;
	}

	/**
	 * @param string
	 */
	public void setTag(String string) {
		tag = string;
	}

	/**
	 * @param string
	 */
	public void setTagFrom(String string) {
		tagFrom = string;
	}

	/**
	 * @param string
	 */
	public void setXmlOutput(String string) {
		xmlOutput = string;
	}

}
