<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="23/03/2004 13.08.20" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="X01" />
                    <param name="X02" />
                    <param name="X03" />
                    <param name="X04" />
                    <param name="X05" />
                    <param name="X06" />
                    <param name="X07" />
                    <param name="X09" />
                    <param name="X97" />
                    <param name="X98" />
                </input>
		    <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NRussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="XP12X012" >
                    </hostService>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>XP00</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>XP00</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>