<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>
<%@page contentType="text/html;charset=UTF-8"%><HTML>

<HEAD>
<TITLE>Methods</TITLE>
</HEAD>
<BODY>
<H1>Methods</H1>
<UL>
<LI>
<c:choose>
	<c:when test="${sessionScope.testMode eq 'PASSWORD'}">
		<A HREF="Input.jsp?method=2" TARGET="inputs"> useJNDI(boolean)</A>
	</c:when>
	<c:when test="${sessionScope.testMode eq 'TOKEN'}">
		<A HREF="Input.jsp?method=3" TARGET="inputs"> useJNDI(boolean)</A>
	</c:when>
	<c:otherwise>
		<A HREF="Input.jsp?method=4" TARGET="inputs"> useJNDI(boolean)</A>
	</c:otherwise>
</c:choose>
</LI>
<LI>
<c:choose>
	<c:when test="${sessionScope.testMode eq 'PASSWORD'}">
		<A HREF="Input.jsp?method=7" TARGET="inputs"> getEndpoint()</A>
	</c:when>
	<c:when test="${sessionScope.testMode eq 'TOKEN'}">
		<A HREF="Input.jsp?method=8" TARGET="inputs"> getEndpoint()</A>
	</c:when>
	<c:otherwise>
		<A HREF="Input.jsp?method=9" TARGET="inputs"> getEndpoint()</A>
	</c:otherwise>
</c:choose>
</LI>
<LI>
<c:choose>
	<c:when test="${sessionScope.testMode eq 'PASSWORD'}">
		<A HREF="Input.jsp?method=10" TARGET="inputs"> setEndpoint(java.lang.String)</A>
	</c:when>
	<c:when test="${sessionScope.testMode eq 'TOKEN'}">
		<A HREF="Input.jsp?method=11" TARGET="inputs"> setEndpoint(java.lang.String)</A>
	</c:when>
	<c:otherwise>
		<A HREF="Input.jsp?method=12" TARGET="inputs"> setEndpoint(java.lang.String)</A>
	</c:otherwise>
</c:choose>
</LI>
<LI>
<c:choose>
	<c:when test="${sessionScope.testMode eq 'PASSWORD'}">
		<A HREF="Input.jsp?method=15" TARGET="inputs"> getCallService()</A>
	</c:when>
	<c:when test="${sessionScope.testMode eq 'TOKEN'}">
		<A HREF="Input.jsp?method=16" TARGET="inputs"> getCallService()</A>
	</c:when>
	<c:otherwise>
		<A HREF="Input.jsp?method=17" TARGET="inputs"> getCallService()</A>
	</c:otherwise>
</c:choose>
</LI>
<LI>
<c:choose>
	<c:when test="${sessionScope.testMode eq 'PASSWORD'}">
		<A HREF="Input.jsp?method=18" TARGET="inputs"> callService(...)</A>
	</c:when>
	<c:when test="${sessionScope.testMode eq 'TOKEN'}">
		<A HREF="Input.jsp?method=20" TARGET="inputs"> callServiceToken(...)</A>
	</c:when>
	<c:otherwise>
		<A HREF="Input.jsp?method=22" TARGET="inputs"> callServiceFree(...)</A>
	</c:otherwise>
</c:choose>
</LI> 
</UL>
</BODY>
</HTML>