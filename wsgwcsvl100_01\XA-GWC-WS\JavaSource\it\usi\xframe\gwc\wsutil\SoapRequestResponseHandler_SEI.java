package it.usi.xframe.gwc.wsutil;


public interface SoapRequestResponseHandler_SEI extends java.rmi.Remote
{
  public boolean handleRequest(javax.xml.rpc.handler.MessageContext messageContext);
  public void init(javax.xml.rpc.handler.HandlerInfo handlerInfo);
  public boolean handleResponse(javax.xml.rpc.handler.MessageContext messageContext);
  public javax.xml.namespace.QName[] getHeaders();
  public boolean handleFault(javax.xml.rpc.handler.MessageContext arg0);
  public void destroy();
  public boolean handleFault(javax.xml.rpc.handler.soap.SOAPMessageContext smc);
}