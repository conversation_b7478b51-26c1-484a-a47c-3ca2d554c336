package it.usi.xframe.gwc.bfutil.base;

import it.usi.xframe.gwc.bfutil.base.IBaseConstDefinitions;
import it.usi.xframe.gwc.bfutil.base.BaseResponseClass;
import it.usi.xframe.utl.bfutil.DataValue;

import java.util.Collection;
import java.util.Iterator;
import java.util.Locale;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts.util.MessageResources;

public class BaseFunctions implements IBaseConstDefinitions {

	private Log logger = LogFactory.getLog(this.getClass());
	private static BaseFunctions obj = null;
	
	private BaseFunctions() {
	}
	
	public static BaseFunctions getInstance() {
		if (obj == null)
			obj = new BaseFunctions();
			
		return obj;
	}
	/**
	 * 
	 * @param inRequest : HttpServletRequest
	 * @param message_res : e.g. it.usi.xframe.rbg.pfstruts.resources.ApplicationResources
	 * @param descriptionKey : the label key
	 * @return
	 */
	public String getLabel(HttpServletRequest inRequest, String message_res, String descriptionKey) 
	{
		MessageResources resources = MessageResources.getMessageResources(message_res);
	
		if (resources!=null)
			return resources.getMessage(BaseFunctions.getInstance().getXframeLocale(inRequest), descriptionKey);
		else
			return "";
	}	
	
	public String getValue(Collection al,String code) {
		Iterator iter = al.iterator();
		while(iter.hasNext())
		{
			DataValue dv = (DataValue) iter.next();
			if (dv.getCode().equalsIgnoreCase(code))
				return dv.getDescription();
		}
		return ""; 
	}
	
	
	public void concatWarning(BaseResponseClass from, BaseResponseClass to)
	{
		// Copy all descr from->to
		for (int i=0;i<from.getWarning().getDescriptions().size();i++)
			to.getWarning().addDescription( (String) from.getWarning().getDescriptions().get(i) );
	}
	
	
	public Locale getXframeLocale( HttpServletRequest request )
	{
		final String COOKIE_NAME = "XFRAMELOCALE";
		final String COOKIE_SEPARATOR = "_";

		final int INDEX_OF_LANGUAGE = 0;
		final int INDEX_OF_COUNTRY = 1;
		
		String language = request.getLocale().getLanguage();
		String country = request.getLocale().getCountry();

		Cookie[] sessionCookies  = request.getCookies();
		for ( int index = 0; index < sessionCookies.length; index++ )
		{
			Cookie cookie = sessionCookies[ index ];
			if ( cookie.getName().equalsIgnoreCase( COOKIE_NAME ) )
			{
				String[] values = cookie.getValue().split( COOKIE_SEPARATOR );
				
				if ( values.length > INDEX_OF_LANGUAGE )
				{
					language = values[ INDEX_OF_LANGUAGE ];
				}
				if ( values.length > INDEX_OF_COUNTRY )
				{
					country = values[ INDEX_OF_COUNTRY ];
				}

				break;
			}
		}

		Locale locale = new Locale( language, country );
		
		return locale;
	}


}