<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="EM0I_COD_BANCA"/>
					<param name="EM0I_TORRE"/>
					<param name="EM0I_CONTROP_TIPO_0"/>
					<param name="EM0I_OCS_ID_0"/>
					<param name="EM0I_NDG_0"/>
					<param name="EM0I_COD_FISCALE_0"/>
					<param name="EM0I_CONTROP_TIPO_1"/>
					<param name="EM0I_OCS_ID_1"/>
					<param name="EM0I_NDG_1"/>
					<param name="EM0I_COD_FISCALE_1"/>
					<param name="EM0I_CONTROP_TIPO_2"/>
					<param name="EM0I_OCS_ID_2"/>
					<param name="EM0I_NDG_2"/>
					<param name="EM0I_COD_FISCALE_2"/>
					<param name="EM0I_CONTROP_TIPO_3"/>
					<param name="EM0I_OCS_ID_3"/>
					<param name="EM0I_NDG_3"/>
					<param name="EM0I_COD_FISCALE_3"/>
					<param name="EM0I_CONTROP_TIPO_4"/>
					<param name="EM0I_OCS_ID_4"/>
					<param name="EM0I_NDG_4"/>
					<param name="EM0I_COD_FISCALE_4"/>
					<param name="EM0I_CONTROP_TIPO_5"/>
					<param name="EM0I_OCS_ID_5"/>
					<param name="EM0I_NDG_5"/>
					<param name="EM0I_COD_FISCALE_5"/>
					<param name="EM0I_CONTROP_TIPO_6"/>
					<param name="EM0I_OCS_ID_6"/>
					<param name="EM0I_NDG_6"/>
					<param name="EM0I_COD_FISCALE_6"/>
					<param name="EM0I_CONTROP_TIPO_7"/>
					<param name="EM0I_OCS_ID_7"/>
					<param name="EM0I_NDG_7"/>
					<param name="EM0I_COD_FISCALE_7"/>
					<param name="EM0I_CONTROP_TIPO_8"/>
					<param name="EM0I_OCS_ID_8"/>
					<param name="EM0I_NDG_8"/>
					<param name="EM0I_COD_FISCALE_8"/>
					<param name="EM0I_CONTROP_TIPO_9"/>
					<param name="EM0I_OCS_ID_9"/>
					<param name="EM0I_NDG_9"/>
					<param name="EM0I_COD_FISCALE_9"/>		
                    <param name="XF_GAUSS_ID"/>
				</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>EMP0-DATICC-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>WEMP</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
