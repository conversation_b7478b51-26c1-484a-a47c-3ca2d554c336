/**
 * CallServiceFreeServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * m1116.12 v5211201433
 */

package it.usi.xframe.gwc.wsutil;

public class CallServiceFreeServiceLocator extends com.ibm.ws.webservices.multiprotocol.AgnosticService implements com.ibm.ws.webservices.multiprotocol.GeneratedService, it.usi.xframe.gwc.wsutil.CallServiceFreeService {

    public CallServiceFreeServiceLocator() {
        super(com.ibm.ws.webservices.engine.utils.QNameTable.createQName(
           "http://wsutil.gwc.xframe.usi.it",
           "CallServiceFreeService"));

        context.setLocatorName("it.usi.xframe.gwc.wsutil.CallServiceFreeServiceLocator");
    }

    public CallServiceFreeServiceLocator(com.ibm.ws.webservices.multiprotocol.ServiceContext ctx) {
        super(ctx);
        context.setLocatorName("it.usi.xframe.gwc.wsutil.CallServiceFreeServiceLocator");
    }

    // Use to get a proxy class for callServiceFree
    private final java.lang.String callServiceFree_address = "http://localhost:9080/XA-GWC-WS/services/CallServiceFree";

    public java.lang.String getCallServiceFreeAddress() {
        if (context.getOverriddingEndpointURIs() == null) {
            return callServiceFree_address;
        }
        String overriddingEndpoint = (String) context.getOverriddingEndpointURIs().get("CallServiceFree");
        if (overriddingEndpoint != null) {
            return overriddingEndpoint;
        }
        else {
            return callServiceFree_address;
        }
    }

    private java.lang.String callServiceFreePortName = "CallServiceFree";

    // The WSDD port name defaults to the port name.
    private java.lang.String callServiceFreeWSDDPortName = "CallServiceFree";

    public java.lang.String getCallServiceFreeWSDDPortName() {
        return callServiceFreeWSDDPortName;
    }

    public void setCallServiceFreeWSDDPortName(java.lang.String name) {
        callServiceFreeWSDDPortName = name;
    }

    public it.usi.xframe.gwc.wsutil.CallServiceFree getCallServiceFree() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(getCallServiceFreeAddress());
        }
        catch (java.net.MalformedURLException e) {
            return null; // unlikely as URL was validated in WSDL2Java
        }
        return getCallServiceFree(endpoint);
    }

    public it.usi.xframe.gwc.wsutil.CallServiceFree getCallServiceFree(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        it.usi.xframe.gwc.wsutil.CallServiceFree _stub =
            (it.usi.xframe.gwc.wsutil.CallServiceFree) getStub(
                callServiceFreePortName,
                (String) getPort2NamespaceMap().get(callServiceFreePortName),
                it.usi.xframe.gwc.wsutil.CallServiceFree.class,
                "it.usi.xframe.gwc.wsutil.CallServiceFreeSoapBindingStub",
                portAddress.toString());
        if (_stub instanceof com.ibm.ws.webservices.engine.client.Stub) {
            ((com.ibm.ws.webservices.engine.client.Stub) _stub).setPortName(callServiceFreeWSDDPortName);
        }
        return _stub;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (it.usi.xframe.gwc.wsutil.CallServiceFree.class.isAssignableFrom(serviceEndpointInterface)) {
                return getCallServiceFree();
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("WSWS3273E: Error: There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        String inputPortName = portName.getLocalPart();
        if ("CallServiceFree".equals(inputPortName)) {
            return getCallServiceFree();
        }
        else  {
            throw new javax.xml.rpc.ServiceException();
        }
    }

    public void setPortNamePrefix(java.lang.String prefix) {
        callServiceFreeWSDDPortName = prefix + "/" + callServiceFreePortName;
    }

    public javax.xml.namespace.QName getServiceName() {
        return com.ibm.ws.webservices.engine.utils.QNameTable.createQName("http://wsutil.gwc.xframe.usi.it", "CallServiceFreeService");
    }

    private java.util.Map port2NamespaceMap = null;

    protected synchronized java.util.Map getPort2NamespaceMap() {
        if (port2NamespaceMap == null) {
            port2NamespaceMap = new java.util.HashMap();
            port2NamespaceMap.put(
               "CallServiceFree",
               "http://schemas.xmlsoap.org/wsdl/soap/");
        }
        return port2NamespaceMap;
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            String serviceNamespace = getServiceName().getNamespaceURI();
            for (java.util.Iterator i = getPort2NamespaceMap().keySet().iterator(); i.hasNext(); ) {
                ports.add(
                    com.ibm.ws.webservices.engine.utils.QNameTable.createQName(
                        serviceNamespace,
                        (String) i.next()));
            }
        }
        return ports.iterator();
    }

    public javax.xml.rpc.Call[] getCalls(javax.xml.namespace.QName portName) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            throw new javax.xml.rpc.ServiceException("WSWS3062E: Error: portName should not be null.");
        }
        if  (portName.getLocalPart().equals("CallServiceFree")) {
            return new javax.xml.rpc.Call[] {
                createCall(portName, "callServiceFree", "callServiceFreeRequest"),
            };
        }
        else {
            throw new javax.xml.rpc.ServiceException("WSWS3062E: Error: portName should not be null.");
        }
    }
}
