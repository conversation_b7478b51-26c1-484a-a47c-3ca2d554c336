<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema">

	<!--
		The type definition of parameter
	-->
	<xsd:complexType name="param-type">
		<xsd:attribute name="value" type="xsd:string" use="required" />
	</xsd:complexType>

	
		<!-- The type of hyperlink -->
	<xsd:simpleType name="hyperlink-type">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="javascript"/>
			<xsd:enumeration value="url"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!--
		The type definition of a link
	-->
	<xsd:complexType name="link-type">
		<xsd:sequence>
			<xsd:element name="param" type="param-type" minOccurs="0" maxOccurs="unbounded" />
		</xsd:sequence>

		<!-- The url of the link -->
		<xsd:attribute name="action" type="xsd:string" use="required" />

		<!-- Test to activate link-->
		<xsd:attribute name="test" type="xsd:string" use="optional" />

		<!-- The class of style sheet applied to the link -->
		<xsd:attribute name="styleClass" type="xsd:string" use="optional" />

		<!-- The key of the hyperlink for parsing -->
		<xsd:attribute name="type" type="hyperlink-type" use="optional" default="url"/>
	</xsd:complexType>
	


	<!-- The type og group functions -->
	<xsd:simpleType name="function-type">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="sum"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!--
		The type definition of a group function
	-->
	<xsd:complexType name="group-function-type">

		<!-- The group function applied to the field -->
		<xsd:attribute name="function" use="required" type="function-type">
			</xsd:attribute>

		<!-- The stylesheet class of group function result (usually the sub total value) -->
		<xsd:attribute name="styleClass" type="xsd:string" use="optional" />
	</xsd:complexType>


	<!-- The type of translation -->
	<xsd:simpleType name="translation-type">
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="transparent"/>
			<xsd:enumeration value="translated"/>
		</xsd:restriction>
	</xsd:simpleType>

	<!--
		The type definition of field
	-->
	<xsd:complexType name="title-type">
		<xsd:sequence maxOccurs="1" minOccurs="0">
			<xsd:element name="link" type="link-type" minOccurs="0" maxOccurs="1"></xsd:element>
		</xsd:sequence>

		<!-- The unique identifier -->
		<xsd:attribute name="id" type="xsd:ID" use="optional"/>
		
		<!-- The key of the format string in the localized bundle -->
		<xsd:attribute name="value" type="xsd:string" use="required" />

		<!-- The key of the format string in the localized bundle -->
		<xsd:attribute name="type" type="translation-type" use="optional" default="transparent"/>

		<!-- The stylesheet class of the value -->
		<xsd:attribute name="styleClass" type="xsd:string" use="optional" />

		<!-- The key of the format string in the localized bundle -->
		<xsd:attribute name="format" type="xsd:string" use="optional" />

		<!-- The format class to use -->
		<xsd:attribute name="formatClass" type="xsd:string" use="optional" />

		<!-- The format Method of the formatClass to use -->
		<xsd:attribute name="formatMethod" type="xsd:string" use="optional" />

	</xsd:complexType>

	<!--
		The type definition of field
	-->
	<xsd:complexType name="field-type">
		<xsd:sequence maxOccurs="1" minOccurs="0">
			<xsd:element name="title" type="title-type" minOccurs="0" maxOccurs="1"></xsd:element>
			<xsd:element name="link" type="link-type" minOccurs="0" maxOccurs="1" />
			<xsd:element name="group-function" type="group-function-type" minOccurs="0" maxOccurs="1" />
		</xsd:sequence>

		<xsd:attribute name="id" type="xsd:ID" use="required"/>

		<!-- The stylesheet class of the value -->
		<xsd:attribute name="styleClass" type="xsd:string" use="optional" />

		<!-- The key of the format string in the localized bundle -->
		<xsd:attribute name="format" type="xsd:string" use="optional" />

		<!-- The key of the format string in the localized bundle -->
		<xsd:attribute name="value" type="xsd:string" use="required" />

		<!-- Test to activate altStyleClass-->
		<xsd:attribute name="test" type="xsd:string" use="optional" />
		
		<!-- The alternative stylesheet class of the value -->
		<xsd:attribute name="altStyleClass" type="xsd:string" use="optional" />

		<!-- The format class to use -->
		<xsd:attribute name="formatClass" type="xsd:string" use="optional" />

		<!-- The format Method of the formatClass to use -->
		<xsd:attribute name="formatMethod" type="xsd:string" use="optional" />

	</xsd:complexType>

	<!--
		The definition of a fields group
	-->
	<xsd:complexType name="fields-group-type">
		<xsd:sequence>
			<xsd:element name="title" type="title-type" minOccurs="0" maxOccurs="1"></xsd:element>
			<xsd:element name="field" type="field-type" minOccurs="1" maxOccurs="unbounded" />
		</xsd:sequence>
	</xsd:complexType>


	<!--
		The type definition of sub table group
	-->
	<xsd:complexType name="group-by-type">
		<xsd:sequence>
			<xsd:element name="header" type="title-type" minOccurs="0" maxOccurs="1"></xsd:element>
			<xsd:element name="footer" type="title-type" minOccurs="0" maxOccurs="1"></xsd:element>
		</xsd:sequence>
	</xsd:complexType>

	<!--
		The type definition of a table
	-->
	<xsd:complexType name="table-type">
		<xsd:sequence>
			<xsd:element name="title" type="title-type" minOccurs="0" maxOccurs="1"></xsd:element>
			<xsd:element name="group-by" type="group-by-type" minOccurs="0" maxOccurs="1"/>
			<xsd:element name="fields-group" type="fields-group-type" minOccurs="1" maxOccurs="unbounded" />
		</xsd:sequence>

		<!-- The unique identifier of the table -->
		<xsd:attribute name="id" type="xsd:ID" use="required"/>

		<!-- The class of style sheet applied to the table -->
		<xsd:attribute name="styleClass" type="xsd:string" use="optional" />

		<!-- The class of style sheet applied to the last row of the table -->
		<xsd:attribute name="footerStyleClass" type="xsd:string" use="optional" />

		<!-- The stylesheet class of the value for even rows-->
		<xsd:attribute name="evenStyleClass" type="xsd:string" use="optional" />

		<!-- The stylesheet class of the value for odd rows-->
		<xsd:attribute name="oddStyleClass" type="xsd:string" use="optional" />

		<!-- The object container of the value -->
		<xsd:attribute name="dataContainer" type="xsd:string" use="required" />
	</xsd:complexType>

	<!--
		The type definition of the table definitions
	-->
	<xsd:complexType name="table-definitions-type">
		<xsd:sequence>
			<xsd:element name="table" type="table-type" minOccurs="0" maxOccurs="unbounded" />
		</xsd:sequence>
	</xsd:complexType>

	<!--
		The root of the document
	-->
	<xsd:element name="table-definitions" type="table-definitions-type" />

</xsd:schema>
