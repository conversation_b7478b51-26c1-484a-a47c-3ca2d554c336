<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprBigliettoPGA">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
      <element name="x06" nillable="true" type="xsd:string"/>
      <element name="x07" nillable="true" type="xsd:string"/>
      <element name="x08" nillable="true" type="xsd:string"/>
      <element name="x09" nillable="true" type="xsd:string"/>
      <element name="x11" nillable="true" type="xsd:string"/>
      <element name="x12" nillable="true" type="xsd:string"/>
      <element name="x97" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprBigliettoPGAResponse">
    <complexType>
     <sequence>
      <element name="nprBigliettoPGAReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprBigliettoPGAResponse">

      <wsdl:part element="impl:nprBigliettoPGAResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprBigliettoPGARequest">

      <wsdl:part element="impl:nprBigliettoPGA" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_BigliettoPGA_SEI">

      <wsdl:operation name="nprBigliettoPGA">

         <wsdl:input message="impl:nprBigliettoPGARequest" name="nprBigliettoPGARequest"/>

         <wsdl:output message="impl:nprBigliettoPGAResponse" name="nprBigliettoPGAResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_BigliettoPGASoapBinding" type="impl:NPR_BigliettoPGA_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprBigliettoPGA">

         <wsdlsoap:operation soapAction="nprBigliettoPGA"/>

         <wsdl:input name="nprBigliettoPGARequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprBigliettoPGAResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_BigliettoPGAService">

      <wsdl:port binding="impl:NPR_BigliettoPGASoapBinding" name="NPR_BigliettoPGA">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_BigliettoPGA"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
