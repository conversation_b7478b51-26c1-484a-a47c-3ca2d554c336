<?xml version="1.0" encoding="UTF-8"?>
<!-- USI web xml descriptor -->
<!DOCTYPE web-app PUBLIC "-//Sun Microsystems, Inc.//DTD Web Application 2.3//EN" "http://java.sun.com/dtd/web-app_2_3.dtd">
<web-app id="WebApp">
	<display-name>XA-GWC-PF</display-name>
	<context-param>
		<param-name>javax.servlet.jsp.jstl.fmt.localizationContext</param-name>
		<param-value>it.usi.xframe.gwc.pfstruts.resources.ApplicationResources</param-value>
	</context-param>
	<servlet>
		<servlet-name>LoggerInitializeServlet</servlet-name>
		<display-name>LoggerInitializeServlet</display-name>
		<servlet-class>it.usi.xframe.system.pfutil.LoggerInitializeServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet>
		<servlet-name>action</servlet-name>
		<servlet-class>it.usi.xframe.system.pfutil.ActionDecoratorServlet</servlet-class>
		<init-param>
			<param-name>noSessionForward</param-name>
			<param-value>/sessionExpired.do</param-value>
		</init-param>
		<init-param>
			<param-name>noSessionIgnorePatterns</param-name>
			<param-value>/public/,/index.do,/startBios.do,/startBiosV2.do,/startPgeTest.do,/startAfaTest.do,/testMsg.do,/gauss_form.do,/testPwd.do,/testCallService.do,/testCallServiceToken.do,/testCallServiceFree.do</param-value>
		</init-param>
		<init-param>
			<param-name>config</param-name>
			<param-value>/WEB-INF/struts-config.xml</param-value>
		</init-param>
		<init-param>
			<param-name>debug</param-name>
			<param-value>2</param-value>
		</init-param>
		<init-param>
			<param-name>detail</param-name>
			<param-value>2</param-value>
		</init-param>
		<init-param>
			<param-name>validate</param-name>
			<param-value>true</param-value>
		</init-param>
		<load-on-startup>2</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>action</servlet-name>
		<url-pattern>*.do</url-pattern>
	</servlet-mapping>
	<welcome-file-list>
		<welcome-file>index.html</welcome-file>
		<welcome-file>index.htm</welcome-file>
		<welcome-file>index.jsp</welcome-file>
		<welcome-file>default.html</welcome-file>
		<welcome-file>default.htm</welcome-file>
		<welcome-file>default.jsp</welcome-file>
	</welcome-file-list>
	<taglib>
		<taglib-uri>http://java.sun.com/jstl/core</taglib-uri>
		<taglib-location>/WEB-INF/c.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://java.sun.com/jstl/core_rt</taglib-uri>
		<taglib-location>/WEB-INF/c-rt.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://java.sun.com/jstl/fmt</taglib-uri>
		<taglib-location>/WEB-INF/fmt.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://java.sun.com/jstl/fmt_rt</taglib-uri>
		<taglib-location>/WEB-INF/fmt-rt.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://java.sun.com/jstl/xml</taglib-uri>
		<taglib-location>/WEB-INF/x.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://java.sun.com/jstl/xml_rt</taglib-uri>
		<taglib-location>/WEB-INF/x-rt.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://jakarta.apache.org/struts/tags-bean</taglib-uri>
		<taglib-location>/WEB-INF/struts-bean.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://jakarta.apache.org/struts/tags-html</taglib-uri>
		<taglib-location>/WEB-INF/struts-html.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://jakarta.apache.org/struts/tags-logic</taglib-uri>
		<taglib-location>/WEB-INF/struts-logic.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://jakarta.apache.org/struts/tags-nested</taglib-uri>
		<taglib-location>/WEB-INF/struts-nested.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://jakarta.apache.org/struts/tags-template</taglib-uri>
		<taglib-location>/WEB-INF/struts-template.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://jakarta.apache.org/struts/tags-tiles</taglib-uri>
		<taglib-location>/WEB-INF/struts-tiles.tld</taglib-location>
	</taglib>
	<taglib>
		<taglib-uri>http://jakarta.apache.org/taglibs/string-1.0.1</taglib-uri>
		<taglib-location>/WEB-INF/taglibs-string.tld</taglib-location>
	</taglib>
	<resource-ref id="ResourceRef_1089887274831">
		<res-ref-name>url/log4j</res-ref-name>
		<res-type>java.net.URL</res-type>
		<res-auth>Container</res-auth>
		<res-sharing-scope>Shareable</res-sharing-scope>
	</resource-ref>
	<resource-ref id="ResourceRef_1138100352955">
		<res-ref-name>jdbc/defaultds</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
		<res-sharing-scope>Shareable</res-sharing-scope>
	</resource-ref>
	<security-constraint>
		<display-name>Private pages</display-name>
		<web-resource-collection>
			<web-resource-name>root</web-resource-name>
			<description></description>
			<url-pattern>/*</url-pattern>
			<url-pattern>/</url-pattern>
			<http-method>
			GET</http-method>
			<http-method>
			PUT</http-method>
			<http-method>
			HEAD</http-method>
			<http-method>
			TRACE</http-method>
			<http-method>
			POST</http-method>
			<http-method>
			DELETE</http-method>
			<http-method>
			OPTIONS</http-method>
		</web-resource-collection>
		<auth-constraint>
			<description></description>
			<role-name>Authenticated</role-name>
		</auth-constraint>
	</security-constraint>
	<security-constraint>
		<display-name>Public pages</display-name>
		<web-resource-collection>
			<web-resource-name>public folder</web-resource-name>
			<description></description>
			<url-pattern>
			/public/*</url-pattern>
			<http-method>
			GET</http-method>
			<http-method>
			PUT</http-method>
			<http-method>
			HEAD</http-method>
			<http-method>
			TRACE</http-method>
			<http-method>
			POST</http-method>
			<http-method>
			DELETE</http-method>
			<http-method>
			OPTIONS</http-method>
		</web-resource-collection>
		<web-resource-collection>
			<web-resource-name>utl_public folder</web-resource-name>
			<description></description>
			<url-pattern>
			/../../XA-UTL-PF/public/*</url-pattern>
			<http-method>
			GET</http-method>
			<http-method>
			PUT</http-method>
			<http-method>
			HEAD</http-method>
			<http-method>
			TRACE</http-method>
			<http-method>
			POST</http-method>
			<http-method>
			DELETE</http-method>
			<http-method>
			OPTIONS</http-method>
		</web-resource-collection>
		<auth-constraint>
			<description></description>
			<role-name>Guest</role-name>
		</auth-constraint>
	</security-constraint>
	<login-config>
		<auth-method>FORM</auth-method>
		<form-login-config>
			<form-login-page>/../../XA-UTL-PF/public/login.jsp</form-login-page>
			<form-error-page>/../../XA-UTL-PF/public/error.jsp</form-error-page>
		</form-login-config>
	</login-config>
	<security-role>
		<description></description>
		<role-name>Authenticated</role-name>
	</security-role>
	<security-role>
		<description></description>
		<role-name>Guest</role-name>
	</security-role>
	<ejb-ref id="EjbRef_1138100195581">
		<ejb-ref-name>ejb/IfgServiceFactory</ejb-ref-name>
		<ejb-ref-type>Session</ejb-ref-type>
		<home>it.usi.xframe.ifg.bfintf.IfgHome</home>
		<remote>it.usi.xframe.ifg.bfintf.Ifg</remote>
	</ejb-ref>
	<ejb-ref id="EjbRef_1138100195582">
		<ejb-ref-name>ejb/UTLServiceFactory</ejb-ref-name>
		<ejb-ref-type>Session</ejb-ref-type>
		<home>it.usi.xframe.utl.bfintf.UTLHome</home>
		<remote>it.usi.xframe.utl.bfintf.UTL</remote>
	</ejb-ref>
	<ejb-ref id="EjbRef_1189435943005">
		<ejb-ref-name>ejb/PreServiceFactory</ejb-ref-name>
		<ejb-ref-type>Session</ejb-ref-type>
		<home>it.usi.xframe.pre.bfintf.PreHome</home>
		<remote>it.usi.xframe.pre.bfintf.Pre</remote>
	</ejb-ref>
	<ejb-ref id="EjbRef_1196244638406">
		<ejb-ref-name>ejb/GwcMain</ejb-ref-name>
		<ejb-ref-type>Session</ejb-ref-type>
		<home>it.usi.xframe.gwc.bfintf.GwcMainHome</home>
		<remote>it.usi.xframe.gwc.bfintf.GwcMain</remote>
	</ejb-ref>
</web-app>
	
