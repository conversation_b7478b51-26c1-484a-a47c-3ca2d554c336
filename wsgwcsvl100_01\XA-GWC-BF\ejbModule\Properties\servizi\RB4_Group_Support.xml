<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R56I-PROPOSAL-ID"/>
					<param name="R56I-FUNCTION"/>
					<param name="R56I-USER-ID"/>
					<param name="R56I-QUESTION-LIST"/>
					<param name="R56I-DTQ-INCOHERENCE-1L15"/>
					<param name="R56I-DTQ-INCOHERENCE-1L16"/>
					<param name="R56I-DTQ-INCOHERENCE-G1G2B"/>
					<param name="R56I-DTQ-INCOHERENCE-G1G3"/>					
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB56-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB56</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
