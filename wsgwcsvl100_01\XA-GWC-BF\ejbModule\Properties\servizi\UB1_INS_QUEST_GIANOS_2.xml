<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 
	9.33.19" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
	security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="UB1C017I_COD_INTERNO" />
					<param name="UB1C017I_TIPO_SERVIZIO" />
					<param name="UB1C017I_COD_PROCEDURA" />
					<param name="UB1C017I_TIP_CLI" />
					<param name="UB1C017I_NDG" />
					<param name="UB1C017I_COD_TAE" />
					<param name="UB1C017I_PAE_ATTIVITA_TIT" />
					<param name="UB1C017I_PV_ATTIVITA_TIT" />
					<param name="UB1C017I_PRE_ATT" />
					<param name="UB1C017I_PEP" />
					<param name="UB1C017I_CLI_TER" />
					<param name="UB1C017I_IND" />
					<param name="UB1C017I_CLI_RUOLO_SENS" />
					<param name="UB1C017I_CLI_COMP_DIS" />
					<param name="UB1C017I_ESE_TIPO" />
					<param name="UB1C017I_ESE_NDG" />
					<param name="UB1C017I_ESE_COD_TAE" />
					<param name="UB1C017I_ESE_PAE_ATT" />
					<param name="UB1C017I_ESE_PV_ATT" />
					<param name="UB1C017I_ESE_PRE_ATT" />
					<param name="UB1C017I_ESE_PEP" />
					<param name="UB1C017I_ESE_TER" />
					<param name="UB1C017I_ESE_IND" />
					<param name="UB1C017I_DIPENDENZA" />
					<param name="UB1C017I_TIPO_QUEST" />
					<param name="UB1C017I_TIPO_APERTURA" />
					<param name="UB1C017I_SCOPO_APE" />
					<param name="UB1C017I_ALTRE_INFO" />
					<param name="UB1C017I_CONT_REALE" />
					<param name="UB1C017I_IMP_OPE" />
					<param name="UB1C017I_PROV_DENARO" />
					<param name="UB1C017I_PAE_DEST" />
					<param name="UB1C017I_PV_DEST" />
					<param name="UB1C017I_MATR_OPE" />
					<param name="XF_GAUSS_ID" />
				</input>
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input />
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</bridge>
		<params>
			<hostService>UB1_INS_QUEST_GIANOS_2</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUB1</transaction>
			<program>PC00WUB1</program>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>