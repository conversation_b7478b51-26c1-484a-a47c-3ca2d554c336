/*
 * Created on May 4, 2009
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.base;

import it.usi.xframe.gwc.bfutil.base.IBaseConstDefinitions;
import it.usi.xframe.gwc.bfutil.base.BaseWarning;
import it.usi.xframe.utl.bfutil.ResponseClass;

import java.io.Serializable;

public class BaseResponseClass extends ResponseClass implements Serializable, IBaseConstDefinitions {
	private boolean result;
	private BaseWarning warning = new BaseWarning();
	
	/**
	 * @return
	 */
	public boolean isResult() {
		return result;
	}

	/**
	 * @param b
	 */
	public void setResult(boolean b) {
		result = b;
	}

	/**
	 * @return
	 */
	public BaseWarning getWarning() {
		return warning;
	}

	/**
	 * @param warning
	 */
	public void setWarning(BaseWarning warning) {
		this.warning = warning;
	}

}
