<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
    
     Refer to the below DTD Instead:
    
    <!DOCTYPE form-validation PUBLIC
          "-//Apache Software Foundation//DTD Commons Validator Rules Configuration 1.0//EN"
          "http://jakarta.apache.org/commons/dtds/validator_1_0.dtd">


    $Id: validator-rules_1_1.dtd,v 1.1 2003/10/18 10:28:30 marco Exp $
-->


<!-- =================================================== Top Level Elements -->


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "form-validation" element is the root of the configuration file
     hierarchy, and contains nested elements for all of the other
     configuration settings.
-->
<!ELEMENT form-validation (global+)>


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
    The elements defined here are all global and must be nested within a
    "global" element.
-->
<!ELEMENT global (validator+)>


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "validator" element defines what validators objects can be used with
     the fields referenced by the formset elements. The validator element can
     accept 4 properties: name, classname, method, methodparams, msg, depends,
     and jsFunctionName.
-->
<!ELEMENT validator (javascript?)>
<!ATTLIST validator name           CDATA #REQUIRED
                    classname      CDATA #REQUIRED
                    method         CDATA #REQUIRED
                    methodParams   CDATA #REQUIRED
                    msg            CDATA #REQUIRED
                    depends        CDATA #IMPLIED
                    jsFunctionName CDATA #IMPLIED >


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "javascript" element defines a JavaScript that can be used to perform
     client-side validators.
-->
<!ELEMENT javascript (#PCDATA)>


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "constant" element defines a static value that can be used as
     replacement parameters within "field" elements. The "constant-name" and
     "constant-value" elements define the constant's reference id and replacement
     value.
-->
<!ELEMENT constant (constant-name, constant-value)>
<!ELEMENT constant-name  (#PCDATA)>
<!ELEMENT constant-value (#PCDATA)>


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
      The "formset" element defines a set of forms for a locale. Formsets for
      specific locales can override only those fields that change. The
      localization is properly scoped, so that a formset can override just the
      language, or just the country, or both.
-->
<!ELEMENT formset (constant*, form+)>
<!ATTLIST formset language     CDATA #IMPLIED
                  country      CDATA #IMPLIED >


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "form" element defines a set of fields to be validated. The name
     corresponds to the identifer the application assigns to the form. In the
     case of the Struts framework, this is the attribute property from
     the ActionMapping. Struts also offers the alternative of using the
     the path property as the Validator form name.
-->
<!ELEMENT form    (field+ )>
<!ATTLIST form    name CDATA #REQUIRED>


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "field" element defines the properties to be validated. In a
     web application, a field would also correspond to a control on
     a HTML form. To validate the properties, the validator works through
     a JavaBean representation, like a Struts ActionForm. The field element
     can accept up to 4 attributes:

     property        The property on the JavaBean corresponding to this
                     field element.

     depends         The comma-delimited list of validators to apply against
                     this field. For the field to succeed, all the
                     validators must succeed.

     page            The JavaBean corresponding to this form may include
                     a page property. Only fields with a "page" attribute
                     value that is equal to or less than the page property
                     on the form JavaBean are processed. This is useful when
                     using a "wizard" approach to completing a large form,
                     to ensure that a page is not skipped.
                     [0]

     indexedListProperty
                     The "indexedListProperty" is the method name that will
                     return an array or a Collection used to retrieve the
                     list and then loop through the list performing the
                     validations for this field.

     fieldOrder      [:FIXME: is this still supported?]
-->
<!ELEMENT field   (msg|arg0|arg1|arg2|arg3|var)*>
<!ATTLIST field   property CDATA #REQUIRED
                  depends  CDATA #IMPLIED
                  page     CDATA #IMPLIED
                  indexedListProperty CDATA #IMPLIED >


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "msg" element defines a custom message key to use when one of the
     validators for this field fails. Each validator has a default message
     property that is used when a corresonding field msg is not specified.
     Each validator applied to a field may have its own msg element.
     The msg element accepts up to three attributes.

      name        The name of the validator corresponding to this msg.

      key         The key that will return the message template from a
                  resource bundle.

      resource    If set to "false", the key is taken to be a literal
                  value rather than a bundle key.
                  [true]
-->
<!ELEMENT msg     EMPTY>
<!ATTLIST msg     name     CDATA #IMPLIED
                  key      CDATA #IMPLIED
                  resource CDATA #IMPLIED >


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "arg0" element defines the first replacement value to use with the
     message template for this validator or this field.
     The arg0 element accepts up to three attributes.

      name        The name of the validator corresponding to this msg.

      key         The key that will return the message template from a
                  resource bundle.

      resource    If set to "false", the key is taken to be a literal
                  value rather than a bundle key.
                  [true]
-->
<!ELEMENT arg0    EMPTY>
<!ATTLIST arg0    name     CDATA #IMPLIED
                  key      CDATA #IMPLIED
                  resource CDATA #IMPLIED >


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "arg1" element defines the second replacement value to use with the
     message template for this validator or this field.
     The arg1 element accepts up to three attributes.

      name        The name of the validator corresponding to this msg.

      key         The key that will return the message template from a
                  resource bundle.

      resource    If set to "false", the key is taken to be a literal
                  value rather than a bundle key.
                  [true]
-->
<!ELEMENT arg1    EMPTY>
<!ATTLIST arg1    name     CDATA #IMPLIED
                  key      CDATA #IMPLIED
                  resource CDATA #IMPLIED >


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "arg2" element defines the third replacement value to use with the
     message template for this validator or this field.
     The arg2 element accepts up to three attributes.

      name        The name of the validator corresponding to this msg.

      key         The key that will return the message template from a
                  resource bundle.

      resource    If set to "false", the key is taken to be a literal
                  value rather than a bundle key.
                  [true]
-->
<!ELEMENT arg2    EMPTY>
<!ATTLIST arg2    name     CDATA #IMPLIED
                  key      CDATA #IMPLIED
                  resource CDATA #IMPLIED >

<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "arg3" element defines the fourth replacement value to use with the
     message template for this validator or this field.
     The arg0 element accepts up to three attributes.

      name        The name of the validator corresponding to this msg.

      key         The key that will return the message template from a
                  resource bundle.

      resource    If set to "false", the key is taken to be a literal
                  value rather than a bundle key.
                  [true]
-->
<!ELEMENT arg3    EMPTY>
<!ATTLIST arg3    name     CDATA #IMPLIED
                  key      CDATA #IMPLIED
                  resource CDATA #IMPLIED >


<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The "var" element can set parameters that a field may need to pass to
     one of its validators, such as the minimum and maximum values in a
     range validation. These parameters may also be referenced by one of the
     arg? elements using a shell syntax: ${var:var-name}.
-->
<!ELEMENT var (var-name, var-value)>



<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The name of the var parameter to provide to a field's validators.
-->
<!ELEMENT var-name  (#PCDATA)>



<!--
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED
    DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED ** DEPRECATED 
     The value of the var parameter to provide to a field's validators.
-->
<!ELEMENT var-value (#PCDATA)>

<!-- eof -->
