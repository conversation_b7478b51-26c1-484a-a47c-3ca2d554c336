<?xml version="1.0" encoding="UTF-8" ?>
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="N001" />
					<param name="N002" />
					<param name="N003" />
					<param name="N004" />
                    <param name="X01" />
					<param name="N005" />
                    <param name="X02" />
                    <param name="X03" />
                    <param name="X04" />
					<param name="N008" />
					<param name="N009" />
                    <param name="X05" />
                    <param name="X06" />
                    <param name="X07" />
                    <param name="X08" />
                    <param name="X09" />
                    <param name="X10" />
                    <param name="X11" />
                    <param name="X12" />
			  <param name="N010" />
		        <param name="X13" />
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="XP21X21I" />
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
			<scheduler>XP00</scheduler>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>XP00</transaction>
			<timeout>60000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>