package it.usi.xframe.gwc.pfstruts.actions;

import javax.servlet.http.HttpServletRequest;

import org.apache.struts.action.Action;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class TestCallServiceBase extends Action {

	protected String getLocatorString(HttpServletRequest request, String actionName, String webServiceName) {

		StringBuffer url = request.getRequestURL();
		String endPoint = url.toString();
		int i = endPoint.indexOf("PF/" + actionName);
		if (i != -1) {
			endPoint = endPoint.substring(0, i);
			endPoint += "WS/services/";
			endPoint += webServiceName;
		}
		
		return endPoint;
	}	
}
