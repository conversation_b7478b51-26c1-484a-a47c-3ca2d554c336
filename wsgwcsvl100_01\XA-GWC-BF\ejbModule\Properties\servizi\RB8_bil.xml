<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R82I-PROPOSAL-ID"/>
					<param name="R82I-FUNCTION"/>
					<param name="R82I-FLAG-ENV"/>
					<param name="R82I-BIL-STATUS"/>
					<param name="R82I-USER-ID"/>
					<param name="R82I-AUTH-LEV"/>
					<param name="R82I-BALANCE-ID"/>
					<param name="R82I-CHECK-1-A"/>
					<param name="R82I-CHECK-1-M"/>
					<param name="R82I-CHECK-2-A"/>
					<param name="R82I-CHECK-2-M"/>
					<param name="R82I-CHECK-3-A"/>
					<param name="R82I-CHECK-3-M"/>
					<param name="R82I-OAP-DESCR"/>
					<param name="R82I-STRUCTURE-DESCR"/>
					<param name="R82I-AUDIT-DESCR"/>
					<param name="R82I-BANK-NAME"/>
					<param name="R82I-INSERT-USER"/>
					<param name="R82I-WARNING"/>
			    </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB82-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB82</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
