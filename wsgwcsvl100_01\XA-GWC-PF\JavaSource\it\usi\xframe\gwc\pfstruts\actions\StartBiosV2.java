package it.usi.xframe.gwc.pfstruts.actions;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts.action.Action;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

import it.usi.xframe.ifg.bfutil.IfgServiceFactory;
import it.usi.xframe.ifg.bfutil.wpro.UserData;
import it.usi.xframe.pre.bfutil.PreServiceFactory;
import it.usi.xframe.pre.bfutil.PreUserInfo;
import it.usi.xframe.system.errors.XFRSevereException;

public class StartBiosV2 extends Action {

	private Log logger = LogFactory.getLog(this.getClass());

	private static final String LOGIN_ERROR = "error.login";
	private static final String HASH_STRING_PATTERN = "ABI={0}FILIALE={1}USER={2}TIMESTAMP={3}PRIVATEKEY={4}";
	
	public ActionForward execute(ActionMapping mapping, ActionForm form, HttpServletRequest request, HttpServletResponse response) throws Exception {

		logger.info("Executing StartBiosV2 action!");

		UserData userData;
		PreUserInfo profile;
		
		try {
			userData = IfgServiceFactory.getInstance().getIfgServiceFacade().getUserInfo();
			profile = PreServiceFactory.getInstance().getPreServiceFacade().retrievePreUserInfo(userData.getCodIstituto(), userData.getCodOperatore(), "ZA");
		} catch (Exception e) {
			throw new XFRSevereException(LOGIN_ERROR, getArrayFromString(e.getMessage()));
		}

		logger.info("Banca = [" + profile.getPG_COD_IST() + "]");
		logger.info("Utente = [" + userData.getCodOperatore() + "]");
		logger.info("User First Name = [" + profile.getPG_NOME_OPER() + "]");
		logger.info("User Last  Name = [" + profile.getPG_COGNOME_OPER() + "]");
		logger.info("User Role = [" + profile.getPG_COD_AUTHOR() + "]");  
		logger.info("User Branch = [" + profile.getPG_COD_SPORT_CONT() + "]");
		
		String bankCode = "R";
		String branchCode = profile.getPG_COD_SPORT_CONT();
		String userProfile = profile.getPG_COD_AUTHOR().length() > 2 ? profile.getPG_COD_AUTHOR().substring(profile.getPG_COD_AUTHOR().length() - 2) : profile.getPG_COD_AUTHOR();
		String userId = userData.getCodOperatore();
		String nowDateStr = getDateAsString(new Date(), "yyyyMMddHHmmss");
		String privateKey = "P2rFND3XB9l2LOxJJ3NAnxa9I11jsZtJTJCzl5zAWI5hCrYNlH2Tp44SDw48o1At";
		
		request.setAttribute("bankCode", bankCode);
		request.setAttribute("branchCode", branchCode);
		request.setAttribute("profile", userProfile);
		request.setAttribute("userId", userId);
		request.setAttribute("hash", getHashForSilos(bankCode, branchCode, userId, nowDateStr, privateKey));
		request.setAttribute("timestamp", nowDateStr);
		
		return mapping.findForward("ok");
	}
	
	private String getHashForSilos(String abi, String filiale, String user, String nowDateStr, String privateKey) throws NoSuchAlgorithmException {
	    String input = MessageFormat.format(HASH_STRING_PATTERN, abi, filiale, user, nowDateStr, privateKey);
	    
	    MessageDigest md = MessageDigest.getInstance("SHA-256");

	    byte[] hash = md.digest(input.getBytes(StandardCharsets.UTF_8));

	    BigInteger number = new BigInteger(1, hash);

	    StringBuilder hexString = new StringBuilder(number.toString(16));

	    while (hexString.length() < 64) {
	      hexString.insert(0, '0');
	    }

	    return hexString.toString();
	}
	
	private String getDateAsString(Date date, String pattern) {
	    SimpleDateFormat sdf = null;
	    if (pattern != null) {
	      sdf = new SimpleDateFormat(pattern);
	    } else {
	      sdf = new SimpleDateFormat();
	    }
	    return sdf.format(date);
	  }
	
	private Object[] getArrayFromString(String s) {
		Object[] a = new Object[1];
		if (s==null) s="???";
		a[0] = s; 
		return a;
	}
}
