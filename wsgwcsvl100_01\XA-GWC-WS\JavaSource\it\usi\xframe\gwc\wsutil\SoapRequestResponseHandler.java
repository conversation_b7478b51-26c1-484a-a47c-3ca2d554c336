/*
 * Created on Feb 17, 2010
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import javax.xml.namespace.QName;
import javax.xml.rpc.handler.GenericHandler;
import javax.xml.rpc.handler.Handler;
import javax.xml.rpc.handler.HandlerInfo;
import javax.xml.rpc.handler.MessageContext;
import javax.xml.rpc.handler.soap.SOAPMessageContext;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class SoapRequestResponseHandler extends GenericHandler implements Handler {
	private static final Log log                  = LogFactory.getLog(SoapRequestResponseHandler.class);
	private HandlerInfo handlerInfo               = null;
	private static final String keyService        = "path";
	private static final String ipCaller          = "remoteaddr";
	private static final String INTERNET_SITE     = "InternetSite";

	private boolean found = false;
	
	//Default Constructor - RSA8 migration prerequisites	
	public SoapRequestResponseHandler(){
	}
	
	public void init(HandlerInfo handlerInfo){
		  this.handlerInfo = handlerInfo;
	}
	
	public boolean handleRequest(MessageContext messageContext){
     	log.info("<<<INSIDE HANDLE REQUEST>>> ");
		try{
			String operatorID = "";
			String partnerCode = "";
			SOAPMessageContext sctx = (SOAPMessageContext)messageContext;
			SOAPMessage message     = sctx.getMessage();
			SOAPPart sp             = message.getSOAPPart();
			SOAPEnvelope senv       = sp.getEnvelope();
			
			System.out.println("HEADER: " + senv.getHeader().toString());
			System.out.println("BPDY: " + senv.getBody().toString());
		}catch (Exception e){
			log.error(e, e.getCause());
		}
			 return true;
	}
	   
	public boolean handleResponse(MessageContext messageContext){
		log.info("<<<INSIDE HANDLE RESPONSE>>> ");
		long  res = 0;
		try{
			SOAPMessageContext sctx = (SOAPMessageContext)messageContext;
			SOAPMessage message     = sctx.getMessage();
			SOAPPart sp             = message.getSOAPPart();
			SOAPEnvelope senv       = sp.getEnvelope();
			
			System.out.println("HEADER: " + senv.getHeader().toString());
			System.out.println("BPDY: " + senv.getBody().toString());
				
		  }
		  catch (Exception e)
		  {
			
			log.error(e, e.getCause());
			
		  }
		return true;
   }

	public QName[] getHeaders() {
		// TODO Auto-generated method stub
		  return handlerInfo.getHeaders();
	}

	public boolean handleFault(SOAPMessageContext smc) {
		 log.error("<<<INSIDE SECOND HANDLE FAULT>>> ");
		return true;
	}
	
	
}
