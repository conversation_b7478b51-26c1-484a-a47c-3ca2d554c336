/*
* Created on Sept 8, 2008 A.C.
*
*/

package it.usi.xframe.gwc.bfutil.base;


import java.sql.SQLException;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import it.usi.xframe.gwc.bfutil.base.BaseSevereException;
import it.usi.xframe.gwc.bfutil.base.BaseResponseClass;
import it.usi.xframe.gwc.bfutil.base.BaseWarning;
import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;


public class BasePersistenceManager extends PersistenceManager implements IBaseConstDefinitions {
	
	Log logger = LogFactory.getLog(this.getClass());
//	private static final String STORED_PROC_OK="";
	private static final String STORED_PROC_KO="G";
	private static final String STORED_PROC_W2="W";
	private static final String STORED_PROC_W3="E";
//	private static final String STORED_PROC_W4="0004";
//	private static final String STORED_PROC_W5="0005";
	
	protected DataValue load(Map record) {
		return null;
	}

	protected void manageStoreProcedureError(String[] exits,BaseResponseClass rc) throws XFRException,SQLException, BaseSevereException
	{
		// default to "good" case
        rc.setResult(true);
		
		String retCode = exits[0];			
		String sqlCode = exits[1];
		String errorDesc = exits[2];
		logger.info("--- retCode = {" + retCode + "}");
		logger.info("--- sqlCode = {" + sqlCode + "}");
		logger.info("--- Error Description = {" + errorDesc + "}");
		
		if (
			retCode.equals(STORED_PROC_W2) 
//			|| retCode.equals(STORED_PROC_W4)
//			|| retCode.equals(STORED_PROC_W5) 
		) 
		{
			BaseWarning war = new BaseWarning();
			war.setCode(retCode);
			war.addDescription(errorDesc);
			rc.setWarning(war);
		}
		
		if (retCode.equals(STORED_PROC_W3)) 
		{
			rc.setResult(false);		
			BaseWarning war = new BaseWarning();
			war.setCode(retCode);
			war.addDescription(errorDesc);
			rc.setWarning(war);
		}

		if (retCode.equals(STORED_PROC_KO)) 
		{
			rc.setResult(false);
			throw new BaseSevereException("The stored procedure returned an error with code G : " + errorDesc );			
		}		
	}
}
