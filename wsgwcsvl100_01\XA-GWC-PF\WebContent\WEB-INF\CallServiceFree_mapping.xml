<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1284538691882">
      <package-mapping id="PackageMapping_1284538691882">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1284538691882">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1284538691882">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1284538691882">
         <service-interface>it.usi.xframe.gwc.wsutil.CallServiceFreeService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1284538691882">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceFreeService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1284538691882">
            <port-name>CallServiceFree</port-name>
            <java-port-name>CallServiceFree</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1284538691882">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallServiceFree</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1284538691882">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceFree</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1284538691882">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceFreeSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1284538691882">
            <java-method-name>callServiceFree</java-method-name>
            <wsdl-operation>callServiceFree</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284538691882">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284538691882">
                  <wsdl-message id="WSDLMessage_1284538691882">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>service</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284538691883">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284538691883">
                  <wsdl-message id="WSDLMessage_1284538691883">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>params</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284538691884">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284538691884">
                  <wsdl-message id="WSDLMessage_1284538691884">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep1</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1284538691885">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1284538691885">
                  <wsdl-message id="WSDLMessage_1284538691885">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep2</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1284538691882">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1284538691886">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>callServiceFreeResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>callServiceFreeReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
