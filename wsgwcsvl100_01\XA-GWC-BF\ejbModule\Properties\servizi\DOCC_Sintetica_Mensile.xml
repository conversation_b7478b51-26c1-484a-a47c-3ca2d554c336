<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="25/11/2002 16.52.28" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="X41"/>
					<param name="X42"/>
					<param name="X43"/>
					<param name="N041"/>
					<param name="N042"/>
					<param name="N043"/>
					<param name="N044"/>
					<param name="X44"/>
					<param name="X45"/>
					<param name="X46"/>
					<param name="X47"/>
					<param name="X48"/>
					<param name="X49"/>
					<param name="X50"/>
					<param name="X51"/>
					<param name="X52"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output>
					<hostService id="1" name="SZBEP0BE">
						<param select="X41" name="X01"/>
						<param select="X42" name="X02"/>
						<param select="X43" name="X03"/>
						<param select="N041" name="N001"/>
						<param select="N042" name="N002"/>
						<param select="N043" name="N003"/>
						<param select="N044" name="N004"/>
						<param select="X44" name="X97"/>
						<param select="X45" name="X98"/>
						<param select="X46" name="X66"/>
					</hostService>
					<hostService id="2" name="SZBFP0BF">
						<param select="X41" name="X01"/>
						<param select="X42" name="X02"/>
						<param select="X43" name="X03"/>
						<param select="N041" name="N001"/>
						<param select="N042" name="N002"/>
						<param select="N043" name="N003"/>
						<param select="N044" name="N004"/>
						<param select="X44" name="X97"/>
						<param select="X45" name="X98"/>
						<param select="X46" name="X66"/>
					</hostService>
					<hostService id="3" name="SZBQP0BQ">
						<param select="X47" name="X01"/>
						<param select="X48" name="X02"/>
						<param select="X49" name="X03"/>
						<param select="X50" name="X04"/>
						<param select="X51" name="X97"/>
						<param select="X52" name="X98"/>
					</hostService>
					<hostService id="4" name="SZBRP0BR">
						<param select="X47" name="X01"/>
						<param select="X48" name="X02"/>
						<param select="X49" name="X03"/>
						<param select="X50" name="X04"/>
						<param select="X51" name="X97"/>
						<param select="X52" name="X98"/>
					</hostService>
					<hostService id="5" name="SZBPP0BP">
						<param select="X47" name="X01"/>
						<param select="X48" name="X02"/>
						<param select="X49" name="X03"/>
						<param select="X50" name="X04"/>
						<param select="X51" name="X97"/>
						<param select="X52" name="X98"/>
					</hostService>
					<hostService id="6" name="SZTDP0TD">
						<param select="X47" name="X01"/>
						<param select="X48" name="X02"/>
						<param select="X49" name="X03"/>
						<param select="X50" name="X04"/>
						<param select="X51" name="X97"/>
						<param select="X52" name="X98"/>
					</hostService>
					<hostService id="7" name="SZBSP0BS">
						<param select="X47" name="X01"/>
						<param select="X48" name="X02"/>
						<param select="X49" name="X03"/>
						<param select="X50" name="X04"/>
						<param select="X51" name="X97"/>
						<param select="X52" name="X98"/>
					</hostService>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<scheduler>SZ00</scheduler>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>SZ00</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>
