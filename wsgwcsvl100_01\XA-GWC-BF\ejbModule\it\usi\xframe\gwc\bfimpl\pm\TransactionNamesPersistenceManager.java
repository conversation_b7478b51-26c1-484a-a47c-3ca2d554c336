/*
 * Created on Oct 19, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfimpl.pm;


import it.usi.xframe.gwc.bfutil.base.BasePersistenceManager;
import it.usi.xframe.gwc.bfutil.base.BaseSevereException;
import it.usi.xframe.gwc.bfutil.rc.TransactionResponseClass;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;

import java.util.ArrayList;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class TransactionNamesPersistenceManager extends BasePersistenceManager {

	private static TransactionNamesPersistenceManager instance = null;

	/**	Logger for debugging */
	Log logger = LogFactory.getLog(this.getClass());

	/**
	* @return singleton
	*/
	public static TransactionNamesPersistenceManager getInstance() {
		if (instance == null)
			instance = new TransactionNamesPersistenceManager();

		return instance;
	}

	protected DataValue load(Map record) {
		DataValue res = new DataValue();

		res.setCode(((String) record.get("SERV_NOME_TRAN")).trim());
		res.setDescription(((String) record.get("SERV_NOME_TRAN")).trim());
		
		return res;
	}

	//SELECT
	public TransactionResponseClass transactions()	throws BaseSevereException 
	{
		TransactionResponseClass rc = new TransactionResponseClass();

		try {
			String sqlStr =	"SELECT DISTINCT SERV_NOME_TRAN FROM DB2C.CXR0SERVIZI WHERE SERV_NOME_TRAN<>'' ORDER BY SERV_NOME_TRAN";

			setSqlList(sqlStr);
			
			
			PersistenceManager.SqlParam[] params = new PersistenceManager.SqlParam[0];
			ArrayList list = (ArrayList) executeList(params);
			rc.setList(list);
			logger.info("Records extracted = " + list.size());
			
			return rc;
			
		} catch (Exception e) {
			BaseSevereException e1 =
				new BaseSevereException(
					"An error occurred while retrieving data: " + e);
			throw e1;
		}
	}

}
