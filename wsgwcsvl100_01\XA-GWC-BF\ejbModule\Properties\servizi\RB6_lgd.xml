<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R79I-AGGR-EQUITY-C" />
					<param name="R79I-AGGR-EQUITY-CM" />
					<param name="R79I-AGGR-MORTGAGES-C" />
					<param name="R79I-AGGR-MORTGAGES-CM" />
					<param name="R79I-AGGR-NON-REALIZ-C" />
					<param name="R79I-AGGR-NON-REALIZ-CM" />
					<param name="R79I-AGGR-OTH-ILLIQ-ASS-C" />
					<param name="R79I-AGGR-OTH-ILLIQ-ASS-CM" />
					<param name="R79I-AGGR-OTH-LOANS-C" />
					<param name="R79I-AGGR-OTH-LOANS-CM" />
					<param name="R79I-AGGR-PRIOR-DEBT-C" />
					<param name="R79I-AGGR-PRIOR-DEBT-CM" />
					<param name="R79I-AGGR-SEC-LIQ-ASS-C" />
					<param name="R79I-AGGR-SEC-LIQ-ASS-CM" />
					<param name="R79I-AGGR-SENIOR-DEBT-C" />
					<param name="R79I-AGGR-SENIOR-DEBT-CM" />
					<param name="R79I-AGGR-SUB-DEBT-C" />
					<param name="R79I-AGGR-SUB-DEBT-CM" />
					<param name="R79I-AGGR-UNS-LIQ-ASS-C" />
					<param name="R79I-AGGR-UNS-LIQ-ASS-CM" />
					<param name="R79I-BANK-DEP-C" />
					<param name="R79I-BANK-DEP-CM" />
					<param name="R79I-BANK-DEP-M" />
					<param name="R79I-BANK-DEP-MISS" />
					<param name="R79I-BONDS-C" />
					<param name="R79I-BONDS-CM" />
					<param name="R79I-BONDS-M" />
					<param name="R79I-BONDS-MISS" />
					<param name="R79I-CASH-DUE-BANK-C" />
					<param name="R79I-CASH-DUE-BANK-CM" />
					<param name="R79I-CASH-DUE-BANK-I" />
					<param name="R79I-CASH-DUE-BANK-IM" />
					<param name="R79I-CASH-DUE-BANK-M" />
					<param name="R79I-CASH-DUE-BANK-MISS" />
					<param name="R79I-CERT-DEPOSITS-C" />
					<param name="R79I-CERT-DEPOSITS-CM" />
					<param name="R79I-CERT-DEPOSITS-M" />
					<param name="R79I-CERT-DEPOSITS-MISS" />
					<param name="R79I-DEP-W-BANK-C" />
					<param name="R79I-DEP-W-BANK-CM" />
					<param name="R79I-DEP-W-BANK-I" />
					<param name="R79I-DEP-W-BANK-IM" />
					<param name="R79I-DEP-W-BANK-M" />
					<param name="R79I-DEP-W-BANK-MISS" />
					<param name="R79I-DTQ1L07-A" />
					<param name="R79I-DTQ1L07-M" />
					<param name="R79I-DTQ1L07-NOTE" />
					<param name="R79I-DUE-CBANK-C" />
					<param name="R79I-DUE-CBANK-CM" />
					<param name="R79I-DUE-CBANK-I" />
					<param name="R79I-DUE-CBANK-IM" />
					<param name="R79I-DUE-CBANK-M" />
					<param name="R79I-DUE-CBANK-MISS" />
					<param name="R79I-DUE-OBANK-C" />
					<param name="R79I-DUE-OBANK-CM" />
					<param name="R79I-DUE-OBANK-I" />
					<param name="R79I-DUE-OBANK-IM" />
					<param name="R79I-DUE-OBANK-M" />
					<param name="R79I-DUE-OBANK-MISS" />
					<param name="R79I-DUE-OCRINST-C" />
					<param name="R79I-DUE-OCRINST-CM" />
					<param name="R79I-DUE-OCRINST-I" />
					<param name="R79I-DUE-OCRINST-IM" />
					<param name="R79I-DUE-OCRINST-M" />
					<param name="R79I-DUE-OCRINST-MISS" />
					<param name="R79I-EQUITY-C" />
					<param name="R79I-EQUITY-CM" />
					<param name="R79I-EQUITY-I" />
					<param name="R79I-EQUITY-IM" />
					<param name="R79I-EQUITY-INV-C" />
					<param name="R79I-EQUITY-INV-CM" />
					<param name="R79I-EQUITY-INV-M" />
					<param name="R79I-EQUITY-INV-MISS" />
					<param name="R79I-EQUITY-M" />
					<param name="R79I-EQUITY-MISS" />
					<param name="R79I-FUNCTION" />
					<param name="R79I-GEN-LOAN-LOSS-R-C" />
					<param name="R79I-GEN-LOAN-LOSS-R-CM" />
					<param name="R79I-GEN-LOAN-LOSS-R-M" />
					<param name="R79I-GEN-LOAN-LOSS-R-MISS" />
					<param name="R79I-GOV-SEC-C" />
					<param name="R79I-GOV-SEC-CM" />
					<param name="R79I-GOV-SEC-M" />
					<param name="R79I-GOV-SEC-MISS" />
					<param name="R79I-HYBRID-CAP-C" />
					<param name="R79I-HYBRID-CAP-CM" />
					<param name="R79I-HYBRID-CAP-M" />
					<param name="R79I-HYBRID-CAP-MISS" />
					<param name="R79I-INV-SEC-C" />
					<param name="R79I-INV-SEC-CM" />
					<param name="R79I-INV-SEC-M" />
					<param name="R79I-INV-SEC-MISS" />
					<param name="R79I-MM-CERT-DEP-C" />
					<param name="R79I-MM-CERT-DEP-CM" />
					<param name="R79I-MM-CERT-DEP-M" />
					<param name="R79I-MM-CERT-DEP-MISS" />
					<param name="R79I-MORTGAGES-BONDS-C" />
					<param name="R79I-MORTGAGES-BONDS-CM" />
					<param name="R79I-MORTGAGES-BONDS-M" />
					<param name="R79I-MORTGAGES-BONDS-MISS" />
					<param name="R79I-MORTGAGES-C" />
					<param name="R79I-MORTGAGES-CM" />
					<param name="R79I-MORTGAGES-M" />
					<param name="R79I-MORTGAGES-MISS" />
					<param name="R79I-NON-LIST-SEC-C" />
					<param name="R79I-NON-LIST-SEC-CM" />
					<param name="R79I-NON-LIST-SEC-M" />
					<param name="R79I-NON-LIST-SEC-MISS" />
					<param name="R79I-OTHER-BILLS-C" />
					<param name="R79I-OTHER-BILLS-CM" />
					<param name="R79I-OTHER-BILLS-M" />
					<param name="R79I-OTHER-BILLS-MISS" />
					<param name="R79I-OTHER-INV-C" />
					<param name="R79I-OTHER-INV-CM" />
					<param name="R79I-OTHER-INV-M" />
					<param name="R79I-OTHER-INV-MISS" />
					<param name="R79I-OTHER-LIABILITIES-C" />
					<param name="R79I-OTHER-LIABILITIES-CM" />
					<param name="R79I-OTHER-LIABILITIES-M" />
					<param name="R79I-OTHER-LIABILITIES-MISS" />
					<param name="R79I-OTHER-LIST-SEC-C" />
					<param name="R79I-OTHER-LIST-SEC-CM" />
					<param name="R79I-OTHER-LIST-SEC-M" />
					<param name="R79I-OTHER-LIST-SEC-MISS" />
					<param name="R79I-OTHER-SEC-C" />
					<param name="R79I-OTHER-SEC-CM" />
					<param name="R79I-OTHER-SEC-M" />
					<param name="R79I-OTHER-SEC-MISS" />
					<param name="R79I-PROPOSAL-ID" />
					<param name="R79I-SUB-DEBT-C" />
					<param name="R79I-SUB-DEBT-CM" />
					<param name="R79I-SUB-DEBT-M" />
					<param name="R79I-SUB-DEBT-MISS" />
					<param name="R79I-TOT-DEP-C" />
					<param name="R79I-TOT-DEP-CM" />
					<param name="R79I-TOT-DEP-M" />
					<param name="R79I-TOT-DEP-MISS" />
					<param name="R79I-TOT-FIXED-ASS-C" />
					<param name="R79I-TOT-FIXED-ASS-CM" />
					<param name="R79I-TOT-FIXED-ASS-M" />
					<param name="R79I-TOT-FIXED-ASS-MISS" />
					<param name="R79I-TOT-LOAN-LOSS-R-C" />
					<param name="R79I-TOT-LOAN-LOSS-R-CM" />
					<param name="R79I-TOT-LOAN-LOSS-R-M" />
					<param name="R79I-TOT-LOAN-LOSS-R-MISS" />
					<param name="R79I-TOT-LOANS-NET-C" />
					<param name="R79I-TOT-LOANS-NET-CM" />
					<param name="R79I-TOT-LOANS-NET-M" />
					<param name="R79I-TOT-LOANS-NET-MISS" />
					<param name="R79I-TOT-MM-FUND-C" />
					<param name="R79I-TOT-MM-FUND-CM" />
					<param name="R79I-TOT-MM-FUND-M" />
					<param name="R79I-TOT-MM-FUND-MISS" />
					<param name="R79I-TOT-NON-EARN-ASS-C" />
					<param name="R79I-TOT-NON-EARN-ASS-CM" />
					<param name="R79I-TOT-NON-EARN-ASS-M" />
					<param name="R79I-TOT-NON-EARN-ASS-MISS" />
					<param name="R79I-TOT-OTHER-FUND-C" />
					<param name="R79I-TOT-OTHER-FUND-CM" />
					<param name="R79I-TOT-OTHER-FUND-M" />
					<param name="R79I-TOT-OTHER-FUND-MISS" />
					<param name="R79I-TRADING-SEC-C" />
					<param name="R79I-TRADING-SEC-CM" />
					<param name="R79I-TRADING-SEC-M" />
					<param name="R79I-TRADING-SEC-MISS" />
					<param name="R79I-TREASURY-BILLS-C" />
					<param name="R79I-TREASURY-BILLS-CM" />
					<param name="R79I-TREASURY-BILLS-I" />
					<param name="R79I-TREASURY-BILLS-IM" />
					<param name="R79I-TREASURY-BILLS-M" />
					<param name="R79I-TREASURY-BILLS-MISS" />
					<param name="R79I-USER-ID" />
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB79-INPUT</hostService>
            <applBankNumber>99</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0001</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB79</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
