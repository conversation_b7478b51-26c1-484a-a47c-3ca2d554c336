package it.usi.xframe.gwc.wsutil.dto;

import java.io.Serializable;

public class GetExperianDataOutput implements Serializable {
	private static final long serialVersionUID = 1L;

	private ExperianX030OutputMap X030;
	private ExperianX031OutputMap X031;
	private ExperianX032OutputMap X032;
	private ExperianX033OutputMap X033;
	
	public GetExperianDataOutput() {
	}

	public ExperianX030OutputMap getX030() {
		return X030;
	}

	public void setX030(ExperianX030OutputMap x030) {
		X030 = x030;
	}

	public ExperianX031OutputMap getX031() {
		return X031;
	}

	public void setX031(ExperianX031OutputMap x031) {
		X031 = x031;
	}

	public ExperianX032OutputMap getX032() {
		return X032;
	}

	public void setX032(ExperianX032OutputMap x032) {
		X032 = x032;
	}

	public ExperianX033OutputMap getX033() {
		return X033;
	}

	public void setX033(ExperianX033OutputMap x033) {
		X033 = x033;
	}
}
