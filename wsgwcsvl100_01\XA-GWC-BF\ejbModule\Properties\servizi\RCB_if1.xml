<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="X82I-FUNZIONE"/>
					<param name="X82I-NUM-QUEST"/>
					<param name="X82I-USER-ID"/>
					<param name="X82I-CODICE-SCREEN"/>
					<param name="X82I-STATUS-INF1"/>
					<param name="X82I-FLAG-AMB"/>
					<param name="X82I-AL-SI"/>
					<param name="X82I-AL-C"/>
					<param name="X82I-AL-NE"/>
					<param name="X82I-AL-NO"/>
					<param name="X82I-AR-SI"/>
					<param name="X82I-AR-C"/>
					<param name="X82I-AR-NE"/>
					<param name="X82I-AR-NO"/>
					<param name="X82I-A-SI"/>
					<param name="X82I-A-C"/>
					<param name="X82I-A-NE"/>
					<param name="X82I-A-NO"/>
					<param name="X82I-S-SI"/>
					<param name="X82I-S-C"/>
					<param name="X82I-S-NE"/>
					<param name="X82I-S-NO"/>
					<param name="X82I-C-SI"/>
					<param name="X82I-C-C"/>
					<param name="X82I-C-NE"/>
					<param name="X82I-C-NO"/>
					<param name="X82I-I-SI"/>
					<param name="X82I-I-C"/>
					<param name="X82I-I-NE"/>
					<param name="X82I-I-NO"/>
					<param name="X82I-PROFILO"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>XPO082-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>XP82</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
