<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE ejb-jar PUBLIC "-//Sun Microsystems, Inc.//DTD Enterprise JavaBeans 2.0//EN" "http://java.sun.com/dtd/ejb-jar_2_0.dtd">
<ejb-jar id="ejb-jar_ID">
	<display-name>XA-GWC-BF</display-name>
	<enterprise-beans>
		<session id="GwcMain">
			<ejb-name>GwcMain</ejb-name>
			<home>it.usi.xframe.gwc.bfintf.GwcMainHome</home>
			<remote>it.usi.xframe.gwc.bfintf.GwcMain</remote>
			<local-home>it.usi.xframe.gwc.bfintf.GwcMainLocalHome</local-home>
			<local>it.usi.xframe.gwc.bfintf.GwcMainLocal</local>
			<ejb-class>it.usi.xframe.gwc.bfimpl.GwcMainBean</ejb-class>
			<session-type>Stateless</session-type>
			<transaction-type>Container</transaction-type>
			<ejb-ref id="EjbRef_1138358844311">
				<ejb-ref-name>ejb/IfgServiceFactory</ejb-ref-name>
				<ejb-ref-type>Session</ejb-ref-type>
				<home>it.usi.xframe.ifg.bfintf.IfgHome</home>
				<remote>it.usi.xframe.ifg.bfintf.Ifg</remote>
			</ejb-ref>
			<resource-ref id="ResourceRef_1138701589428">
				<description></description>
				<res-ref-name>url/log4j</res-ref-name>
				<res-type>java.net.URL</res-type>
				<res-auth>Container</res-auth>
				<res-sharing-scope>Shareable</res-sharing-scope>
			</resource-ref>
			<resource-ref id="ResourceRef_1196245403377">
				<description></description>
				<res-ref-name>jdbc/defaultds</res-ref-name>
				<res-type>javax.sql.DataSource</res-type>
				<res-auth>Container</res-auth>
				<res-sharing-scope>Shareable</res-sharing-scope>
			</resource-ref>
		</session>
	</enterprise-beans>
	<assembly-descriptor>
		<container-transaction>
			<method>
				<ejb-name>GwcMain</ejb-name>
				<method-intf>Local</method-intf>
				<method-name>UCF_db2_oracle_copy</method-name>
				<method-params>
				</method-params>
			</method>
			<method>
				<ejb-name>GwcMain</ejb-name>
				<method-intf>Remote</method-intf>
				<method-name>UCF_db2_oracle_copy</method-name>
				<method-params>
				</method-params>
			</method>
			<trans-attribute>NotSupported</trans-attribute>
		</container-transaction>
	</assembly-descriptor>
	<ejb-client-jar>XA-GWC-BFCL.jar</ejb-client-jar>
</ejb-jar>
