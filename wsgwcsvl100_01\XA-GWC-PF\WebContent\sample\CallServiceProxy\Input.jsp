<%@ taglib uri="http://java.sun.com/jstl/core" prefix="c" %>
<%@page contentType="text/html;charset=UTF-8"%>

<HTML>
<HEAD>
<TITLE>Inputs</TITLE>
</HEAD>
<BODY>
<H1>Inputs</H1>

<%
String method = request.getParameter("method");
int methodID = 0;
if (method == null) methodID = -1;

boolean valid = true;

if(methodID != -1) methodID = Integer.parseInt(method);
switch (methodID){ 
case 2:
case 3:
case 4:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">useJNDI:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="useJNDI5" SIZE=20></TD>
</TR>
</TABLE>
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 7:
case 8:
case 9:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 10:
case 11:
case 12:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">endpoint:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="endpoint13" SIZE=20></TD>
</TR>
</TABLE>
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 15:
case 16:
case 17:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 18:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">service:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="service21" SIZE="40" value="NPR_Login_RS"></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">params:</TD>
<TD ALIGN="left"><textArea cols="80" rows="60" name="params23" id="params23">X01=00094;X02=AUTHOR;X03=US00023;X04=10127;X05=XC;X99=en;</textArea></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Separatore tra Campo/Valore:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="sep125" SIZE="2" value="="></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Separatore tra Coppie</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="sep227" SIZE="2" value=";"></TD>
</TR>
</TABLE>
<BR>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Userid</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="userid" SIZE="10" value="US00465"></TD>
</TR>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Password</TD>
<TD ALIGN="left"><INPUT TYPE="password" NAME="pwd" SIZE="10" value=""></TD>
</TR>
</TABLE>
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 20:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">service:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="serviceName" SIZE="40" value="NPR_Login_RS"></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">params:</TD>
<TD ALIGN="left"><textArea cols="80" rows="60" name="paramsList" id="paramsList">X01=00094;X02=AUTHOR;X03=US00023;X04=10127;X05=XC;X99=en;</textArea></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Separatore tra Campo/Valore:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="separatore1" SIZE="2" value="="></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Separatore tra Coppie</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="separatore2" SIZE="2" value=";"></TD>
</TR>
</TABLE>
<BR>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Userid</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="userid2" SIZE="10" value="US00465"></TD>
</TR>
<TR>
<TD COLSPAN="2" ALIGN="left">
<INPUT TYPE="HIDDEN" name="pwdToken" value="<c:out value="${sessionScope.paramToken}"/>">
</TD>
</TR>
</TABLE>
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 22:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">service:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="serviceFree" SIZE="40" value="NPR_Login_RS"></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">params:</TD>
<TD ALIGN="left"><textArea cols="80" rows="60" name="paramsFree" id="paramsFree">X01=00094;X02=AUTHOR;X03=US00023;X04=10127;X05=XC;X99=en;</textArea></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Separatore tra Campo/Valore:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="sep1Free" SIZE="2" value="="></TD>
</TR>
</TABLE>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Separatore tra Coppie</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="sep2Free" SIZE="2" value=";"></TD>
</TR>
</TABLE>
<BR>
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">Userid</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="userid3" SIZE="10" value="US00465"></TD>
</TR>
</TABLE>
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 1111111111:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<TABLE>
<TR>
<TD COLSPAN="1" ALIGN="LEFT">URLString:</TD>
<TD ALIGN="left"><INPUT TYPE="TEXT" NAME="url1111111111" SIZE=20></TD>
</TR>
</TABLE>
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
case 1111111112:
valid = false;
%>
<FORM METHOD="POST" ACTION="Result.jsp" TARGET="result">
<INPUT TYPE="HIDDEN" NAME="method" VALUE="<%=method%>">
<BR>
<INPUT TYPE="SUBMIT" VALUE="Invoke">
<INPUT TYPE="RESET" VALUE="Clear">
</FORM>
<%
break;
}
if (valid) {
%>
Select a method to test.
<%
    return;
}
%>

</BODY>
</HTML>
