<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="23/03/2004 13.08.28" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="internet">
    <dispenser>
        <road>
            <request>
                <input>
                    <param name="N001" />
                    <param name="N016" />
                    <param name="N017" />
                    <param name="N018" />
                    <param name="N026" />
                    <param name="X01" />
                    <param name="X02" />
                    <param name="X03" />
                    <param name="X04" />
                    <param name="X05" />
                    <param name="X06" />
                    <param name="X07" />
                    <param name="X08" />
                    <param name="X09" />
                    <param name="X10" />
                    <param name="X11" />
                    <param name="X29" />
                    <param name="X56" />																                    
                    <param name="X57" />
                    <param name="X58" />
                    <param name="X61" />
                    <param name="X62" />
                    <param name="X63" />
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output>
                    <hostService id="1" name="XP30X30I" >
                    </hostService>
                </output>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <scheduler>XP00</scheduler>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>XP00</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>