<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
	security="intranet">
	<dispenser>
		<road>
			<request>
				<input>	
				    <param name="UW41I_FUNZ" />
				    <param name="UW41I_FLAG_TIPO" />  
				    <param name="UW41I_IMPORTO" />  
				    <param name="UW41I_DURATA" />  
				    <param name="UW41I_DIPENDENZA" />  
				    <param name="UW41I_NUMERO" /> 
				    <param name="UW41I_PROCEDURA" />      
				    <param name="UW41I_COND_CODICE_1" />  
				    <param name="UW41I_COND_VALORE_1" />
				    <param name="UW41I_COND_CODICE_2" />  
				    <param name="UW41I_COND_VALORE_2" />   
				    <param name="UW41I_COND_CODICE_3" />  
				    <param name="UW41I_COND_VALORE_3" />   
				    <param name="UW41I_COND_CODICE_4" />  
				    <param name="UW41I_COND_VALORE_4" />   
				    <param name="UW41I_COND_CODICE_5" />  
				    <param name="UW41I_COND_VALORE_5" />   
				    <param name="UW41I_COND_CODICE_6" />  
				    <param name="UW41I_COND_VALORE_6" />   
				    <param name="UW41I_COND_CODICE_7" />  
				    <param name="UW41I_COND_VALORE_7" />   
				    <param name="UW41I_COND_CODICE_8" />  
				    <param name="UW41I_COND_VALORE_8" />   
				    <param name="UW41I_COND_CODICE_9" />  
				    <param name="UW41I_COND_VALORE_9" />   
				    <param name="UW41I_COND_CODICE_10" />  
				    <param name="UW41I_COND_VALORE_10" />   
				    <param name="UW41I_COND_CODICE_11" />  
				    <param name="UW41I_COND_VALORE_11" />   
				    <param name="UW41I_COND_CODICE_12" />  
				    <param name="UW41I_COND_VALORE_12" />   
				    <param name="UW41I_COND_CODICE_13" />  
				    <param name="UW41I_COND_VALORE_13" />   
				    <param name="UW41I_COND_CODICE_14" />  
				    <param name="UW41I_COND_VALORE_14" />   
				    <param name="UW41I_COND_CODICE_15" />  
				    <param name="UW41I_COND_VALORE_15" />   
				    <param name="UW41I_COND_CODICE_16" />  
				    <param name="UW41I_COND_VALORE_16" />   
				    <param name="UW41I_COND_CODICE_17" />  
				    <param name="UW41I_COND_VALORE_17" />   
				    <param name="UW41I_COND_CODICE_18" />  
				    <param name="UW41I_COND_VALORE_18" />   
				    <param name="UW41I_COND_CODICE_19" />  
				    <param name="UW41I_COND_VALORE_19" />   
				    <param name="UW41I_COND_CODICE_20" />  
				    <param name="UW41I_COND_VALORE_20" />   
				    <param name="UW41I_COND_CODICE_21" />  
				    <param name="UW41I_COND_VALORE_21" />   
				    <param name="UW41I_COND_CODICE_22" />  
				    <param name="UW41I_COND_VALORE_22" />   
				    <param name="UW41I_COND_CODICE_23" />  
				    <param name="UW41I_COND_VALORE_23" />   
				    <param name="UW41I_COND_CODICE_24" />  
				    <param name="UW41I_COND_VALORE_24" />   
				    <param name="UW41I_COND_CODICE_25" />  
				    <param name="UW41I_COND_VALORE_25" />   
				    <param name="UW41I_COND_CODICE_26" />  
				    <param name="UW41I_COND_VALORE_26" />   
				    <param name="UW41I_COND_CODICE_27" />  
				    <param name="UW41I_COND_VALORE_27" />   
				    <param name="UW41I_COND_CODICE_28" />  
				    <param name="UW41I_COND_VALORE_28" />   
				    <param name="UW41I_COND_CODICE_29" />  
				    <param name="UW41I_COND_VALORE_29" />   
				    <param name="UW41I_COND_CODICE_30" />  
				    <param name="UW41I_COND_VALORE_30" />   
				    <param name="UW41I_COND_CODICE_31" />  
				    <param name="UW41I_COND_VALORE_31" />   
				    <param name="UW41I_COND_CODICE_32" />  
				    <param name="UW41I_COND_VALORE_32" />   
				    <param name="UW41I_COND_CODICE_33" />  
				    <param name="UW41I_COND_VALORE_33" />   
				    <param name="UW41I_COND_CODICE_34" />  
				    <param name="UW41I_COND_VALORE_34" />   
				    <param name="UW41I_COND_CODICE_35" />  
				    <param name="UW41I_COND_VALORE_35" />   
				    <param name="UW41I_COND_CODICE_36" />  
				    <param name="UW41I_COND_VALORE_36" />  
				    <param name="UW41I_COND_CODICE_37" />  
				    <param name="UW41I_COND_VALORE_37" />    
				    <param name="UW41I_COND_CODICE_38" />  
				    <param name="UW41I_COND_VALORE_38" />   
				    <param name="UW41I_COND_CODICE_39" />  
				    <param name="UW41I_COND_VALORE_39" />   
				    <param name="UW41I_COND_CODICE_40" />  
				    <param name="UW41I_COND_VALORE_40" />   
				    <param name="UW41I_COND_CODICE_41" />  
				    <param name="UW41I_COND_VALORE_41" />   
				    <param name="UW41I_COND_CODICE_42" />  
				    <param name="UW41I_COND_VALORE_42" />   
				    <param name="UW41I_COND_CODICE_43" />  
				    <param name="UW41I_COND_VALORE_43" />   
				    <param name="UW41I_COND_CODICE_44" />  
				    <param name="UW41I_COND_VALORE_44" />   
				    <param name="UW41I_COND_CODICE_45" />  
				    <param name="UW41I_COND_VALORE_45" />   
				    <param name="UW41I_COND_CODICE_46" />  
				    <param name="UW41I_COND_VALORE_46" />   
				    <param name="UW41I_COND_CODICE_47" />  
				    <param name="UW41I_COND_VALORE_47" />   
				    <param name="UW41I_COND_CODICE_48" />  
				    <param name="UW41I_COND_VALORE_48" />   
				    <param name="UW41I_COND_CODICE_49" />  
				    <param name="UW41I_COND_VALORE_49" />   
				    <param name="UW41I_COND_CODICE_50" />  
				    <param name="UW41I_COND_VALORE_50" />       
                    <param name="XF_GAUSS_ID"/>
				</input>
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</road>
       <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
    </provider>
	<protocol>
		<bridge>
			<request>
				<input />
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</bridge>
		<params>
			<hostService>UWM_SALVATAGGIO_CTB</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUW1</transaction>
			<program>PC00WUW1</program>
			<timeout>15000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>