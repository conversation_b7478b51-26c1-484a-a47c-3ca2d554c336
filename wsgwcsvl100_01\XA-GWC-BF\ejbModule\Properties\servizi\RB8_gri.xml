<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R87I-PROPOSAL-ID"/>
					<param name="R87I-FUNCTION"/>
					<param name="R87I-FLAG-ENV"/>
					<param name="R87I-GRI-STATUS"/>
					<param name="R87I-USER-ID"/>
					<param name="R87I-AUTH-LEV"/>
					<param name="R87I-QUESTION-LIST"/>
					<param name="R87I-NOTE"/>
					<param name="R87I-CHECK-1-A"/>
					<param name="R87I-CHECK-1-M"/>
					<param name="R87I-CHECK-2-A"/>
					<param name="R87I-CHECK-2-M"/>
			    </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB87-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB87</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
