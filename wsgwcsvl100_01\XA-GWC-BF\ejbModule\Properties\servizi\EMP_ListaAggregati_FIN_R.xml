<?xml version="1.0" encoding="UTF-8"?>
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="EM13I_PROGR_CHIAM"/>
					<param name="EM13I_BANCA"/>
					<param name="EM13I_CONV_TIPO"/>
					<param name="EM13I_CONV_COD"/>
					<param name="EM13I_PRODOTTO"/>
					<param name="EM13I_ATTRIB_01"/>
					<param name="EM13I_VALORE_DA_01"/>
					<param name="EM13I_VALORE_A_01"/>
					<param name="EM13I_ATTRIB_02"/>
					<param name="EM13I_VALORE_DA_02"/>
					<param name="EM13I_VALORE_A_02"/>
					<param name="EM13I_ATTRIB_03"/>
					<param name="EM13I_VALORE_DA_03"/>
					<param name="EM13I_VALORE_A_03"/>
					<param name="EM13I_ATTRIB_04"/>
					<param name="EM13I_VALORE_DA_04"/>
					<param name="EM13I_VALORE_A_04"/>
					<param name="EM13I_ATTRIB_05"/>
					<param name="EM13I_VALORE_DA_05"/>
					<param name="EM13I_VALORE_A_05"/>
					<param name="EM13I_ATTRIB_06"/>
					<param name="EM13I_VALORE_DA_06"/>
					<param name="EM13I_VALORE_A_06"/>
					<param name="EM13I_ATTRIB_07"/>
					<param name="EM13I_VALORE_DA_07"/>
					<param name="EM13I_VALORE_A_07"/>
					<param name="EM13I_ATTRIB_08"/>
					<param name="EM13I_VALORE_DA_08"/>
					<param name="EM13I_VALORE_A_08"/>
					<param name="EM13I_ATTRIB_09"/>
					<param name="EM13I_VALORE_DA_09"/>
					<param name="EM13I_VALORE_A_09"/>
					<param name="EM13I_ATTRIB_10"/>
					<param name="EM13I_VALORE_DA_10"/>
					<param name="EM13I_VALORE_A_10"/>
					<param name="EM13I_ATTRIB_11"/>
					<param name="EM13I_VALORE_DA_11"/>
					<param name="EM13I_VALORE_A_11"/>
					<param name="EM13I_ATTRIB_12"/>
					<param name="EM13I_VALORE_DA_12"/>
					<param name="EM13I_VALORE_A_12"/>
					<param name="EM13I_ATTRIB_13"/>
					<param name="EM13I_VALORE_DA_13"/>
					<param name="EM13I_VALORE_A_13"/>
					<param name="EM13I_ATTRIB_14"/>
					<param name="EM13I_VALORE_DA_14"/>
					<param name="EM13I_VALORE_A_14"/>
					<param name="EM13I_ATTRIB_15"/>
					<param name="EM13I_VALORE_DA_15"/>
					<param name="EM13I_VALORE_A_15"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<hostService>EMP0-DATICPCR031-INPUT</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>01</servBankNumber>
			<version>0100</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WEMP</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>