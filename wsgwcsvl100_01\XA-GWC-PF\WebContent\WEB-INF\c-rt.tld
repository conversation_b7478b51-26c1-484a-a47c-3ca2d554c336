<?xml version="1.0" encoding="ISO-8859-1" ?>
<!DOCTYPE taglib
  PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.2//EN"
  "http://java.sun.com/dtd/web-jsptaglibrary_1_2.dtd">
<taglib>
  <tlib-version>1.0</tlib-version>
  <jsp-version>1.2</jsp-version>
  <short-name>c_rt</short-name>
  <uri>http://java.sun.com/jstl/core_rt</uri>
  <display-name>JSTL core RT</display-name>
  <description>JSTL 1.0 core library</description>

  <validator>
    <validator-class>
        org.apache.taglibs.standard.tlv.JstlCoreTLV
    </validator-class>
    <description>
        Provides core validation features for JSTL tags.
    </description>
  </validator>

  <tag>
    <name>catch</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.CatchTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Catches any Throwable that occurs in its body and optionally
        exposes it.
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>choose</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.ChooseTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Simple conditional tag that establishes a context for
	mutually exclusive conditional operations, marked by
	&lt;when&gt; and &lt;otherwise&gt;
    </description>
  </tag>

  <tag>
    <name>if</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.IfTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Simple conditional tag, which evalutes its body if the
	supplied condition is true and optionally exposes a Boolean
	scripting variable representing the evaluation of this condition
    </description>
    <attribute>
        <name>test</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
	<type>boolean</type>
    </attribute>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>import</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ImportTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.ImportTEI</tei-class>
    <body-content>JSP</body-content>
    <description>
        Retrieves an absolute or relative URL and exposes its contents
        to either the page, a String in 'var', or a Reader in 'varReader'.
    </description>
    <attribute>
        <name>url</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>varReader</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>charEncoding</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>forEach</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ForEachTag</tag-class>
    <tei-class>org.apache.taglibs.standard.tei.ForEachTEI</tei-class>
    <body-content>JSP</body-content>
    <description>
	The basic iteration tag, accepting many different
        collection types and supporting subsetting and other
        functionality
    </description>
    <attribute>
	<name>items</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>java.lang.Object</type>
    </attribute>
    <attribute>
	<name>begin</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
	<name>end</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
	<name>step</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
	<name>var</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>varStatus</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>forTokens</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ForTokensTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Iterates over tokens, separated by the supplied delimeters
    </description>
    <attribute>
	<name>items</name>
	<required>true</required>
	<rtexprvalue>true</rtexprvalue>
	<type>java.lang.String</type>
    </attribute>
    <attribute>
	<name>delims</name>
	<required>true</required>
	<rtexprvalue>true</rtexprvalue>
	<type>java.lang.String</type>
    </attribute>
    <attribute>
	<name>begin</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
	<name>end</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
	<name>step</name>
	<required>false</required>
	<rtexprvalue>true</rtexprvalue>
	<type>int</type>
    </attribute>
    <attribute>
	<name>var</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
	<name>varStatus</name>
	<required>false</required>
	<rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>out</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.OutTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Like &lt;%= ... &gt;, but for expressions.
    </description> 
    <attribute>
        <name>value</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>default</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>escapeXml</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>


  <tag>
    <name>otherwise</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.OtherwiseTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Subtag of &lt;choose&gt; that follows &lt;when&gt; tags
        and runs only if all of the prior conditions evaluated to
        'false'
    </description>
  </tag>

  <tag>
    <name>param</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.ParamTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Adds a parameter to a containing 'import' tag's URL.
    </description>
    <attribute>
        <name>name</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>redirect</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.RedirectTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Redirects to a new URL.
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>url</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>remove</name>
    <tag-class>org.apache.taglibs.standard.tag.common.core.RemoveTag</tag-class>
    <body-content>empty</body-content>
    <description>
        Removes a scoped variable (from a particular scope, if specified).
    </description>
    <attribute>
        <name>var</name>
        <required>true</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

 <tag>
    <name>set</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.SetTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Sets the result of an expression evaluation in a 'scope'
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>target</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>property</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>url</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.UrlTag</tag-class>
    <body-content>JSP</body-content>
    <description>
        Creates a URL with optional query parameters.
    </description>
    <attribute>
        <name>var</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>scope</name>
        <required>false</required>
        <rtexprvalue>false</rtexprvalue>
    </attribute>
    <attribute>
        <name>value</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
        <name>context</name>
        <required>false</required>
        <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>

  <tag>
    <name>when</name>
    <tag-class>org.apache.taglibs.standard.tag.rt.core.WhenTag</tag-class>
    <body-content>JSP</body-content>
    <description>
	Subtag of &lt;choose&gt; that includes its body if its
	condition evalutes to 'true'
    </description>
    <attribute>
        <name>test</name>
        <required>true</required>
        <rtexprvalue>true</rtexprvalue>
	<type>boolean</type>
    </attribute>
  </tag>

</taglib>
