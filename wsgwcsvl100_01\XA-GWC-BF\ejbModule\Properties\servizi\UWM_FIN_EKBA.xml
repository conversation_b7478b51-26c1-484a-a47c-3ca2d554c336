<?xml version="1.0"?>
<!-- This XML has been Auto-Generated -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
	security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="SERVIZIO" />
					<param name="VERSIONE" />
					<param name="COD_BAN_SERV" />
					<param name="FIN_FAMR28_TIPO" />
					<param name="FIN_FAMR28_CAT" />
					<param name="FIN_FAMR28_NUM" />
					<param name="FIN_FAMR28_SUB" />
					<param name="FIN_FAMR28_SPORTELLO" />
					<param name="FIN_FAMR28_TIPO_RAP" />
					<param name="FIN_FAMR28_NUM_CONTO" />
					<param name="FIN_FAMR28_DT_RIF" />
					<param name="FIN_FAMR28_COD_BANCA" />
					<param name="FIN_FAMR28_DATI_ANAG" />
					<param name="FIN_FAMR28_DATI_TASSO" />
					<param name="FIN_FAMR28_DATI_CONT" />
					<param name="FIN_FAMR28_DATI_ISTR" />
					<param name="FIN_FAMR28_DATI_SCAD" />
					<param name="FIN_FAMR28_DATI_EROG" />
					<param name="FIN_FAMR28_DATI_DESC" />
					<param name="FIN_FAMR28_IMP_PROX_RATA" />
					<param name="FIN_FAMR28_DATI_EXTRA" />
					<param name="FIN_FAMR28_CALC_TASS_CAP" />
					<param name="FIN_FAMR28_SW_DATI_COVER" />
					<param name="FIN_FAMR28_NDG" />
					<param name="FIN_FAMR28_PROG_FIDO" />
					<param name="FIN_CXXX_MATRICOLA_USI" />
					<param name="FIN_CXXX_MATRICOLA_CONT" />
					<param name="FIN_CXXX_TERMINALE" />
					<param name="FIN_CXXX_DIP" />
					<param name="FIN_CXXX_LINGUA_UTENTE" />
					<param name="FIN_CXXX_LINGUA_PAESE" />
					<param name="FIN_CXXX_COD_PROC" />
					<param name="XF_GAUSS_ID" />
				</input>
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
    </provider>
	<protocol>
		<bridge>
			<request>
				<input />
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</bridge>
		<params>
			<hostService>UWM_FIN_EK</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUW1</transaction>
			<program>PC00WUW1</program>
			<timeout>15000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>

