<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.14.284" utente="Gasparini" Timestamp="06/05/2004 19.07.49" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="X02"/>
					<param name="X03"/>
					<param name="X04"/>
					<param name="X05"/>
					<param name="X06"/>
					<param name="X09"/>
					<param name="X10"/>
					<param name="X11"/>
					<param name="X13"/>
					<param name="X16"/>
					<param name="X17"/>
					<param name="X18"/>
					<param name="X19"/>
					<param name="N001"/>
					<param name="N002"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output>
					<hostService id="1" name="KB21CB21">
					</hostService>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<scheduler>KB00</scheduler>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>KB00</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>
