<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="23/03/2004 13.08.28" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="SELETTORE"/>
					<param name="PRZUBZCI_BANCODE"/>
					<param name="PRZUBZCI_NDG"/>
					<param name="PRZUBZCI_PROG_GAR"/>
					<param name="PRZUBZCO_NDG"/>
					<param name="PRZUBZCO_PROG_GAR"/>
					<param name="PRZUBZCO_PRZ_ID"/>
					<param name="PRZUBZCO_DATA_PERF"/>
					<param name="PRZUBZCO_STATO"/>
					<param name="PRZUBZCO_TOTAL_VALUE"/>
					<param name="PRZUBZCO_IMM_VALUE"/>
					<param name="PRZUBZCO_CAUZ_VALUE"/>
					<param name="PRZUBZCO_TIPO"/>
					<param name="XF_GAUSS_ID"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.GaussProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<hostService>PRZ_MAIN_PERIZIE</hostService>
			<applBankNumber>99</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WPRZ</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
			<synclevel>0</synclevel>
		</params>
	</channel>
</service>