<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFin_Tasso">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFin_TassoResponse">
    <complexType>
     <sequence>
      <element name="nprFin_TassoReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFin_TassoRequest">

      <wsdl:part element="impl:nprFin_Tasso" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFin_TassoResponse">

      <wsdl:part element="impl:nprFin_TassoResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fin_Tasso_SEI">

      <wsdl:operation name="nprFin_Tasso">

         <wsdl:input message="impl:nprFin_TassoRequest" name="nprFin_TassoRequest"/>

         <wsdl:output message="impl:nprFin_TassoResponse" name="nprFin_TassoResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fin_TassoSoapBinding" type="impl:NPR_Fin_Tasso_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFin_Tasso">

         <wsdlsoap:operation soapAction="nprFin_Tasso"/>

         <wsdl:input name="nprFin_TassoRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFin_TassoResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fin_TassoService">

      <wsdl:port binding="impl:NPR_Fin_TassoSoapBinding" name="NPR_Fin_Tasso">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fin_Tasso"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
