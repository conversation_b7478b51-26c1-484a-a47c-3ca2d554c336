<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R58I-AGG-DEP-SENDEBT-C"/>
					<param name="R58I-AGG-DEP-SENDEBT-DQ"/>
					<param name="R58I-AGG-EQUITY-C"/>
					<param name="R58I-AGG-EQUITY-DQ"/>
					<param name="R58I-AGG-LIQUID-ASS-C"/>
					<param name="R58I-AGG-LIQUID-ASS-DQ"/>
					<param name="R58I-AGG-MORTGAGES-C"/>
					<param name="R58I-AGG-MORTGAGES-DQ"/>
					<param name="R58I-AGG-NON-REALIZABLE-C"/>
					<param name="R58I-AGG-NON-REALIZABLE-DQ"/>
					<param name="R58I-AGG-OTH-ILLIQ-ASS-C"/>
					<param name="R58I-AGG-OTH-ILLIQ-ASS-DQ"/>
					<param name="R58I-AGG-OTH-LOANS-C"/>
					<param name="R58I-AGG-OTH-LOANS-DQ"/>
					<param name="R58I-AGG-PRI-DEDUCTIONS-C"/>
					<param name="R58I-AGG-PRI-DEDUCTIONS-DQ"/>
					<param name="R58I-AGG-SUB-DEBTS-C"/>
					<param name="R58I-AGG-SUB-DEBTS-DQ"/>
					<param name="R58I-BONDS-C"/>
					<param name="R58I-BONDS-CM"/>
					<param name="R58I-BONDS-M"/>
					<param name="R58I-BONDS-S"/>
					<param name="R58I-BONDS-SM"/>
					<param name="R58I-CASH-DUE-BANK-C"/>
					<param name="R58I-CASH-DUE-BANK-CM"/>
					<param name="R58I-CASH-DUE-BANK-I"/>
					<param name="R58I-CASH-DUE-BANK-IM"/>
					<param name="R58I-CASH-DUE-BANK-M"/>
					<param name="R58I-CASH-DUE-BANK-S"/>
					<param name="R58I-CASH-DUE-BANK-SM"/>
					<param name="R58I-CDS-C"/>
					<param name="R58I-CDS-CM"/>
					<param name="R58I-CDS-M"/>
					<param name="R58I-CDS-S"/>
					<param name="R58I-CDS-SM"/>
					<param name="R58I-DEP-BANK-C"/>
					<param name="R58I-DEP-BANK-CM"/>
					<param name="R58I-DEP-BANK-I"/>
					<param name="R58I-DEP-BANK-IM"/>
					<param name="R58I-DEP-BANK-M"/>
					<param name="R58I-DEP-BANK-S"/>
					<param name="R58I-DEP-BANK-SM"/>
					<param name="R58I-DUE-CBANK-C"/>
					<param name="R58I-DUE-CBANK-CM"/>
					<param name="R58I-DUE-CBANK-I"/>
					<param name="R58I-DUE-CBANK-IM"/>
					<param name="R58I-DUE-CBANK-M"/>
					<param name="R58I-DUE-CBANK-S"/>
					<param name="R58I-DUE-CBANK-SM"/>
					<param name="R58I-DUE-OBANK-C"/>
					<param name="R58I-DUE-OBANK-CM"/>
					<param name="R58I-DUE-OBANK-I"/>
					<param name="R58I-DUE-OBANK-IM"/>
					<param name="R58I-DUE-OBANK-M"/>
					<param name="R58I-DUE-OBANK-S"/>
					<param name="R58I-DUE-OBANK-SM"/>
					<param name="R58I-DUE-OCRINST-C"/>
					<param name="R58I-DUE-OCRINST-CM"/>
					<param name="R58I-DUE-OCRINST-I"/>
					<param name="R58I-DUE-OCRINST-IM"/>
					<param name="R58I-DUE-OCRINST-M"/>
					<param name="R58I-DUE-OCRINST-S"/>
					<param name="R58I-DUE-OCRINST-SM"/>
					<param name="R58I-EQUITY-C"/>
					<param name="R58I-EQUITY-CM"/>
					<param name="R58I-EQUITY-INV-C"/>
					<param name="R58I-EQUITY-INV-CM"/>
					<param name="R58I-EQUITY-INV-M"/>
					<param name="R58I-EQUITY-INV-S"/>
					<param name="R58I-EQUITY-INV-SM"/>
					<param name="R58I-EQUITY-M"/>
					<param name="R58I-EQUITY-S"/>
					<param name="R58I-EQUITY-SM"/>
					<param name="R58I-FUNCTION"/>
					<param name="R58I-HYBRID-CAP-C"/>
					<param name="R58I-HYBRID-CAP-CM"/>
					<param name="R58I-HYBRID-CAP-M"/>
					<param name="R58I-HYBRID-CAP-S"/>
					<param name="R58I-HYBRID-CAP-SM"/>
					<param name="R58I-LOAN-LOSS-RES-C"/>
					<param name="R58I-LOAN-LOSS-RES-CM"/>
					<param name="R58I-LOAN-LOSS-RES-M"/>
					<param name="R58I-LOAN-LOSS-RES-S"/>
					<param name="R58I-LOAN-LOSS-RES-SM"/>
					<param name="R58I-MORTGAGES-BONDS-C"/>
					<param name="R58I-MORTGAGES-BONDS-CM"/>
					<param name="R58I-MORTGAGES-BONDS-M"/>
					<param name="R58I-MORTGAGES-BONDS-S"/>
					<param name="R58I-MORTGAGES-BONDS-SM"/>
					<param name="R58I-MORTGAGES-C"/>
					<param name="R58I-MORTGAGES-CM"/>
					<param name="R58I-MORTGAGES-M"/>
					<param name="R58I-MORTGAGES-S"/>
					<param name="R58I-MORTGAGES-SM"/>
					<param name="R58I-OTHER-BILLS-C"/>
					<param name="R58I-OTHER-BILLS-CM"/>
					<param name="R58I-OTHER-BILLS-M"/>
					<param name="R58I-OTHER-BILLS-S"/>
					<param name="R58I-OTHER-BILLS-SM"/>
					<param name="R58I-OTHER-INV-C"/>
					<param name="R58I-OTHER-INV-CM"/>
					<param name="R58I-OTHER-INV-M"/>
					<param name="R58I-OTHER-INV-S"/>
					<param name="R58I-OTHER-INV-SM"/>
					<param name="R58I-OTHER-LIAB-C"/>
					<param name="R58I-OTHER-LIAB-CM"/>
					<param name="R58I-OTHER-LIAB-M"/>
					<param name="R58I-OTHER-LIAB-S"/>
					<param name="R58I-OTHER-LIAB-SM"/>
					<param name="R58I-PROPOSAL-ID"/>
					<param name="R58I-SUB-DEBT-C"/>
					<param name="R58I-SUB-DEBT-CM"/>
					<param name="R58I-SUB-DEBT-M"/>
					<param name="R58I-SUB-DEBT-S"/>
					<param name="R58I-SUB-DEBT-SM"/>
					<param name="R58I-TOT-DEPOSIT-C"/>
					<param name="R58I-TOT-DEPOSIT-CM"/>
					<param name="R58I-TOT-DEPOSIT-M"/>
					<param name="R58I-TOT-DEPOSIT-S"/>
					<param name="R58I-TOT-DEPOSIT-SM"/>
					<param name="R58I-TOT-FIXED-ASS-C"/>
					<param name="R58I-TOT-FIXED-ASS-CM"/>
					<param name="R58I-TOT-FIXED-ASS-M"/>
					<param name="R58I-TOT-FIXED-ASS-S"/>
					<param name="R58I-TOT-FIXED-ASS-SM"/>
					<param name="R58I-TOT-LLOSS-RES-C"/>
					<param name="R58I-TOT-LLOSS-RES-CM"/>
					<param name="R58I-TOT-LLOSS-RES-M"/>
					<param name="R58I-TOT-LLOSS-RES-S"/>
					<param name="R58I-TOT-LLOSS-RES-SM"/>
					<param name="R58I-TOT-LOANS-C"/>
					<param name="R58I-TOT-LOANS-CM"/>
					<param name="R58I-TOT-LOANS-M"/>
					<param name="R58I-TOT-LOANS-S"/>
					<param name="R58I-TOT-LOANS-SM"/>
					<param name="R58I-TOT-MMARKET-FUND-C"/>
					<param name="R58I-TOT-MMARKET-FUND-CM"/>
					<param name="R58I-TOT-MMARKET-FUND-M"/>
					<param name="R58I-TOT-MMARKET-FUND-S"/>
					<param name="R58I-TOT-MMARKET-FUND-SM"/>
					<param name="R58I-TOT-NOTEARN-ASS-C"/>
					<param name="R58I-TOT-NOTEARN-ASS-CM"/>
					<param name="R58I-TOT-NOTEARN-ASS-M"/>
					<param name="R58I-TOT-NOTEARN-ASS-S"/>
					<param name="R58I-TOT-NOTEARN-ASS-SM"/>
					<param name="R58I-TOT-OTHER-FUND-C"/>
					<param name="R58I-TOT-OTHER-FUND-CM"/>
					<param name="R58I-TOT-OTHER-FUND-M"/>
					<param name="R58I-TOT-OTHER-FUND-S"/>
					<param name="R58I-TOT-OTHER-FUND-SM"/>
					<param name="R58I-TOT-SECURITY-C"/>
					<param name="R58I-TOT-SECURITY-CM"/>
					<param name="R58I-TOT-SECURITY-M"/>
					<param name="R58I-TOT-SECURITY-S"/>
					<param name="R58I-TOT-SECURITY-SM"/>
					<param name="R58I-TREAS-BILL-C"/>
					<param name="R58I-TREAS-BILL-CM"/>
					<param name="R58I-TREAS-BILL-I"/>
					<param name="R58I-TREAS-BILL-IM"/>
					<param name="R58I-TREAS-BILL-M"/>
					<param name="R58I-TREAS-BILL-S"/>
					<param name="R58I-TREAS-BILL-SM"/>
					<param name="R58I-USER-ID"/>				
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB58-INPUT</hostService>
            <applBankNumber>99</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0001</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB58</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
