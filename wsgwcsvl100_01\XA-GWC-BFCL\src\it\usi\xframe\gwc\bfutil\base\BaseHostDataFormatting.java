package it.usi.xframe.gwc.bfutil.base;

import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.utl.bfintf.IHostDataFormatting;
import it.usi.xframe.utl.bfutil.HostDataFormatting;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;

public class BaseHostDataFormatting extends HostDataFormatting implements Serializable, IHostDataFormatting {

	/**
	 * 
	 */
	public BaseHostDataFormatting() {
		super();
	}

	public String getStringTrue() {
		return "Y";
	}

	public String getStringFalse() {
		return "N";
	}

	public DateFormat getDateFormat() {
		SimpleDateFormat sdf = new SimpleDateFormat("dd.MM.yyyy");
		return sdf;
	}
	

	public BigDecimal parseDecimal(String value) throws XFRException {
		// TODO Auto-generated method stub
		return null;
	}


}
