<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="2.2.167" utente="pipinato" Timestamp="04/02/2002 16:56:46" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="EGUK04CE-STATUS-AGG"/>
					<param name="EGUK04CE-TELEFONI-AGG"/>
					<param name="EGUK04CE-INDIR-CORR-AGG"/>
					<param name="EGUK04CE-ALTRI-DATI-AGG"/>
					<param name="EGUK04CE-CODICI-VARI-AGG"/>
					<param name="EGUK04CE-SEDE-FISCALE-AGG"/>
					<param name="EGUK04CE-DATI-PRIVACY-AGG"/>
					<param name="EGUK04CE-DATI-GENERALI-AGG"/>
					<param name="EGUK04CE-DATI-AZIENDALI-AGG"/>
					<param name="EGUK04CE-PROFESSIONI-ALT-AGG"/>
					<param name="EGUK04CE-INTESTAZIONE-LONG-AGG"/>
					<param name="EGUK04CE-CLASSIFICAZIONE-ALT-A"/>
					<param name="EGUK04CE-NDG"/>
					<param name="EGUK04CE-CONTROLLO-INT"/>
					<param name="EGUK04CE-CODICE-FISCALE"/>
					<param name="EGUK04CE-PARTITA-IVA"/>
					<param name="EGUK04CE-SPORTELLO-CAPOFILA"/>
					<param name="EGUK04CE-INTESTAZIONE-A"/>
					<param name="EGUK04CE-INTESTAZIONE-B"/>
					<param name="EGUK04CE-TIPO-NDG"/>
					<param name="EGUK04CE-SETTORISTA"/>
					<param name="EGUK04CE-PSEUDONIMO"/>
					<param name="EGUK04CE-STATO-RES"/>
					<param name="EGUK04CE-COMUNE-RES"/>
					<param name="EGUK04CE-LOCALITA-RES"/>
					<param name="EGUK04CE-PROVINCIA-RES"/>
					<param name="EGUK04CE-CAP-RES"/>
					<param name="EGUK04CE-VIA-RES"/>
					<param name="EGUK04CE-PRESSO-RES"/>
					<param name="EGUK04CE-INTESTAZIONE-CORR"/>
					<param name="EGUK04CE-STATO-CORR"/>
					<param name="EGUK04CE-COMUNE-CORR"/>
					<param name="EGUK04CE-LOCALITA-CORR"/>
					<param name="EGUK04CE-PROVINCIA-CORR"/>
					<param name="EGUK04CE-CAP-CORR"/>
					<param name="EGUK04CE-VIA-CORR"/>
					<param name="EGUK04CE-PRESSO-CORR"/>
					<param name="EGUK04CE-UFFICIO-CORR"/>
					<param name="EGUK04CE-CASELLARIO-CORR"/>
					<param name="EGUK04CE-CODICE-FISCALE-ESTERO"/>
					<param name="EGUK04CE-RAMO"/>
					<param name="EGUK04CE-SETTORE"/>
					<param name="EGUK04CE-RAMO-ALT"/>
					<param name="EGUK04CE-SETTORE-ALT"/>
					<param name="EGUK04CE-COMUNE-ALT"/>
					<param name="EGUK04CE-PROVINCIA-ALT"/>
					<param name="EGUK04CE-STATO-ALT"/>
					<param name="EGUK04CE-SEGMENTO"/>
					<param name="EGUK04CE-SIT-GIURIDICA"/>
					<param name="EGUK04CE-STATUS"/>
					<param name="EGUK04CE-UBIC-DOC-RIFERIM"/>
					<param name="EGUK04CE-UBIC-DOC-SPORTELLO"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-1"/>
					<param name="EGUK04CE-TEL-PROGR-1"/>
					<param name="EGUK04CE-TIPO-TELEFONO-1"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-1"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-2"/>
					<param name="EGUK04CE-TEL-PROGR-2"/>
					<param name="EGUK04CE-TIPO-TELEFONO-2"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-2"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-3"/>
					<param name="EGUK04CE-TEL-PROGR-3"/>
					<param name="EGUK04CE-TIPO-TELEFONO-3"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-3"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-4"/>
					<param name="EGUK04CE-TEL-PROGR-4"/>
					<param name="EGUK04CE-TIPO-TELEFONO-4"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-4"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-5"/>
					<param name="EGUK04CE-TEL-PROGR-5"/>
					<param name="EGUK04CE-TIPO-TELEFONO-5"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-5"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-6"/>
					<param name="EGUK04CE-TEL-PROGR-6"/>
					<param name="EGUK04CE-TIPO-TELEFONO-6"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-6"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-7"/>
					<param name="EGUK04CE-TEL-PROGR-7"/>
					<param name="EGUK04CE-TIPO-TELEFONO-7"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-7"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-8"/>
					<param name="EGUK04CE-TEL-PROGR-8"/>
					<param name="EGUK04CE-TIPO-TELEFONO-8"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-8"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-9"/>
					<param name="EGUK04CE-TEL-PROGR-9"/>
					<param name="EGUK04CE-TIPO-TELEFONO-9"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-9"/>
					<param name="EGUK04CE-TEL-OPERAZIONE-10"/>
					<param name="EGUK04CE-TEL-PROGR-10"/>
					<param name="EGUK04CE-TIPO-TELEFONO-10"/>
					<param name="EGUK04CE-NUMERO-TELEFONO-10"/>
					<param name="EGUK04CE-CONSENSO-1"/>
					<param name="EGUK04CE-DT-CONSENSO-1"/>
					<param name="EGUK04CE-CONSENSO-2"/>
					<param name="EGUK04CE-DT-CONSENSO-2"/>
					<param name="EGUK04CE-CONSENSO-3"/>
					<param name="EGUK04CE-DT-CONSENSO-3"/>
					<param name="EGUK04CE-CONSENSO-4"/>
					<param name="EGUK04CE-DT-CONSENSO-4"/>
					<param name="EGUK04CE-CONSENSO-5"/>
					<param name="EGUK04CE-DT-CONSENSO-5"/>
					<param name="EGUK04CE-CONSENSO-6"/>
					<param name="EGUK04CE-DT-CONSENSO-6"/>
					<param name="EGUK04CE-INVIO-INFORMATIVA"/>
					<param name="EGUK04CE-ESENZ-FISC-DATA-EM"/>
					<param name="EGUK04CE-ESENZ-FISC-DATA-SCAD"/>
					<param name="EGUK04CE-ESENZ-FISC-TIPO"/>
					<param name="EGUK04CE-OPERATORE"/>
					<param name="EGUK04PF-DOCUMENTI-AGG"/>
					<param name="EGUK04PF-PREF-COGN-ACQUIS"/>
					<param name="EGUK04PF-COGNOME-ACQUISITO"/>
					<param name="EGUK04PF-TITOLI"/>
					<param name="EGUK04PF-SESSO"/>
					<param name="EGUK04PF-DATA-NASCITA"/>
					<param name="EGUK04PF-COMUNE-NASCITA"/>
					<param name="EGUK04PF-PROVINCIA-NASC"/>
					<param name="EGUK04PF-STATO-NASCITA"/>
					<param name="EGUK04PF-DATA-DECESSO"/>
					<param name="EGUK04PF-EREDITA"/>
					<param name="EGUK04PF-PROFESSIONE"/>
					<param name="EGUK04PF-PROF-ALT-1"/>
					<param name="EGUK04PF-PROF-ALT-2"/>
					<param name="EGUK04PF-PROF-ALT-3"/>
					<param name="EGUK04PF-PROF-ALT-4"/>
					<param name="EGUK04PF-PROF-ALT-5"/>
					<param name="EGUK04PF-DOC-OPERAZIONE-1"/>
					<param name="EGUK04PF-TIPO-DOC-1"/>
					<param name="EGUK04PF-NUMERO-DOC-1"/>
					<param name="EGUK04PF-DATA-RIL-DOC-1"/>
					<param name="EGUK04PF-PROV-RIL-DOC-1"/>
					<param name="EGUK04PF-COMUNE-RIL-DOC-1"/>
					<param name="EGUK04PF-STATO-RIL-DOC-1"/>
					<param name="EGUK04PF-ENTE-RILAS-DOC-1"/>
					<param name="EGUK04PF-DATA-SCA-DOC-1"/>
					<param name="EGUK04PF-DOC-OPERAZIONE-2"/>
					<param name="EGUK04PF-TIPO-DOC-2"/>
					<param name="EGUK04PF-NUMERO-DOC-2"/>
					<param name="EGUK04PF-DATA-RIL-DOC-2"/>
					<param name="EGUK04PF-PROV-RIL-DOC-2"/>
					<param name="EGUK04PF-COMUNE-RIL-DOC-2"/>
					<param name="EGUK04PF-STATO-RIL-DOC-2"/>
					<param name="EGUK04PF-ENTE-RILAS-DOC-2"/>
					<param name="EGUK04PF-DATA-SCA-DOC-2"/>
					<param name="EGUK04PF-DOC-OPERAZIONE-3"/>
					<param name="EGUK04PF-TIPO-DOC-3"/>
					<param name="EGUK04PF-NUMERO-DOC-3"/>
					<param name="EGUK04PF-DATA-RIL-DOC-3"/>
					<param name="EGUK04PF-PROV-RIL-DOC-3"/>
					<param name="EGUK04PF-COMUNE-RIL-DOC-3"/>
					<param name="EGUK04PF-STATO-RIL-DOC-3"/>
					<param name="EGUK04PF-ENTE-RILAS-DOC-3"/>
					<param name="EGUK04PF-DATA-SCA-DOC-3"/>
					<param name="EGUK04PF-DOC-OPERAZIONE-4"/>
					<param name="EGUK04PF-TIPO-DOC-4"/>
					<param name="EGUK04PF-NUMERO-DOC-4"/>
					<param name="EGUK04PF-DATA-RIL-DOC-4"/>
					<param name="EGUK04PF-PROV-RIL-DOC-4"/>
					<param name="EGUK04PF-COMUNE-RIL-DOC-4"/>
					<param name="EGUK04PF-STATO-RIL-DOC-4"/>
					<param name="EGUK04PF-ENTE-RILAS-DOC-4"/>
					<param name="EGUK04PF-DATA-SCA-DOC-4"/>
					<param name="EGUK04PF-DOC-OPERAZIONE-5"/>
					<param name="EGUK04PF-TIPO-DOC-5"/>
					<param name="EGUK04PF-NUMERO-DOC-5"/>
					<param name="EGUK04PF-DATA-RIL-DOC-5"/>
					<param name="EGUK04PF-PROV-RIL-DOC-5"/>
					<param name="EGUK04PF-COMUNE-RIL-DOC-5"/>
					<param name="EGUK04PF-STATO-RIL-DOC-5"/>
					<param name="EGUK04PF-ENTE-RILAS-DOC-5"/>
					<param name="EGUK04PF-DATA-SCA-DOC-5"/>
				</input>
				<output>
					<xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request">
						<xsl:output method="xml"/>
						<xsl:template match="/">
							<EGUKA004>
								  <ROCH-HEADER>
								    <ROCH-STRUCID>ROCH</ROCH-STRUCID>
								    <ROCH-VERSION>0002</ROCH-VERSION>
                                    <ROCH-BSNAME>RBS-XX-EGU-CENSIMENTO-ANAG</ROCH-BSNAME>
								    <ROCH-RETURNCODE>0000</ROCH-RETURNCODE>
								    <ROCH-UOWCONTROL>0000</ROCH-UOWCONTROL>
								    <ROCH-ABEND-CODE/>
								    <ROCH-AREA-FREE/>
								  </ROCH-HEADER>
								<EGUK04-SYS>
									<EGUK04-SYS-CO-TERMINALE/>
									<EGUK04-SYS-CO-OPERATORE/>
									<EGUK04-SYS-CO-FIL-OPER/>
									<EGUK04-SYS-CO-BANCA>00084</EGUK04-SYS-CO-BANCA>
									<EGUK04-SYS-FIL/>
									<EGUK04-SYS-CO-APPL>EG</EGUK04-SYS-CO-APPL>
									<EGUK04-SYS-SERVIZIO>AFA-CENSIMENTO-PF</EGUK04-SYS-SERVIZIO>
									<EGUK04-SYS-FILLER/>
								</EGUK04-SYS>
								<EGUK04-ERR>
									<EGUK04-ERR-RC/>
									<EGUK04-ERR-RC-DESC>0000</EGUK04-ERR-RC-DESC>
									<EGUK04-ERR-RC-DESC-ERRORE/>
									<EGUK04-ERR-CODICE-ABEND/>
									<EGUK04-ERR-SQLCODE>000000000</EGUK04-ERR-SQLCODE>
									<EGUK04-ERR-SQLERRMC/>
									<EGUK04-ERR-LABEL/>
									<EGUK04-ERR-TABEL/>
									<EGUK04-ERR-FUNZIONE/>
									<EGUK04-ERR-PGM/>
									<EGUK04-ERR-CAMPI/>
								</EGUK04-ERR>
								<EGUK04-SYS-LU-DATI>06659</EGUK04-SYS-LU-DATI>
								<EGUK04-DATI>
									<EGUK04-INPUT>
										<EGUK04-AREACENS>
											<EGUK04CE-STATUS-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-STATUS-AGG']/text()"/>
											</EGUK04CE-STATUS-AGG>
											<EGUK04CE-TELEFONI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TELEFONI-AGG']/text()"/>
											</EGUK04CE-TELEFONI-AGG>
											<EGUK04CE-INDIR-CORR-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-INDIR-CORR-AGG']/text()"/>
											</EGUK04CE-INDIR-CORR-AGG>
											<EGUK04CE-ALTRI-DATI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='UEGUK04CE-ALTRI-DATI-AGG']/text()"/>
											</EGUK04CE-ALTRI-DATI-AGG>
											<EGUK04CE-CODICI-VARI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CODICI-VARI-AGG']/text()"/>
											</EGUK04CE-CODICI-VARI-AGG>
											<EGUK04CE-SEDE-FISCALE-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-SEDE-FISCALE-AGG']/text()"/>
											</EGUK04CE-SEDE-FISCALE-AGG>
											<EGUK04CE-DATI-PRIVACY-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DATI-PRIVACY-AGG']/text()"/>
											</EGUK04CE-DATI-PRIVACY-AGG>
											<EGUK04CE-DATI-GENERALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DATI-GENERALI-AGG']/text()"/>
											</EGUK04CE-DATI-GENERALI-AGG>
											<EGUK04CE-DATI-AZIENDALI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DATI-AZIENDALI-AGG']/text()"/>
											</EGUK04CE-DATI-AZIENDALI-AGG>
											<EGUK04CE-PROFESSIONI-ALT-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PROFESSIONI-ALT-AGG']/text()"/>
											</EGUK04CE-PROFESSIONI-ALT-AGG>
											<EGUK04CE-INTESTAZIONE-LONG-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-INTESTAZIONE-LONG-AGG']/text()"/>
											</EGUK04CE-INTESTAZIONE-LONG-AGG>
											<EGUK04CE-CLASSIFICAZIONE-ALT-A>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CLASSIFICAZIONE-ALT-A']/text()"/>
											</EGUK04CE-CLASSIFICAZIONE-ALT-A>
											<EGUK04CE-FUNZIONE>I</EGUK04CE-FUNZIONE>
											<EGUK04CE-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NDG']/text()"/>
											</EGUK04CE-NDG>
											<EGUK04CE-CONTROLLO-INT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CONTROLLO-INT']/text()"/>
											</EGUK04CE-CONTROLLO-INT>
											<EGUK04CE-CODICE-FISCALE>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CODICE-FISCALE']/text()"/>
											</EGUK04CE-CODICE-FISCALE>
											<EGUK04CE-PARTITA-IVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PARTITA-IVA']/text()"/>
											</EGUK04CE-PARTITA-IVA>
											<EGUK04CE-SPORTELLO-CAPOFILA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-SPORTELLO-CAPOFILA']/text()"/>
											</EGUK04CE-SPORTELLO-CAPOFILA>
											<EGUK04CE-INTESTAZIONE-A>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-INTESTAZIONE-A']/text()"/>
											</EGUK04CE-INTESTAZIONE-A>
											<EGUK04CE-INTESTAZIONE-B>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-INTESTAZIONE-B']/text()"/>
											</EGUK04CE-INTESTAZIONE-B>
											<EGUK04CE-TIPO-NDG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-NDG']/text()"/>
											</EGUK04CE-TIPO-NDG>
											<EGUK04CE-SETTORISTA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-SETTORISTA']/text()"/>
											</EGUK04CE-SETTORISTA>
											<EGUK04CE-PSEUDONIMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PSEUDONIMO']/text()"/>
											</EGUK04CE-PSEUDONIMO>
											<EGUK04CE-STATO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-STATO-RES']/text()"/>
											</EGUK04CE-STATO-RES>
											<EGUK04CE-COMUNE-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-COMUNE-RES']/text()"/>
											</EGUK04CE-COMUNE-RES>
											<EGUK04CE-LOCALITA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-LOCALITA-RES']/text()"/>
											</EGUK04CE-LOCALITA-RES>
											<EGUK04CE-PROVINCIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PROVINCIA-RES']/text()"/>
											</EGUK04CE-PROVINCIA-RES>
											<EGUK04CE-CAP-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CAP-RES']/text()"/>
											</EGUK04CE-CAP-RES>
											<EGUK04CE-VIA-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-VIA-RES']/text()"/>
											</EGUK04CE-VIA-RES>
											<EGUK04CE-PRESSO-RES>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PRESSO-RES']/text()"/>
											</EGUK04CE-PRESSO-RES>
											<EGUK04CE-INTESTAZIONE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-INTESTAZIONE-CORR']/text()"/>
											</EGUK04CE-INTESTAZIONE-CORR>
											<EGUK04CE-STATO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-STATO-CORR']/text()"/>
											</EGUK04CE-STATO-CORR>
											<EGUK04CE-COMUNE-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-COMUNE-CORR']/text()"/>
											</EGUK04CE-COMUNE-CORR>
											<EGUK04CE-LOCALITA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-LOCALITA-CORR']/text()"/>
											</EGUK04CE-LOCALITA-CORR>
											<EGUK04CE-PROVINCIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PROVINCIA-CORR']/text()"/>
											</EGUK04CE-PROVINCIA-CORR>
											<EGUK04CE-CAP-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CAP-CORR']/text()"/>
											</EGUK04CE-CAP-CORR>
											<EGUK04CE-VIA-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-VIA-CORR']/text()"/>
											</EGUK04CE-VIA-CORR>
											<EGUK04CE-PRESSO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PRESSO-CORR']/text()"/>
											</EGUK04CE-PRESSO-CORR>
											<EGUK04CE-UFFICIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-UFFICIO-CORR']/text()"/>
											</EGUK04CE-UFFICIO-CORR>
											<EGUK04CE-CASELLARIO-CORR>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CASELLARIO-CORR']/text()"/>
											</EGUK04CE-CASELLARIO-CORR>
											<EGUK04CE-CODICE-FISCALE-ESTERO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CODICE-FISCALE-ESTERO']/text()"/>
											</EGUK04CE-CODICE-FISCALE-ESTERO>
											<EGUK04CE-RAMO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-RAMO']/text()"/>
											</EGUK04CE-RAMO>
											<EGUK04CE-SETTORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-SETTORE']/text()"/>
											</EGUK04CE-SETTORE>
											<EGUK04CE-RAMO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-RAMO-ALT']/text()"/>
											</EGUK04CE-RAMO-ALT>
											<EGUK04CE-SETTORE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-SETTORE-ALT']/text()"/>
											</EGUK04CE-SETTORE-ALT>
											<EGUK04CE-COMUNE-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-COMUNE-ALT']/text()"/>
											</EGUK04CE-COMUNE-ALT>
											<EGUK04CE-PROVINCIA-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-PROVINCIA-ALT']/text()"/>
											</EGUK04CE-PROVINCIA-ALT>
											<EGUK04CE-STATO-ALT>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-STATO-ALT']/text()"/>
											</EGUK04CE-STATO-ALT>
											<EGUK04CE-SEGMENTO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-SEGMENTO']/text()"/>
											</EGUK04CE-SEGMENTO>
											<EGUK04CE-SIT-GIURIDICA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-SIT-GIURIDICA']/text()"/>
											</EGUK04CE-SIT-GIURIDICA>
											<EGUK04CE-STATUS>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-STATUS']/text()"/>
											</EGUK04CE-STATUS>
											<EGUK04CE-UBIC-DOC-RIFERIM>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-UBIC-DOC-RIFERIM']/text()"/>
											</EGUK04CE-UBIC-DOC-RIFERIM>
											<EGUK04CE-UBIC-DOC-SPORTELLO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-UBIC-DOC-SPORTELLO']/text()"/>
											</EGUK04CE-UBIC-DOC-SPORTELLO>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-1']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-1']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-1']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-1']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-2']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-2']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-2']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-2']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-3']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-3']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-3']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-3']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONEC-4']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-4']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-4']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-4']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-5']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-5']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-5']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-5']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-6']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-6']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-6']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-6']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-7']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-7']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-7']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-7']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-8']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-8']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-8']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-8']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-9']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-9']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-9']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-9']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-TELEFONI>
												<EGUK04CE-TEL-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-OPERAZIONE-10']/text()"/>
												</EGUK04CE-TEL-OPERAZIONE>
												<EGUK04CE-TEL-PROGR>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TEL-PROGR-10']/text()"/>
												</EGUK04CE-TEL-PROGR>
												<EGUK04CE-TIPO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-TIPO-TELEFONO-10']/text()"/>
												</EGUK04CE-TIPO-TELEFONO>
												<EGUK04CE-NUMERO-TELEFONO>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-NUMERO-TELEFONO-10']/text()"/>
												</EGUK04CE-NUMERO-TELEFONO>
											</EGUK04CE-TELEFONI>
											<EGUK04CE-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CONSENSO-1']/text()"/>
											</EGUK04CE-CONSENSO-1>
											<EGUK04CE-DT-CONSENSO-1>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DT-CONSENSO-1']/text()"/>
											</EGUK04CE-DT-CONSENSO-1>
											<EGUK04CE-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CONSENSO-2']/text()"/>
											</EGUK04CE-CONSENSO-2>
											<EGUK04CE-DT-CONSENSO-2>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DT-CONSENSO-2']/text()"/>
											</EGUK04CE-DT-CONSENSO-2>
											<EGUK04CE-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CONSENSO-3']/text()"/>
											</EGUK04CE-CONSENSO-3>
											<EGUK04CE-DT-CONSENSO-3>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DT-CONSENSO-3']/text()"/>
											</EGUK04CE-DT-CONSENSO-3>
											<EGUK04CE-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CONSENSO-4']/text()"/>
											</EGUK04CE-CONSENSO-4>
											<EGUK04CE-DT-CONSENSO-4>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DT-CONSENSO-4']/text()"/>
											</EGUK04CE-DT-CONSENSO-4>
											<EGUK04CE-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CONSENSO-5']/text()"/>
											</EGUK04CE-CONSENSO-5>
											<EGUK04CE-DT-CONSENSO-5>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DT-CONSENSO-5']/text()"/>
											</EGUK04CE-DT-CONSENSO-5>
											<EGUK04CE-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-CONSENSO-6']/text()"/>
											</EGUK04CE-CONSENSO-6>
											<EGUK04CE-DT-CONSENSO-6>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-DT-CONSENSO-6']/text()"/>
											</EGUK04CE-DT-CONSENSO-6>
											<EGUK04CE-INVIO-INFORMATIVA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-INVIO-INFORMATIVA']/text()"/>
											</EGUK04CE-INVIO-INFORMATIVA>
											<EGUK04CE-ESENZ-FISC-DATA-EM>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-ESENZ-FISC-DATA-EM']/text()"/>
											</EGUK04CE-ESENZ-FISC-DATA-EM>
											<EGUK04CE-ESENZ-FISC-DATA-SCAD>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-ESENZ-FISC-DATA-SCAD']/text()"/>
											</EGUK04CE-ESENZ-FISC-DATA-SCAD>
											<EGUK04CE-ESENZ-FISC-TIPO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-ESENZ-FISC-TIPO']/text()"/>
											</EGUK04CE-ESENZ-FISC-TIPO>
											<EGUK04CE-OPERATORE>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04CE-OPERATORE']/text()"/>
											</EGUK04CE-OPERATORE>
											<FILLER1/>
										</EGUK04-AREACENS>
										<EGUK04-AREACEPF>
											<EGUK04PF-DOCUMENTI-AGG>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DOCUMENTI-AGG']/text()"/>
											</EGUK04PF-DOCUMENTI-AGG>
											<EGUK04PF-PREF-COGN-ACQUIS>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PREF-COGN-ACQUIS']/text()"/>
											</EGUK04PF-PREF-COGN-ACQUIS>
											<EGUK04PF-COGNOME-ACQUISITO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-COGNOME-ACQUISITO']/text()"/>
											</EGUK04PF-COGNOME-ACQUISITO>
											<EGUK04PF-TITOLI>
												<xsl:value-of select="xgen:request/xgen:param[@name='UEGUK04PF-TITOLI']/text()"/>
											</EGUK04PF-TITOLI>
											<EGUK04PF-SESSO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-SESSO']/text()"/>
											</EGUK04PF-SESSO>
											<EGUK04PF-DATA-NASCITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-NASCITA']/text()"/>
											</EGUK04PF-DATA-NASCITA>
											<EGUK04PF-COMUNE-NASCITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-COMUNE-NASCITA']/text()"/>
											</EGUK04PF-COMUNE-NASCITA>
											<EGUK04PF-PROVINCIA-NASC>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROVINCIA-NASC']/text()"/>
											</EGUK04PF-PROVINCIA-NASC>
											<EGUK04PF-STATO-NASCITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-STATO-NASCITA']/text()"/>
											</EGUK04PF-STATO-NASCITA>
											<EGUK04PF-DATA-DECESSO>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-DECESSO']/text()"/>
											</EGUK04PF-DATA-DECESSO>
											<EGUK04PF-EREDITA>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-EREDITA']/text()"/>
											</EGUK04PF-EREDITA>
											<EGUK04PF-PROFESSIONE>
												<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROFESSIONE']/text()"/>
											</EGUK04PF-PROFESSIONE>
											<EGUK04PF-PROFESSIONE-ALT>
												<EGUK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROF-ALT-1']/text()"/>
												</EGUK04PF-PROF-ALT>
											</EGUK04PF-PROFESSIONE-ALT>
											<EGUK04PF-PROFESSIONE-ALT>
												<EGUK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROF-ALT-2']/text()"/>
												</EGUK04PF-PROF-ALT>
											</EGUK04PF-PROFESSIONE-ALT>
											<EGUK04PF-PROFESSIONE-ALT>
												<EGUK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROF-ALT-3']/text()"/>
												</EGUK04PF-PROF-ALT>
											</EGUK04PF-PROFESSIONE-ALT>
											<EGUK04PF-PROFESSIONE-ALT>
												<EGUK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROF-ALT-4']/text()"/>
												</EGUK04PF-PROF-ALT>
											</EGUK04PF-PROFESSIONE-ALT>
											<EGUK04PF-PROFESSIONE-ALT>
												<EGUK04PF-PROF-ALT>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROF-ALT-5']/text()"/>
												</EGUK04PF-PROF-ALT>
											</EGUK04PF-PROFESSIONE-ALT>
											<EGUK04PF-DOCUMENTI>
												<EGUK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DOC-OPERAZIONE-1']/text()"/>
												</EGUK04PF-DOC-OPERAZIONE>
												<EGUK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-TIPO-DOC-1']/text()"/>
												</EGUK04PF-TIPO-DOC>
												<EGUK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-NUMERO-DOC-1']/text()"/>
												</EGUK04PF-NUMERO-DOC>
												<EGUK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-RIL-DOC-1']/text()"/>
												</EGUK04PF-DATA-RIL-DOC>
												<EGUK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROV-RIL-DOC-1']/text()"/>
												</EGUK04PF-PROV-RIL-DOC>
												<EGUK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-COMUNE-RIL-DOC-1']/text()"/>
												</EGUK04PF-COMUNE-RIL-DOC>
												<EGUK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-STATO-RIL-DOC-1']/text()"/>
												</EGUK04PF-STATO-RIL-DOC>
												<EGUK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-ENTE-RILAS-DOC-1']/text()"/>
												</EGUK04PF-ENTE-RILAS-DOC>
												<EGUK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-SCA-DOC-1']/text()"/>
												</EGUK04PF-DATA-SCA-DOC>
											</EGUK04PF-DOCUMENTI>
											<EGUK04PF-DOCUMENTI>
												<EGUK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DOC-OPERAZIONE-2']/text()"/>
												</EGUK04PF-DOC-OPERAZIONE>
												<EGUK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-TIPO-DOC-2']/text()"/>
												</EGUK04PF-TIPO-DOC>
												<EGUK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-NUMERO-DOC-2']/text()"/>
												</EGUK04PF-NUMERO-DOC>
												<EGUK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-RIL-DOC-2']/text()"/>
												</EGUK04PF-DATA-RIL-DOC>
												<EGUK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROV-RIL-DOC-2']/text()"/>
												</EGUK04PF-PROV-RIL-DOC>
												<EGUK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-COMUNE-RIL-DOC-2']/text()"/>
												</EGUK04PF-COMUNE-RIL-DOC>
												<EGUK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-STATO-RIL-DOC-2']/text()"/>
												</EGUK04PF-STATO-RIL-DOC>
												<EGUK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-ENTE-RILAS-DOC-2']/text()"/>
												</EGUK04PF-ENTE-RILAS-DOC>
												<EGUK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-SCA-DOC-2']/text()"/>
												</EGUK04PF-DATA-SCA-DOC>
											</EGUK04PF-DOCUMENTI>
											<EGUK04PF-DOCUMENTI>
												<EGUK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DOC-OPERAZIONE-3']/text()"/>
												</EGUK04PF-DOC-OPERAZIONE>
												<EGUK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-TIPO-DOC-3']/text()"/>
												</EGUK04PF-TIPO-DOC>
												<EGUK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-NUMERO-DOC-3']/text()"/>
												</EGUK04PF-NUMERO-DOC>
												<EGUK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-RIL-DOC-3']/text()"/>
												</EGUK04PF-DATA-RIL-DOC>
												<EGUK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROV-RIL-DOC-3']/text()"/>
												</EGUK04PF-PROV-RIL-DOC>
												<EGUK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-COMUNE-RIL-DOC-3']/text()"/>
												</EGUK04PF-COMUNE-RIL-DOC>
												<EGUK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-STATO-RIL-DOC-3']/text()"/>
												</EGUK04PF-STATO-RIL-DOC>
												<EGUK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-ENTE-RILAS-DOC-3']/text()"/>
												</EGUK04PF-ENTE-RILAS-DOC>
												<EGUK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-SCA-DOC-3']/text()"/>
												</EGUK04PF-DATA-SCA-DOC>
											</EGUK04PF-DOCUMENTI>
											<EGUK04PF-DOCUMENTI>
												<EGUK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DOC-OPERAZIONE-4']/text()"/>
												</EGUK04PF-DOC-OPERAZIONE>
												<EGUK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-TIPO-DOC-4']/text()"/>
												</EGUK04PF-TIPO-DOC>
												<EGUK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-NUMERO-DOC-4']/text()"/>
												</EGUK04PF-NUMERO-DOC>
												<EGUK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-RIL-DOC-4']/text()"/>
												</EGUK04PF-DATA-RIL-DOC>
												<EGUK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROV-RIL-DOC-4']/text()"/>
												</EGUK04PF-PROV-RIL-DOC>
												<EGUK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-COMUNE-RIL-DOC-4']/text()"/>
												</EGUK04PF-COMUNE-RIL-DOC>
												<EGUK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-STATO-RIL-DOC-4']/text()"/>
												</EGUK04PF-STATO-RIL-DOC>
												<EGUK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-ENTE-RILAS-DOC-4']/text()"/>
												</EGUK04PF-ENTE-RILAS-DOC>
												<EGUK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-SCA-DOC-4']/text()"/>
												</EGUK04PF-DATA-SCA-DOC>
											</EGUK04PF-DOCUMENTI>
											<EGUK04PF-DOCUMENTI>
												<EGUK04PF-DOC-OPERAZIONE>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DOC-OPERAZIONE-5']/text()"/>
												</EGUK04PF-DOC-OPERAZIONE>
												<EGUK04PF-TIPO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-TIPO-DOC-5']/text()"/>
												</EGUK04PF-TIPO-DOC>
												<EGUK04PF-NUMERO-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-NUMERO-DOC-5']/text()"/>
												</EGUK04PF-NUMERO-DOC>
												<EGUK04PF-DATA-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-RIL-DOC-5']/text()"/>
												</EGUK04PF-DATA-RIL-DOC>
												<EGUK04PF-PROV-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-PROV-RIL-DOC-5']/text()"/>
												</EGUK04PF-PROV-RIL-DOC>
												<EGUK04PF-COMUNE-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-COMUNE-RIL-DOC-5']/text()"/>
												</EGUK04PF-COMUNE-RIL-DOC>
												<EGUK04PF-STATO-RIL-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-STATO-RIL-DOC-5']/text()"/>
												</EGUK04PF-STATO-RIL-DOC>
												<EGUK04PF-ENTE-RILAS-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-ENTE-RILAS-DOC-5']/text()"/>
												</EGUK04PF-ENTE-RILAS-DOC>
												<EGUK04PF-DATA-SCA-DOC>
													<xsl:value-of select="xgen:request/xgen:param[@name='EGUK04PF-DATA-SCA-DOC-5']/text()"/>
												</EGUK04PF-DATA-SCA-DOC>
											</EGUK04PF-DOCUMENTI>
											<FILLER1/>
										</EGUK04-AREACEPF>
									</EGUK04-INPUT>
									<EGUK04-OUTPUT>
										<EGUK04OU-NDG/>
										<EGUK04OU-CONTROLLO-INT/>
										<EGUK04OU-CODICE-FISCALE/>
										<EGUK04OU-PARTITA-IVA/>
										<EGUK04OU-SPORTELLO-CAPOFILA/>
										<EGUK04OU-INTESTAZIONE-A/>
										<EGUK04OU-INTESTAZIONE-B/>
										<EGUK04OU-TIPO-NDG/>
										<EGUK04OU-SETTORISTA/>
										<EGUK04OU-PSEUDONIMO/>
										<EGUK04OU-STATO-RES/>
										<EGUK04OU-DESCR-COM-RES/>
										<EGUK04OU-CAB-RES>000000</EGUK04OU-CAB-RES>
										<EGUK04OU-LOCALITA-RES/>
										<EGUK04OU-PROVINCIA-RES/>
										<EGUK04OU-CAP-RES>00000</EGUK04OU-CAP-RES>
										<EGUK04OU-VIA-RES/>
										<EGUK04OU-PRESSO/>
										<EGUK04OU-SETTORE>000</EGUK04OU-SETTORE>
										<EGUK04OU-RAMO>000</EGUK04OU-RAMO>
										<EGUK04OU-PROFESSIONE/>
										<EGUK04OU-INTESTAZ-CORR/>
										<EGUK04OU-STATO-CORR/>
										<EGUK04OU-DESCR-COM-CORR/>
										<EGUK04OU-LOCALITA-CORR/>
										<EGUK04OU-PROVINCIA-CORR/>
										<EGUK04OU-CAP-CORR>000</EGUK04OU-CAP-CORR>
										<EGUK04OU-VIA-CORR/>
										<EGUK04OU-PRESSO-CORR/>
										<EGUK04OU-UFFICIO-CORR/>
										<EGUK04OU-CASELLARIO-CORR/>
										<EGUK04OU-STATUS-TAB>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
											<EGUK04OU-STATUS/>
										</EGUK04OU-STATUS-TAB>
										<EGUK04OU-COMPLETEZZA-DATI/>
										<EGUK04OU-STATO-NDG/>
										<EGUK04OU-DATA-CENSIMENTO/>
										<EGUK04OU-DATA-VA/>
										<EGUK04OU-SPORTELLO-ESEC/>
										<EGUK04OU-OPERATORE/>
										<EGUK04OU-INTESTAZIONE-AGG/>
										<EGUK04OU-COD-CR>0000000000000</EGUK04OU-COD-CR>
										<EGUK04OU-PREF-COGN-ACQUIS/>
										<EGUK04OU-COGNOME-ACQUISITO/>
										<EGUK04OU-TITOLI/>
										<EGUK04OU-SESSO/>
										<EGUK04OU-DATA-NASCITA/>
										<EGUK04OU-COMUNE-NASCITA/>
										<EGUK04OU-PROVINCIA-NASC/>
										<EGUK04OU-DATA-COSTITUZI/>
										<EGUK04OU-NUMERO-CCIAA>00000000</EGUK04OU-NUMERO-CCIAA>
										<EGUK04OU-PROV-CCIAA/>
										<EGUK04OU-DATA-ISCR-CCIA/>
										<EGUK04OU-NUMERO-AIA>00000000</EGUK04OU-NUMERO-AIA>
										<EGUK04OU-PROV-AIA/>
										<EGUK04OU-CODICE-ABI>00000</EGUK04OU-CODICE-ABI>
										<EGUK04OU-COD-CONTROLLO>0</EGUK04OU-COD-CONTROLLO>
										<EGUK04OU-MINCOMES/>
										<EGUK04OU-NUM-REG-TRIB>000000000</EGUK04OU-NUM-REG-TRIB>
										<EGUK04OU-SEDE-TRIBUNALE/>
										<EGUK04OU-CODICE-SWIFT/>
										<EGUK04OU-COD-OPERAT-EST/>
										<EGUK04OU-STATO-SEDE-F/>
										<EGUK04OU-LOCALITA-SEDE-F/>
										<EGUK04OU-DESCR-COM-SEDE-F/>
										<EGUK04OU-PROVINCIA-SEDE-F/>
										<EGUK04OU-CAP-SEDE-F>00000</EGUK04OU-CAP-SEDE-F>
										<EGUK04OU-VIA-SEDE-F/>
										<EGUK04OU-PRESSO-SEDE-F/>
										<EGUK04OU-DATA-RIF-ADA/>
										<EGUK04OU-CLASSE-DIMENSION>00</EGUK04OU-CLASSE-DIMENSION>
										<EGUK04OU-FATTURATO>0000000</EGUK04OU-FATTURATO>
										<EGUK04OU-CAPITALE-SOCIALE>0000000</EGUK04OU-CAPITALE-SOCIALE>
										<EGUK04OU-NUMERO-DIPENDENTI>000000</EGUK04OU-NUMERO-DIPENDENTI>
										<EGUK04OU-SETTORE-ALT>000</EGUK04OU-SETTORE-ALT>
										<EGUK04OU-RAMO-ALT>000</EGUK04OU-RAMO-ALT>
										<EGUK04OU-STATO-ALT/>
										<EGUK04OU-COMUNE-ALT/>
										<EGUK04OU-PROVINCIA-ALT/>
										<EGUK04OU-CAB-ALT>000000</EGUK04OU-CAB-ALT>
										<EGUK04OU-CONSENSO-1/>
										<EGUK04OU-DT-CONSENSO-1/>
										<EGUK04OU-CONSENSO-2/>
										<EGUK04OU-DT-CONSENSO-2/>
										<EGUK04OU-CONSENSO-3/>
										<EGUK04OU-DT-CONSENSO-3/>
										<EGUK04OU-CONSENSO-4/>
										<EGUK04OU-DT-CONSENSO-4/>
										<EGUK04OU-CONSENSO-5/>
										<EGUK04OU-DT-CONSENSO-5/>
										<EGUK04OU-CONSENSO-6/>
										<EGUK04OU-DT-CONSENSO-6/>
										<EGUK04OU-INVIO-INFORMATIVA/>
										<EGUK04OU-PROF-ATTIVITA/>
										<EGUK04OU-PROF-ATTIVITA/>
										<EGUK04OU-PROF-ATTIVITA/>
										<EGUK04OU-PROF-ATTIVITA/>
										<EGUK04OU-PROF-ATTIVITA/>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-TELEFONI>
											<EGUK04OU-TEL-PROGR>00</EGUK04OU-TEL-PROGR>
											<EGUK04OU-TIPO-TELEFONO/>
											<EGUK04OU-NUMERO-TELEFONO/>
										</EGUK04OU-TELEFONI>
										<EGUK04OU-DOCUMENTI>
											<EGUK04OU-TIPO-DOCUM/>
											<EGUK04OU-NUM-DOCUM/>
											<EGUK04OU-DATA-RIL-DOCUM/>
											<EGUK04OU-PROV-RIL-DOCUM/>
											<EGUK04OU-COMUNE-RIL-DOCUM/>
											<EGUK04OU-CAB-RIL-DOCUM>000000</EGUK04OU-CAB-RIL-DOCUM>
											<EGUK04OU-STATO-RIL-DOCUM/>
											<EGUK04OU-ENTE-RILAS-DOCUM/>
											<EGUK04OU-DATA-SCA-DOCUM/>
										</EGUK04OU-DOCUMENTI>
										<EGUK04OU-DOCUMENTI>
											<EGUK04OU-TIPO-DOCUM/>
											<EGUK04OU-NUM-DOCUM/>
											<EGUK04OU-DATA-RIL-DOCUM/>
											<EGUK04OU-PROV-RIL-DOCUM/>
											<EGUK04OU-COMUNE-RIL-DOCUM/>
											<EGUK04OU-CAB-RIL-DOCUM>000000</EGUK04OU-CAB-RIL-DOCUM>
											<EGUK04OU-STATO-RIL-DOCUM/>
											<EGUK04OU-ENTE-RILAS-DOCUM/>
											<EGUK04OU-DATA-SCA-DOCUM/>
										</EGUK04OU-DOCUMENTI>
										<EGUK04OU-DOCUMENTI>
											<EGUK04OU-TIPO-DOCUM/>
											<EGUK04OU-NUM-DOCUM/>
											<EGUK04OU-DATA-RIL-DOCUM/>
											<EGUK04OU-PROV-RIL-DOCUM/>
											<EGUK04OU-COMUNE-RIL-DOCUM/>
											<EGUK04OU-CAB-RIL-DOCUM>000000</EGUK04OU-CAB-RIL-DOCUM>
											<EGUK04OU-STATO-RIL-DOCUM/>
											<EGUK04OU-ENTE-RILAS-DOCUM/>
											<EGUK04OU-DATA-SCA-DOCUM/>
										</EGUK04OU-DOCUMENTI>
										<EGUK04OU-DOCUMENTI>
											<EGUK04OU-TIPO-DOCUM/>
											<EGUK04OU-NUM-DOCUM/>
											<EGUK04OU-DATA-RIL-DOCUM/>
											<EGUK04OU-PROV-RIL-DOCUM/>
											<EGUK04OU-COMUNE-RIL-DOCUM/>
											<EGUK04OU-CAB-RIL-DOCUM>000000</EGUK04OU-CAB-RIL-DOCUM>
											<EGUK04OU-STATO-RIL-DOCUM/>
											<EGUK04OU-ENTE-RILAS-DOCUM/>
											<EGUK04OU-DATA-SCA-DOCUM/>
										</EGUK04OU-DOCUMENTI>
										<EGUK04OU-DOCUMENTI>
											<EGUK04OU-TIPO-DOCUM/>
											<EGUK04OU-NUM-DOCUM/>
											<EGUK04OU-DATA-RIL-DOCUM/>
											<EGUK04OU-PROV-RIL-DOCUM/>
											<EGUK04OU-COMUNE-RIL-DOCUM/>
											<EGUK04OU-CAB-RIL-DOCUM>000000</EGUK04OU-CAB-RIL-DOCUM>
											<EGUK04OU-STATO-RIL-DOCUM/>
											<EGUK04OU-ENTE-RILAS-DOCUM/>
											<EGUK04OU-DATA-SCA-DOCUM/>
										</EGUK04OU-DOCUMENTI>
										<EGUK04OU-SEGMENTO/>
										<EGUK04OU-SIT-GIURIDICA/>
										<EGUK04OU-EREDITA/>
										<EGUK04OU-DATA-EREDITA/>
										<EGUK04OU-SEDE-CASAMADRE>000000</EGUK04OU-SEDE-CASAMADRE>
										<EGUK04OU-DATA-ESTINZIONE/>
										<EGUK04OU-SPORTELLO-RIF/>
										<EGUK04OU-RIFERIMENTO/>
										<EGUK04OU-FILLER/>
									</EGUK04-OUTPUT>
								</EGUK04-DATI>
							</EGUKA004>
						</xsl:template>
					</xsl:stylesheet>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.RomaProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass/>
		<channelclass>it.usi.webfactory.channels.RomaBusinnesChannel</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params/>
	</protocol>
	<channel>
		<params>
			<client>Client_EGU_XX</client>
			<service>RBS-XX-EGU-CENSIMENTO-ANAG</service>
			<format>EGUKA004-XML</format>
			<timeout>30000</timeout>
		</params>
	</channel>
</service>
