<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="npxFidi_Logon">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="npxFidi_LogonResponse">
    <complexType>
     <sequence>
      <element name="npxFidi_LogonReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="npxFidi_LogonResponse">

      <wsdl:part element="impl:npxFidi_LogonResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="npxFidi_LogonRequest">

      <wsdl:part element="impl:npxFidi_Logon" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPX_Fidi_Logon_SEI">

      <wsdl:operation name="npxFidi_Logon">

         <wsdl:input message="impl:npxFidi_LogonRequest" name="npxFidi_LogonRequest"/>

         <wsdl:output message="impl:npxFidi_LogonResponse" name="npxFidi_LogonResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPX_Fidi_LogonSoapBinding" type="impl:NPX_Fidi_Logon_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="npxFidi_Logon">

         <wsdlsoap:operation soapAction="npxFidi_Logon"/>

         <wsdl:input name="npxFidi_LogonRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="npxFidi_LogonResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPX_Fidi_LogonService">

      <wsdl:port binding="impl:NPX_Fidi_LogonSoapBinding" name="NPX_Fidi_Logon">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPX_Fidi_Logon"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
