/*
 * Created on Apr 10, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 * vo che mantiene le banche censite per le procedure e/o progetti e/o estrazioni
 */
public class BanksList implements Serializable {
	
	private ArrayList banks;
	private Map hashTowers;
		
	public BanksList() {
		this.clear();
	}
	
	public void clear() {
		banks = new ArrayList(0);
		hashTowers = new HashMap();
	}

	/**
	 * @return
	 */
	public ArrayList getBanks() {
		return banks;
	}
		
	public BankData[] getBanksList() {
		return (BankData[]) banks.toArray(new BankData[0]);
	}
	
	public BankData getBankFromTowerKey(String key) {
		
		if (!hashTowers.containsKey(key))
			return null;
		
		return (BankData) hashTowers.get(key);
	}
	
	/**
	 * @param list
	 */
	public void setBanks(ArrayList list) {
		banks = list;

		for (int i=0; i<list.size(); i++) {
			BankData obj = (BankData) list.get(i);
			hashTowers.put(obj.getTowerCode(), obj);
		}
	}
}
