package it.usi.xframe.gwc.wsutil;

public interface NPR_SEM_Zivno_SEI extends java.rmi.Remote
{
 public java.lang.String nprSemZivno(java.lang.String n001,java.lang.String n002,java.lang.String x01,java.lang.String n003,java.lang.String x02,java.lang.String n004,java.lang.String x03,java.lang.String n005,java.lang.String x04,java.lang.String n006,java.lang.String x05,java.lang.String n007,java.lang.String x06,java.lang.String n008,java.lang.String x07,java.lang.String n009,java.lang.String x08,java.lang.String n010,java.lang.String x09,java.lang.String n011,java.lang.String x10,java.lang.String n012,java.lang.String x11,java.lang.String n013,java.lang.String x12,java.lang.String n014,java.lang.String x13,java.lang.String n015,java.lang.String n016,java.lang.String n017,java.lang.String x14,java.lang.String x15,java.lang.String x16,java.lang.String x17,java.lang.String n018,java.lang.String n019,java.lang.String n020,java.lang.String n021,java.lang.String n022,java.lang.String x18,java.lang.String x19,java.lang.String n023,java.lang.String x20,java.lang.String x21,java.lang.String n024,java.lang.String x22,java.lang.String x23,java.lang.String n025,java.lang.String x24,java.lang.String n026,java.lang.String n027,java.lang.String n028,java.lang.String x25,java.lang.String x26,java.lang.String x27,java.lang.String x28,java.lang.String x29,java.lang.String x30,java.lang.String n029,java.lang.String x31,java.lang.String n030) throws java.lang.Exception;
}