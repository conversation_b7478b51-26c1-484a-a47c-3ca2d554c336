<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R73I-PROPOSAL-ID"/>
					<param name="R73I-FUNCTION"/>
					<param name="R73I-USER-ID"/>
					<param name="R73I-DEP-ST-FUND-MISS"/>
					<param name="R73I-DEP-ST-FUND-M"/>
					<param name="R73I-DEP-ST-FUND-C"/>
					<param name="R73I-DEP-ST-FUND-CM"/>
					<param name="R73I-OVERHEADS-MISS"/>
					<param name="R73I-OVERHEADS-M"/>
					<param name="R73I-OVERHEADS-C"/>
					<param name="R73I-OVERHEADS-CM"/>
					<param name="R73I-GS-TOT-ASSETS-MISS"/>
					<param name="R73I-GS-TOT-ASSETS-M"/>
					<param name="R73I-GS-TOT-ASSETS-C"/>
					<param name="R73I-GS-TOT-ASSETS-CM"/>
					<param name="R73I-GS-TOT-ASSETS-T1-MISS"/>
					<param name="R73I-GS-TOT-ASSETS-T1-M"/>
					<param name="R73I-GS-TOT-ASSETS-T1-C"/>
					<param name="R73I-GS-TOT-ASSETS-T1-CM"/>
					<param name="R73I-INT-EXPENSE-MISS"/>
					<param name="R73I-INT-EXPENSE-M"/>
					<param name="R73I-INT-EXPENSE-C"/>
					<param name="R73I-INT-EXPENSE-CM"/>
					<param name="R73I-TOT-LIABILITIES-MISS"/>
					<param name="R73I-TOT-LIABILITIES-M"/>
					<param name="R73I-TOT-LIABILITIES-C"/>
					<param name="R73I-TOT-LIABILITIES-CM"/>
					<param name="R73I-TOT-LIABILITIES-T1-MISS"/>
					<param name="R73I-TOT-LIABILITIES-T1-M"/>
					<param name="R73I-TOT-LIABILITIES-T1-C"/>
					<param name="R73I-TOT-LIABILITIES-T1-CM"/>
					<param name="R73I-NET-INCOME-MISS"/>
					<param name="R73I-NET-INCOME-M"/>
					<param name="R73I-NET-INCOME-C"/>
					<param name="R73I-NET-INCOME-CM"/>
					<param name="R73I-GS-NET-INT-REV-MISS"/>
					<param name="R73I-GS-NET-INT-REV-M"/>
					<param name="R73I-GS-NET-INT-REV-C"/>
					<param name="R73I-GS-NET-INT-REV-CM"/>
					<param name="R73I-IMPAIRED-LOANS-MISS"/>
					<param name="R73I-IMPAIRED-LOANS-M"/>
					<param name="R73I-IMPAIRED-LOANS-C"/>
					<param name="R73I-IMPAIRED-LOANS-CM"/>
					<param name="R73I-LOANS-LOSS-RESM-MISS"/>
					<param name="R73I-LOANS-LOSS-RESM-M"/>
					<param name="R73I-LOANS-LOSS-RESM-C"/>
					<param name="R73I-LOANS-LOSS-RESM-CM"/>
					<param name="R73I-LOANS-LOSS-RES-MISS"/>
					<param name="R73I-LOANS-LOSS-RES-M"/>
					<param name="R73I-LOANS-LOSS-RES-C"/>
					<param name="R73I-LOANS-LOSS-RES-CM"/>
					<param name="R73I-LOANS-LOSS-PROV-MISS"/>
					<param name="R73I-LOANS-LOSS-PROV-M"/>
					<param name="R73I-LOANS-LOSS-PROV-C"/>
					<param name="R73I-LOANS-LOSS-PROV-CM"/>
					<param name="R73I-TOT-LOANS-MISS"/>
					<param name="R73I-TOT-LOANS-M"/>
					<param name="R73I-TOT-LOANS-C"/>
					<param name="R73I-TOT-LOANS-CM"/>
					<param name="R73I-TOT-LOANS-T1-MISS"/>
					<param name="R73I-TOT-LOANS-T1-M"/>
					<param name="R73I-TOT-LOANS-T1-C"/>
					<param name="R73I-TOT-LOANS-T1-CM"/>
					<param name="R73I-EQUITY-MISS"/>
					<param name="R73I-EQUITY-M"/>
					<param name="R73I-EQUITY-C"/>
					<param name="R73I-EQUITY-CM"/>
					<param name="R73I-GOODWILL-MISS"/>
					<param name="R73I-GOODWILL-M"/>
					<param name="R73I-GOODWILL-C"/>
					<param name="R73I-GOODWILL-CM"/>
					<param name="R73I-TOT-ASSETS-MISS"/>
					<param name="R73I-TOT-ASSETS-M"/>
					<param name="R73I-TOT-ASSETS-C"/>
					<param name="R73I-TOT-ASSETS-CM"/>
					<param name="R73I-DEP-W-BANK-MISS"/>
					<param name="R73I-DEP-W-BANK-M"/>
					<param name="R73I-DEP-W-BANK-C"/>
					<param name="R73I-DEP-W-BANK-CM"/>
					<param name="R73I-DUE-CBANK-MISS"/>
					<param name="R73I-DUE-CBANK-M"/>
					<param name="R73I-DUE-CBANK-C"/>
					<param name="R73I-DUE-CBANK-CM"/>
					<param name="R73I-DUE-OBANK-MISS"/>
					<param name="R73I-DUE-OBANK-M"/>
					<param name="R73I-DUE-OBANK-C"/>
					<param name="R73I-DUE-OBANK-CM"/>
					<param name="R73I-DUE-OCRINST-MISS"/>
					<param name="R73I-DUE-OCRINST-M"/>
					<param name="R73I-DUE-OCRINST-C"/>
					<param name="R73I-DUE-OCRINST-CM"/>
					<param name="R73I-TREASURY-BILLS-MISS"/>
					<param name="R73I-TREASURY-BILLS-M"/>
					<param name="R73I-TREASURY-BILLS-C"/>
					<param name="R73I-TREASURY-BILLS-CM"/>
					<param name="R73I-CASH-DUE-BANK-MISS"/>
					<param name="R73I-CASH-DUE-BANK-M"/>
					<param name="R73I-CASH-DUE-BANK-C"/>
					<param name="R73I-CASH-DUE-BANK-CM"/>
					<param name="R73I-GS-OTHER-OP-INC-MISS"/>
					<param name="R73I-GS-OTHER-OP-INC-M"/>
					<param name="R73I-GS-OTHER-OP-INC-C"/>
					<param name="R73I-GS-OTHER-OP-INC-CM"/>
					<param name="R73I-TOT-CUST-LOANS-MISS"/>
					<param name="R73I-TOT-CUST-LOANS-M"/>
					<param name="R73I-TOT-CUST-LOANS-C"/>
					<param name="R73I-TOT-CUST-LOANS-CM"/>
					<param name="R73I-DTQ1L08-A"/>
					<param name="R73I-DTQ1L08-M"/>
					<param name="R73I-DTQ1L07-A"/>
					<param name="R73I-DTQ1L07-M"/>
					<param name="R73I-DTQ1L07-NOTE"/>
					<param name="R73I-L-LAOSTFA-C"/>
					<param name="R73I-L-LAOSTFA-CM"/>
					<param name="R73I-P-MOHOAA-C"/>
					<param name="R73I-P-MOHOAA-CM"/>
					<param name="R73I-P-MIXOALI-C"/>
					<param name="R73I-P-MIXOALI-CM"/>
					<param name="R73I-P-NIOTOI-C"/>
					<param name="R73I-P-NIOTOI-CM"/>
					<param name="R73I-R-MLLOL-C"/>
					<param name="R73I-R-MLLOL-CM"/>
					<param name="R73I-R-MLLPOAL-C"/>
					<param name="R73I-R-MLLPOAL-CM"/>
					<param name="R73I-R-LGROWTH-C"/>
					<param name="R73I-R-LGROWTH-CM"/>
					<param name="R73I-S-A-C"/>
					<param name="R73I-S-A-CM"/>
					<param name="R73I-C-TCEOA-C"/>
					<param name="R73I-C-TCEOA-CM"/>
					<param name="R73I-L-LAOSTFC-C"/>
					<param name="R73I-L-LAOSTFC-CM"/>
					<param name="R73I-P-MIXOLIA-C"/>
					<param name="R73I-P-MIXOLIA-CM"/>
					<param name="R73I-R-MLLROCL-C"/>
					<param name="R73I-R-MLLROCL-CM"/>
					<param name="R73I-R-LGROWTH15-C"/>
					<param name="R73I-R-LGROWTH15-CM"/>
					<param name="R73I-S-L-C"/>
					<param name="R73I-S-L-CM"/>
					<param name="R73I-LIQUID-ASS-MISS"/>
					<param name="R73I-LIQUID-ASS-M"/>
					<param name="R73I-LIQUID-ASS-C"/>
					<param name="R73I-LIQUID-ASS-CM"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB73-INPUT</hostService>
            <applBankNumber>99</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0001</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB73</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>