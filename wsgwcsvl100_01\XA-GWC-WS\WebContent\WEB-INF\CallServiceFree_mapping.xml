<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411998546938">
      <package-mapping id="PackageMapping_1411998546938">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411998546938">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411998546938">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411998546938">
         <service-interface>it.usi.xframe.gwc.wsutil.CallServiceFreeService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411998546938">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceFreeService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411998546938">
            <port-name>CallServiceFree</port-name>
            <java-port-name>CallServiceFree</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411998546938">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallServiceFree_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411998546938">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceFree_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411998546938">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceFreeSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411998546938">
            <java-method-name>callServiceFree</java-method-name>
            <wsdl-operation>callServiceFree</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998546938">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998546938">
                  <wsdl-message id="WSDLMessage_1411998546938">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>service</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998546939">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998546939">
                  <wsdl-message id="WSDLMessage_1411998546939">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>params</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998546940">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998546940">
                  <wsdl-message id="WSDLMessage_1411998546940">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep1</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998546941">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998546941">
                  <wsdl-message id="WSDLMessage_1411998546941">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>callServiceFreeRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>sep2</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411998546938">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411998546942">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>callServiceFreeResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>callServiceFreeReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
