<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="npxFidi_AperturaProposta">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
      <element name="x06" nillable="true" type="xsd:string"/>
      <element name="x08" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="npxFidi_AperturaPropostaResponse">
    <complexType>
     <sequence>
      <element name="npxFidi_AperturaPropostaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="npxFidi_AperturaPropostaRequest">

      <wsdl:part element="impl:npxFidi_AperturaProposta" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="npxFidi_AperturaPropostaResponse">

      <wsdl:part element="impl:npxFidi_AperturaPropostaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPX_Fidi_AperturaProposta_SEI">

      <wsdl:operation name="npxFidi_AperturaProposta">

         <wsdl:input message="impl:npxFidi_AperturaPropostaRequest" name="npxFidi_AperturaPropostaRequest"/>

         <wsdl:output message="impl:npxFidi_AperturaPropostaResponse" name="npxFidi_AperturaPropostaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPX_Fidi_AperturaPropostaSoapBinding" type="impl:NPX_Fidi_AperturaProposta_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="npxFidi_AperturaProposta">

         <wsdlsoap:operation soapAction="npxFidi_AperturaProposta"/>

         <wsdl:input name="npxFidi_AperturaPropostaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="npxFidi_AperturaPropostaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPX_Fidi_AperturaPropostaService">

      <wsdl:port binding="impl:NPX_Fidi_AperturaPropostaSoapBinding" name="NPX_Fidi_AperturaProposta">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPX_Fidi_AperturaProposta"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
