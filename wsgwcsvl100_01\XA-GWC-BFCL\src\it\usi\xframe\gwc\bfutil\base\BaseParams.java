/*
 * Created on Jun 24, 2009
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.base;

import it.usi.xframe.gwc.bfutil.base.IBaseConstDefinitions;
import it.usi.xframe.utl.bfutil.IParams;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class BaseParams  implements IParams, Serializable, IBaseConstDefinitions{
	
	public void clear() {
		// TODO Auto-generated method stub
	}

}
