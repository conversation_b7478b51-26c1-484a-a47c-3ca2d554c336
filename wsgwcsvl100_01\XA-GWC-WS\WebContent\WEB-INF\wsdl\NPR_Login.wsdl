<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprLogin">
    <complexType>
     <sequence>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprLoginResponse">
    <complexType>
     <sequence>
      <element name="nprLoginReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprLoginResponse">

      <wsdl:part element="impl:nprLoginResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprLoginRequest">

      <wsdl:part element="impl:nprLogin" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Login_SEI">

      <wsdl:operation name="nprLogin">

         <wsdl:input message="impl:nprLoginRequest" name="nprLoginRequest"/>

         <wsdl:output message="impl:nprLoginResponse" name="nprLoginResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_LoginSoapBinding" type="impl:NPR_Login_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprLogin">

         <wsdlsoap:operation soapAction="nprLogin"/>

         <wsdl:input name="nprLoginRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprLoginResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_LoginService">

      <wsdl:port binding="impl:NPR_LoginSoapBinding" name="NPR_Login">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Login"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
