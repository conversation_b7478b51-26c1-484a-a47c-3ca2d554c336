<?xml version="1.0"?>
<!-- Generated by XMLGEN Versione="4.8.264" utente="Gasparini" Timestamp="23/03/2004 13.08.28" -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="SELETTORE"/>
					<param name="PRZUBZ2I_BANCODE"/>
					<param name="PRZUBZ2I_NDG"/>
					<param name="PRZUBZ2I_IN_TIPO_ESTR"/>
					<param name="PRZUBZ2O_PROG_GAR"/>
					<param name="PRZUBZ2O_FORMA_TEC_GAR"/>
					<param name="PRZUBZ2O_DATA_PERFEZION"/>
					<param name="PRZUBZ2O_DATA_ESTINZ"/>
					<param name="PRZUBZ2O_NUM_PROPOSTA"/>
					<param name="PRZUBZ2O_STATO_GAR"/>
					<param name="PRZUBZ2O_DIVISA"/>
					<param name="PRZUBZ2O_IMPORTO_GARANZIA"/>
					<param name="PRZUBZ2O_IMPORTO_GARANZIA_E"/>   
					<param name="PRZUBZ2O_IMP_DEL_FID_ESS_E"/>    
					<param name="PRZUBZ2O_IMP_FID_PROP_E"/>       
					<param name="XF_GAUSS_ID"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.GaussProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<hostService>PRZ_MAIN_PERIZIE</hostService>
			<applBankNumber>99</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WPRZ</transaction>
			<timeout>30000</timeout>
			<applid>CICS</applid>
			<synclevel>0</synclevel>
		</params>
	</channel>
</service>