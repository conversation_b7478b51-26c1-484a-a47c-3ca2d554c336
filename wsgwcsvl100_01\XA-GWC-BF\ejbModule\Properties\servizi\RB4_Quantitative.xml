<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R53I-PROPOSAL-ID"/>
					<param name="R53I-FUNCTION"/>
					<param name="R53I-USER-ID"/>
					<param name="R53I-TOT-EQUITY-M"/>
					<param name="R53I-TOT-EQUITY-C"/>
					<param name="R53I-TOT-EQUITY-S"/>
					<param name="R53I-TOT-EQUITY-CM"/>
					<param name="R53I-TOT-EQUITY-SM"/>
					<param name="R53I-PRE-TAX-PROF-M"/>
					<param name="R53I-PRE-TAX-PROF-C"/>
					<param name="R53I-PRE-TAX-PROF-S"/>
					<param name="R53I-PRE-TAX-PROF-CM"/>
					<param name="R53I-PRE-TAX-PROF-SM"/>
					<param name="R53I-PRE-TAX-PROF-T1-M"/>
					<param name="R53I-PRE-TAX-PROF-T1-C"/>
					<param name="R53I-PRE-TAX-PROF-T1-S"/>
					<param name="R53I-PRE-TAX-PROF-T1-CM"/>
					<param name="R53I-PRE-TAX-PROF-T1-SM"/>
					<param name="R53I-PRE-TAX-PROF-T2-M"/>
					<param name="R53I-PRE-TAX-PROF-T2-C"/>
					<param name="R53I-PRE-TAX-PROF-T2-S"/>
					<param name="R53I-PRE-TAX-PROF-T2-CM"/>
					<param name="R53I-PRE-TAX-PROF-T2-SM"/>			
					<param name="R53I-TOT-OP-IN-M"/>
					<param name="R53I-TOT-OP-IN-C"/>
					<param name="R53I-TOT-OP-IN-S"/>
					<param name="R53I-TOT-OP-IN-CM"/>
					<param name="R53I-TOT-OP-IN-SM"/>
					<param name="R53I-TOT-OP-IN-T1-M"/>
					<param name="R53I-TOT-OP-IN-T1-C"/>
					<param name="R53I-TOT-OP-IN-T1-S"/>
					<param name="R53I-TOT-OP-IN-T1-CM"/>
					<param name="R53I-TOT-OP-IN-T1-SM"/>
					<param name="R53I-TOT-OP-IN-T2-M"/>
					<param name="R53I-TOT-OP-IN-T2-C"/>
					<param name="R53I-TOT-OP-IN-T2-S"/>
					<param name="R53I-TOT-OP-IN-T2-CM"/>
					<param name="R53I-TOT-OP-IN-T2-SM"/>	
					<param name="R53I-NON-OP-IN-M"/>
					<param name="R53I-NON-OP-IN-C"/>
					<param name="R53I-NON-OP-IN-S"/>
					<param name="R53I-NON-OP-IN-CM"/>
					<param name="R53I-NON-OP-IN-SM"/>
					<param name="R53I-NON-OP-IN-T1-M"/>
					<param name="R53I-NON-OP-IN-T1-C"/>
					<param name="R53I-NON-OP-IN-T1-S"/>
					<param name="R53I-NON-OP-IN-T1-CM"/>
					<param name="R53I-NON-OP-IN-T1-SM"/>
					<param name="R53I-NON-OP-IN-T2-M"/>
					<param name="R53I-NON-OP-IN-T2-C"/>
					<param name="R53I-NON-OP-IN-T2-S"/>
					<param name="R53I-NON-OP-IN-T2-CM"/>
					<param name="R53I-NON-OP-IN-T2-SM"/>
					<param name="R53I-NON-OP-EXP-M"/>
					<param name="R53I-NON-OP-EXP-C"/>
					<param name="R53I-NON-OP-EXP-S"/>
					<param name="R53I-NON-OP-EXP-CM"/>
					<param name="R53I-NON-OP-EXP-SM"/>
					<param name="R53I-NON-OP-EXP-T1-M"/>
					<param name="R53I-NON-OP-EXP-T1-C"/>
					<param name="R53I-NON-OP-EXP-T1-S"/>
					<param name="R53I-NON-OP-EXP-T1-CM"/>
					<param name="R53I-NON-OP-EXP-T1-SM"/>
					<param name="R53I-NON-OP-EXP-T2-M"/>
					<param name="R53I-NON-OP-EXP-T2-C"/>
					<param name="R53I-NON-OP-EXP-T2-S"/>
					<param name="R53I-NON-OP-EXP-T2-CM"/>
					<param name="R53I-NON-OP-EXP-T2-SM"/>					
					<param name="R53I-EXT-IN-M"/>
					<param name="R53I-EXT-IN-C"/>
					<param name="R53I-EXT-IN-S"/>
					<param name="R53I-EXT-IN-CM"/>
					<param name="R53I-EXT-IN-SM"/>
					<param name="R53I-EXT-IN-T1-M"/>
					<param name="R53I-EXT-IN-T1-C"/>
					<param name="R53I-EXT-IN-T1-S"/>
					<param name="R53I-EXT-IN-T1-CM"/>
					<param name="R53I-EXT-IN-T1-SM"/>
					<param name="R53I-EXT-IN-T2-M"/>
					<param name="R53I-EXT-IN-T2-C"/>
					<param name="R53I-EXT-IN-T2-S"/>
					<param name="R53I-EXT-IN-T2-CM"/>
					<param name="R53I-EXT-IN-T2-SM"/>
					<param name="R53I-EXT-EXP-M"/>
					<param name="R53I-EXT-EXP-C"/>
					<param name="R53I-EXT-EXP-S"/>
					<param name="R53I-EXT-EXP-CM"/>
					<param name="R53I-EXT-EXP-SM"/>
					<param name="R53I-EXT-EXP-T1-M"/>
					<param name="R53I-EXT-EXP-T1-C"/>
					<param name="R53I-EXT-EXP-T1-S"/>
					<param name="R53I-EXT-EXP-T1-CM"/>
					<param name="R53I-EXT-EXP-T1-SM"/>
					<param name="R53I-EXT-EXP-T2-M"/>
					<param name="R53I-EXT-EXP-T2-C"/>
					<param name="R53I-EXT-EXP-T2-S"/>
					<param name="R53I-EXT-EXP-T2-CM"/>
					<param name="R53I-EXT-EXP-T2-SM"/>
					<param name="R53I-EXC-IN-M"/>
					<param name="R53I-EXC-IN-C"/>
					<param name="R53I-EXC-IN-S"/>
					<param name="R53I-EXC-IN-CM"/>
					<param name="R53I-EXC-IN-SM"/>
					<param name="R53I-EXC-IN-T1-M"/>
					<param name="R53I-EXC-IN-T1-C"/>
					<param name="R53I-EXC-IN-T1-S"/>
					<param name="R53I-EXC-IN-T1-CM"/>
					<param name="R53I-EXC-IN-T1-SM"/>
					<param name="R53I-EXC-IN-T2-M"/>
					<param name="R53I-EXC-IN-T2-C"/>
					<param name="R53I-EXC-IN-T2-S"/>
					<param name="R53I-EXC-IN-T2-CM"/>
					<param name="R53I-EXC-IN-T2-SM"/>
					<param name="R53I-EXC-EXP-M"/>
					<param name="R53I-EXC-EXP-C"/>
					<param name="R53I-EXC-EXP-S"/>
					<param name="R53I-EXC-EXP-CM"/>
					<param name="R53I-EXC-EXP-SM"/>
					<param name="R53I-EXC-EXP-T1-M"/>
					<param name="R53I-EXC-EXP-T1-C"/>
					<param name="R53I-EXC-EXP-T1-S"/>
					<param name="R53I-EXC-EXP-T1-CM"/>
					<param name="R53I-EXC-EXP-T1-SM"/>
					<param name="R53I-EXC-EXP-T2-M"/>
					<param name="R53I-EXC-EXP-T2-C"/>
					<param name="R53I-EXC-EXP-T2-S"/>
					<param name="R53I-EXC-EXP-T2-CM"/>
					<param name="R53I-EXC-EXP-T2-SM"/>
					<param name="R53I-DEP-BANK-M"/>
					<param name="R53I-DEP-BANK-C"/>
					<param name="R53I-DEP-BANK-S"/>
					<param name="R53I-DEP-BANK-CM"/>
					<param name="R53I-DEP-BANK-SM"/>
					<param name="R53I-DUE-CBANK-M"/>
					<param name="R53I-DUE-CBANK-C"/>
					<param name="R53I-DUE-CBANK-S"/>
					<param name="R53I-DUE-CBANK-CM"/>
					<param name="R53I-DUE-CBANK-SM"/>
					<param name="R53I-DUE-OBANK-M"/>
					<param name="R53I-DUE-OBANK-C"/>
					<param name="R53I-DUE-OBANK-S"/>
					<param name="R53I-DUE-OBANK-CM"/>
					<param name="R53I-DUE-OBANK-SM"/>
					<param name="R53I-DUE-OCRINST-M"/>
					<param name="R53I-DUE-OCRINST-C"/>
					<param name="R53I-DUE-OCRINST-S"/>
					<param name="R53I-DUE-OCRINST-CM"/>
					<param name="R53I-DUE-OCRINST-SM"/>
					<param name="R53I-TREAS-BILL-M"/>
					<param name="R53I-TREAS-BILL-C"/>
					<param name="R53I-TREAS-BILL-S"/>
					<param name="R53I-TREAS-BILL-CM"/>
					<param name="R53I-TREAS-BILL-SM"/>
					<param name="R53I-CASH-DUE-BANK-M"/>
					<param name="R53I-CASH-DUE-BANK-C"/>
					<param name="R53I-CASH-DUE-BANK-S"/>
					<param name="R53I-CASH-DUE-BANK-CM"/>
					<param name="R53I-CASH-DUE-BANK-SM"/>
					<param name="R53I-DEP-ST-FUND-M"/>
					<param name="R53I-DEP-ST-FUND-C"/>
					<param name="R53I-DEP-ST-FUND-S"/>
					<param name="R53I-DEP-ST-FUND-CM"/>
					<param name="R53I-DEP-ST-FUND-SM"/>
					<param name="R53I-NET-INCOME-M"/>
					<param name="R53I-NET-INCOME-C"/>
					<param name="R53I-NET-INCOME-S"/>
					<param name="R53I-NET-INCOME-CM"/>
					<param name="R53I-NET-INCOME-SM"/>
					<param name="R53I-NET-INT-REV-M"/>
					<param name="R53I-NET-INT-REV-C"/>
					<param name="R53I-NET-INT-REV-S"/>
					<param name="R53I-NET-INT-REV-CM"/>
					<param name="R53I-NET-INT-REV-SM"/>
					<param name="R53I-OTH-OP-IN-M"/>
					<param name="R53I-OTH-OP-IN-C"/>
					<param name="R53I-OTH-OP-IN-S"/>
					<param name="R53I-OTH-OP-IN-CM"/>
					<param name="R53I-OTH-OP-IN-SM"/>
					<param name="R53I-TOT-ASS-M"/>
					<param name="R53I-TOT-ASS-C"/>
					<param name="R53I-TOT-ASS-S"/>
					<param name="R53I-TOT-ASS-CM"/>
					<param name="R53I-TOT-ASS-SM"/>
					<param name="R53I-LOAN-LOSS-PROV-M"/>
					<param name="R53I-LOAN-LOSS-PROV-C"/>
					<param name="R53I-LOAN-LOSS-PROV-S"/>
					<param name="R53I-LOAN-LOSS-PROV-CM"/>
					<param name="R53I-LOAN-LOSS-PROV-SM"/>
					<param name="R53I-LOANS-M"/>
					<param name="R53I-LOANS-C"/>
					<param name="R53I-LOANS-S"/>
					<param name="R53I-LOANS-CM"/>
					<param name="R53I-LOANS-SM"/>
					<param name="R53I-LOANS-T1-M"/>
					<param name="R53I-LOANS-T1-C"/>
					<param name="R53I-LOANS-T1-S"/>
					<param name="R53I-LOANS-T1-CM"/>
					<param name="R53I-LOANS-T1-SM"/>
					<param name="R53I-DTQ-1L07-A"/>
					<param name="R53I-DTQ-1L07-M"/>					
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB53-INPUT</hostService>
            <applBankNumber>99</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0001</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB53</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
