<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
                	<param name="R72I-PROPOSAL-ID"/>
					<param name="R72I-FUNCTION"/>
					<param name="R72I-USER-ID"/>
					<param name="R72I-CP-NAME-MOT-A"/>
					<param name="R72I-CP-NAME-MOT-M"/>
					<param name="R72I-SWIFT-CODE-MOT-A"/>
					<param name="R72I-SWIFT-CODE-MOT-M"/>
					<param name="R72I-COD-COUNTRY-MOT-A"/>
					<param name="R72I-COD-COUNTRY-MOT-M"/>
					<param name="R72I-COD-COUNTRY-RISK"/>
					<param name="R72I-COD-COUNTRY-RISK-MOT-A"/>
					<param name="R72I-COD-COUNTRY-RISK-MOT-M"/>
					<param name="R72I-MOODY-COUNTRY-M"/>
					<param name="R72I-MOODY-COUNTRY-C"/>
					<param name="R72I-MOODY-COUNTRY-CM"/>
					<param name="R72I-SANDP-COUNTRY-M"/>
					<param name="R72I-SANDP-COUNTRY-C"/>
					<param name="R72I-SANDP-COUNTRY-CM"/>
					<param name="R72I-FITCH-COUNTRY-M"/>
					<param name="R72I-FITCH-COUNTRY-C"/>
					<param name="R72I-FITCH-COUNTRY-CM"/>
					<param name="R72I-MOODY-CP-M"/>
					<param name="R72I-MOODY-CP-C"/>
					<param name="R72I-MOODY-CP-CM"/>
					<param name="R72I-SANDP-CP-M"/>
					<param name="R72I-SANDP-CP-C"/>
					<param name="R72I-SANDP-CP-CM"/>
					<param name="R72I-FITCH-CP-M"/>
					<param name="R72I-FITCH-CP-C"/>
					<param name="R72I-FITCH-CP-CM"/>
					<param name="R72I-DTQ1L03-A"/>
					<param name="R72I-DTQ1L03-M"/>
				</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RB72-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB72</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>