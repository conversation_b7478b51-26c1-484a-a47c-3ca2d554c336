<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it">
  <wsdl:types>
    <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="callServiceFree">
    <complexType>
     <sequence>
      <element name="service" nillable="true" type="xsd:string"/>
      <element name="params" nillable="true" type="xsd:string"/>
      <element name="sep1" nillable="true" type="xsd:string"/>
      <element name="sep2" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="callServiceFreeResponse">
    <complexType>
     <sequence>
      <element name="callServiceFreeReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
  </wsdl:types>
  <wsdl:message name="callServiceFreeResponse">
    <wsdl:part name="parameters" element="intf:callServiceFreeResponse"/>
  </wsdl:message>
  <wsdl:message name="callServiceFreeRequest">
    <wsdl:part name="parameters" element="intf:callServiceFree"/>
  </wsdl:message>
  <wsdl:portType name="CallServiceFree">
    <wsdl:operation name="callServiceFree">
      <wsdl:input name="callServiceFreeRequest" message="intf:callServiceFreeRequest"/>
      <wsdl:output name="callServiceFreeResponse" message="intf:callServiceFreeResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="CallServiceFreeSoapBinding" type="intf:CallServiceFree">
    <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="callServiceFree">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="callServiceFreeRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="callServiceFreeResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="CallServiceFreeService">
    <wsdl:port name="CallServiceFree" binding="intf:CallServiceFreeSoapBinding">
      <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/CallServiceFree"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
