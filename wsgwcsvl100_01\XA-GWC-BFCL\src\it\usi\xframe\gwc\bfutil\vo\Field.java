/*
 * Created on Feb 19, 2008
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.vo;

import it.usi.xframe.utl.bfutil.DataValue;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class Field extends DataValue implements Serializable{
	
	private String aggr_dati;
	private String nome_camp;
	private BigInteger prog;
	private String formato;
	private BigInteger len;
	private BigInteger len_dec;
	private boolean flag_segno;
	
	/**
	 * @return
	 */
	public String getAggr_dati() {
		return aggr_dati;
	}

	/**
	 * @return
	 */
	public boolean isFlag_segno() {
		return flag_segno;
	}

	/**
	 * @return
	 */
	public String getFormato() {
		return formato;
	}

	/**
	 * @return
	 */
	public BigInteger getLen() {
		return len;
	}

	/**
	 * @return
	 */
	public BigInteger getLen_dec() {
		return len_dec;
	}

	/**
	 * @return
	 */
	public String getNome_camp() {
		return nome_camp;
	}

	/**
	 * @return
	 */
	public BigInteger getProg() {
		return prog;
	}

	/**
	 * @param string
	 */
	public void setAggr_dati(String string) {
		aggr_dati = string;
	}

	/**
	 * @param b
	 */
	public void setFlag_segno(boolean b) {
		flag_segno = b;
	}

	/**
	 * @param string
	 */
	public void setFormato(String string) {
		formato = string;
	}

	/**
	 * @param integer
	 */
	public void setLen(BigInteger integer) {
		len = integer;
	}

	/**
	 * @param integer
	 */
	public void setLen_dec(BigInteger integer) {
		len_dec = integer;
	}

	/**
	 * @param string
	 */
	public void setNome_camp(String string) {
		nome_camp = string;
	}

	/**
	 * @param integer
	 */
	public void setProg(BigInteger integer) {
		prog = integer;
	}

}
