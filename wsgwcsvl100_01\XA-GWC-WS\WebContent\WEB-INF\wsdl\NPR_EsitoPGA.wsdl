<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprEsitoPGA">
    <complexType>
     <sequence>
      <element name="x03" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprEsitoPGAResponse">
    <complexType>
     <sequence>
      <element name="nprEsitoPGAReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprEsitoPGAResponse">

      <wsdl:part element="impl:nprEsitoPGAResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprEsitoPGARequest">

      <wsdl:part element="impl:nprEsitoPGA" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_EsitoPGA_SEI">

      <wsdl:operation name="nprEsitoPGA">

         <wsdl:input message="impl:nprEsitoPGARequest" name="nprEsitoPGARequest"/>

         <wsdl:output message="impl:nprEsitoPGAResponse" name="nprEsitoPGAResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_EsitoPGASoapBinding" type="impl:NPR_EsitoPGA_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprEsitoPGA">

         <wsdlsoap:operation soapAction="nprEsitoPGA"/>

         <wsdl:input name="nprEsitoPGARequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprEsitoPGAResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_EsitoPGAService">

      <wsdl:port binding="impl:NPR_EsitoPGASoapBinding" name="NPR_EsitoPGA">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_EsitoPGA"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
