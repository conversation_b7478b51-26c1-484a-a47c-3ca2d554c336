/**
 * CallServiceServiceLocator.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * m1116.12 v5211201433
 */

package it.usi.xframe.gwc.wsutil;

public class CallServiceServiceLocator extends com.ibm.ws.webservices.multiprotocol.AgnosticService implements com.ibm.ws.webservices.multiprotocol.GeneratedService, it.usi.xframe.gwc.wsutil.CallServiceService {

    public CallServiceServiceLocator() {
        super(com.ibm.ws.webservices.engine.utils.QNameTable.createQName(
           "http://wsutil.gwc.xframe.usi.it",
           "CallServiceService"));

        context.setLocatorName("it.usi.xframe.gwc.wsutil.CallServiceServiceLocator");
    }

    public CallServiceServiceLocator(com.ibm.ws.webservices.multiprotocol.ServiceContext ctx) {
        super(ctx);
        context.setLocatorName("it.usi.xframe.gwc.wsutil.CallServiceServiceLocator");
    }

    // Use to get a proxy class for callService
    private final java.lang.String callService_address = "http://localhost:9080/XA-GWC-WS/services/CallService";

    public java.lang.String getCallServiceAddress() {
        if (context.getOverriddingEndpointURIs() == null) {
            return callService_address;
        }
        String overriddingEndpoint = (String) context.getOverriddingEndpointURIs().get("CallService");
        if (overriddingEndpoint != null) {
            return overriddingEndpoint;
        }
        else {
            return callService_address;
        }
    }

    private java.lang.String callServicePortName = "CallService";

    // The WSDD port name defaults to the port name.
    private java.lang.String callServiceWSDDPortName = "CallService";

    public java.lang.String getCallServiceWSDDPortName() {
        return callServiceWSDDPortName;
    }

    public void setCallServiceWSDDPortName(java.lang.String name) {
        callServiceWSDDPortName = name;
    }

    public it.usi.xframe.gwc.wsutil.CallService getCallService() throws javax.xml.rpc.ServiceException {
       java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(getCallServiceAddress());
        }
        catch (java.net.MalformedURLException e) {
            return null; // unlikely as URL was validated in WSDL2Java
        }
        return getCallService(endpoint);
    }

    public it.usi.xframe.gwc.wsutil.CallService getCallService(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        it.usi.xframe.gwc.wsutil.CallService _stub =
            (it.usi.xframe.gwc.wsutil.CallService) getStub(
                callServicePortName,
                (String) getPort2NamespaceMap().get(callServicePortName),
                it.usi.xframe.gwc.wsutil.CallService.class,
                "it.usi.xframe.gwc.wsutil.CallServiceSoapBindingStub",
                portAddress.toString());
        if (_stub instanceof com.ibm.ws.webservices.engine.client.Stub) {
            ((com.ibm.ws.webservices.engine.client.Stub) _stub).setPortName(callServiceWSDDPortName);
        }
        return _stub;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (it.usi.xframe.gwc.wsutil.CallService.class.isAssignableFrom(serviceEndpointInterface)) {
                return getCallService();
            }
        }
        catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("WSWS3273E: Error: There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        String inputPortName = portName.getLocalPart();
        if ("CallService".equals(inputPortName)) {
            return getCallService();
        }
        else  {
            throw new javax.xml.rpc.ServiceException();
        }
    }

    public void setPortNamePrefix(java.lang.String prefix) {
        callServiceWSDDPortName = prefix + "/" + callServicePortName;
    }

    public javax.xml.namespace.QName getServiceName() {
        return com.ibm.ws.webservices.engine.utils.QNameTable.createQName("http://wsutil.gwc.xframe.usi.it", "CallServiceService");
    }

    private java.util.Map port2NamespaceMap = null;

    protected synchronized java.util.Map getPort2NamespaceMap() {
        if (port2NamespaceMap == null) {
            port2NamespaceMap = new java.util.HashMap();
            port2NamespaceMap.put(
               "CallService",
               "http://schemas.xmlsoap.org/wsdl/soap/");
        }
        return port2NamespaceMap;
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            String serviceNamespace = getServiceName().getNamespaceURI();
            for (java.util.Iterator i = getPort2NamespaceMap().keySet().iterator(); i.hasNext(); ) {
                ports.add(
                    com.ibm.ws.webservices.engine.utils.QNameTable.createQName(
                        serviceNamespace,
                        (String) i.next()));
            }
        }
        return ports.iterator();
    }

    public javax.xml.rpc.Call[] getCalls(javax.xml.namespace.QName portName) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            throw new javax.xml.rpc.ServiceException("WSWS3062E: Error: portName should not be null.");
        }
        if  (portName.getLocalPart().equals("CallService")) {
            return new javax.xml.rpc.Call[] {
                createCall(portName, "callService", "callServiceRequest"),
            };
        }
        else {
            throw new javax.xml.rpc.ServiceException("WSWS3062E: Error: portName should not be null.");
        }
    }
}
