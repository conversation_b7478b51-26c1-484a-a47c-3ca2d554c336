<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Cancella">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_CancellaResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_CancellaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_CancellaResponse">

      <wsdl:part element="impl:nprFidi_CancellaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_CancellaRequest">

      <wsdl:part element="impl:nprFidi_Cancella" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Cancella_SEI">

      <wsdl:operation name="nprFidi_Cancella">

         <wsdl:input message="impl:nprFidi_CancellaRequest" name="nprFidi_CancellaRequest"/>

         <wsdl:output message="impl:nprFidi_CancellaResponse" name="nprFidi_CancellaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_CancellaSoapBinding" type="impl:NPR_Fidi_Cancella_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Cancella">

         <wsdlsoap:operation soapAction="nprFidi_Cancella"/>

         <wsdl:input name="nprFidi_CancellaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_CancellaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_CancellaService">

      <wsdl:port binding="impl:NPR_Fidi_CancellaSoapBinding" name="NPR_Fidi_Cancella">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Cancella"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
