<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
    <wb-module deploy-name="XA-GWC">
        <wb-resource deploy-path="/" source-path="/" tag="defaultRootSource"/>
        <dependent-module archiveName="XA-GWC-PF.war" deploy-path="/" handle="module:/resource/XA-GWC-PF/XA-GWC-PF">
            <dependent-object>WebModule_1368092166796</dependent-object>
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="XA-GWC-WS.war" deploy-path="/" handle="module:/resource/XA-GWC-WS/XA-GWC-WS">
            <dependent-object>WebModule_1368092166827</dependent-object>
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="XA-GWC-BF.jar" deploy-path="/" handle="module:/resource/XA-GWC-BF/XA-GWC-BF">
            <dependent-object>EjbModule_1368092192225</dependent-object>
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="XA-GWC-BFCL.jar" deploy-path="/" handle="module:/resource/XA-GWC-BFCL/XA-GWC-BFCL">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="XA-IFG-BFCL.jar" deploy-path="/" handle="module:/classpath/lib/XA-IFG/XA-IFG-BFCL.jar">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="XA-PRE-BFCL.jar" deploy-path="/" handle="module:/classpath/lib/XA-PRE/XA-PRE-BFCL.jar">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="XA-UTL-BFCL.jar" deploy-path="/" handle="module:/classpath/lib/XA-UTL/XA-UTL-BFCL.jar">
            <dependency-type>uses</dependency-type>
        </dependent-module>
    </wb-module>
</project-modules>