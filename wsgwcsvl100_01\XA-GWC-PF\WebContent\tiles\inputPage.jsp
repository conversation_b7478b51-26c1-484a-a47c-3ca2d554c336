<!--  completeStateTest.jsp   -->
<%@ taglib uri="/WEB-INF/struts-html.tld" prefix="html" %>

<html:form action="executeMsg.do" enctype="multipart/form-data" method ="post">
 <table border="0" align="center">
	<tr>
	 <td class="utl_data_label" align="right">Nome Servizio (Es. RBG_Bank_AccessControl) </td>
	 <td class="utl_data_label" align="left">
	 	<INPUT type="text" name="serviceName" id="serviceName" maxlength="40" size="50" />
	 </td>
	</tr>
	<tr>
	 <td class="utl_data_label" align="right">Campi di Input e relativi valori. Es X01=prova;X02=prova2....</td>
	 <td class="utl_data_label" align="left">
	   <textArea cols="80" rows="60" name="textInput" id="textInput"></textArea>
	 </td>
	</tr>
	<tr>
	 <td class="utl_data_label" align="right">Separatore tra Campo/Valore</td>
	 <td class="utl_data_label" align="left">
	 	<INPUT type="text" name="sep1" id="sep1" value="=" maxlength="3" size="3" />
	 </td>
	</tr>
	<tr>
	 <td class="utl_data_label" align="right">Separatore tra Coppie</td>
	 <td class="utl_data_label" align="left">
	 	<INPUT type="text" name="sep2" id="sep2" value=";" maxlength="3" size="3" />
	 </td>
	</tr>
	<tr>
	  <td class="utl_data_label" align="center" colspan="2">
		<input type="submit" name="execute" class="utl_button" value="EXECUTE"/>
	  </td>
    </tr>
 </table>
</html:form>

<!-- end completeStateTest.jsp  -->