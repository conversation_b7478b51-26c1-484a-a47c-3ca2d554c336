<?xml version="1.0"?>
<!-- This XML has been Auto-Generated -->
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
	security="intranet">
	<dispenser>
		<road>
			<request>
				<input>							
					<param name="UWMC050I_USER_ID" />
					<param name="UWMC050I_USER_DIP" />                   
					<param name="UWMC050I_CPROC" />                       
					<param name="UWMC050I_TOPE" />                        
					<param name="UWMC050I_ABI" />                       
					<param name="UWMC050I_TIP_RAP" />               
					<param name="UWMC050I_DIP_RAP" />                    
					<param name="UWMC050I_NUM_RAP" />                     
					<param name="UWMC050I_NDG" />                      
					<param name="UWMC050I_FL_CORR" />                
					<param name="UWMC050I_FL_PT" />                 
					<param name="UWMC050I_NDG_ESE_SUB1" />              
					<param name="UWMC050I_STATO_FINALE" />              
					<param name="XF_GAUSS_ID" />
				</input>
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
    </provider>
	<protocol>
		<bridge>
			<request>
				<input />
				<output />
			</request>
			<response>
				<input />
				<output />
			</response>
		</bridge>
		<params>			
			<hostService>UWM_ANTIRIC_CIA</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUW1</transaction>
			<program>PC00WUW1</program>
			<timeout>15000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>

