<?xml version="1.0"?>
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="X02"/>
					<param name="X03"/>
					<param name="X04"/>
					<param name="N001"/>
					<param name="X05"/>
					<param name="X07"/>
					<param name="N003"/>
					<param name="N004"/>
					<param name="X12"/>
					<param name="X15"/>
					<param name="X98"/>
					<param name="N006"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.RussianProtocol</protocolclass>
		<channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output>
					<hostService name="XP58XP58" id="1"/>
				</output>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<scheduler>XP00</scheduler>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>XP00</transaction>
			<timeout>60000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>
