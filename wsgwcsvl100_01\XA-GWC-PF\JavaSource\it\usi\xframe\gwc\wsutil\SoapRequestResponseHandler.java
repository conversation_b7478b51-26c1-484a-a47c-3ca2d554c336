/*
 * Created on Feb 17, 2010
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.wsutil;

import javax.xml.namespace.QName;
import javax.xml.rpc.handler.GenericHandler;
import javax.xml.rpc.handler.Handler;
import javax.xml.rpc.handler.HandlerInfo;
import javax.xml.rpc.handler.MessageContext;
import javax.xml.rpc.handler.soap.SOAPMessageContext;
import javax.xml.soap.Name;
import javax.xml.soap.SOAPElement;
import javax.xml.soap.SOAPEnvelope;
import javax.xml.soap.SOAPFactory;
import javax.xml.soap.SOAPHeader;
import javax.xml.soap.SOAPHeaderElement;
import javax.xml.soap.SOAPMessage;
import javax.xml.soap.SOAPPart;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class SoapRequestResponseHandler extends GenericHandler implements Handler {
	private static final Log log                  = LogFactory.getLog(SoapRequestResponseHandler.class);
	private HandlerInfo handlerInfo               = null;
	
	public void init(HandlerInfo handlerInfo){
		  this.handlerInfo = handlerInfo;
	}
	
	public boolean handleRequest(MessageContext messageContext){
     	log.info("<<<INSIDE HANDLE REQUEST>>> ");
		try{
			SOAPMessageContext sctx = (SOAPMessageContext)messageContext;
			SOAPMessage message     = sctx.getMessage();
			SOAPPart sp             = message.getSOAPPart();
			SOAPEnvelope senv       = sp.getEnvelope();
			
			/*
				   * Struttura dell'handler
				   * 
				   * <wsse:Security soapenv:mustUnderstand="1" 
				   * xmlns:wsse="http://schemas.xmlsoap.org/ws/2003/06/secext">
							 <wsse:UsernameToken>
								   <wsse:Username>XXXXXX</wsse:Username>
						<wsse:Password>XXXXXX</wsse:Password>
					</wsse:UsernameToken>
				  </wsse:Security>
			*/

			
			log.info("HEADER PRIMA: " + senv.getHeader().toString());            

			//tolgo l'header se esiste e ne creo uno nuovo
			senv.getHeader().detachNode();
			SOAPHeader header = senv.addHeader();

			Name secName = senv.createName("Security", "wsse", "http://schemas.xmlsoap.org/ws/2003/06/secext");
			SOAPHeaderElement securityElement = header.addHeaderElement(secName);
			securityElement.setMustUnderstand(true);

			SOAPFactory factory = SOAPFactory.newInstance();

			Name usrnameToken = factory.createName("UsernameToken", "wsse", "http://schemas.xmlsoap.org/ws/2003/06/secext");
			SOAPElement ut = factory.createElement(usrnameToken);
                 
			String userid = (String) sctx.getProperty("userid");
			log.info("HEADER DURANTE: userid = " + userid);
			Name username = factory.createName("Username", "wsse", "http://schemas.xmlsoap.org/ws/2003/06/secext");
			SOAPElement uel = factory.createElement(username);
			uel.addTextNode(userid);
               
			String pwd = (String) sctx.getProperty("pwd");
			log.info("HEADER DURANTE: pwd = " + pwd);
			Name password = factory.createName("Password", "wsse", "http://schemas.xmlsoap.org/ws/2003/06/secext");
			SOAPElement upwd = factory.createElement(password);
			upwd.addTextNode(pwd);

			ut.addChildElement(uel);
			ut.addChildElement(upwd);

			securityElement.addChildElement(ut);

			log.info("HEADER DOPO: " + senv.getHeader().toString());
			log.info("BODY: " + senv.getBody().toString());
		}catch (Exception e){
			log.error(e, e.getCause());
		}
			 return true;
	}
	   
	public boolean handleResponse(MessageContext messageContext){
		log.info("<<<INSIDE HANDLE RESPONSE>>> ");
		try{
			SOAPMessageContext sctx = (SOAPMessageContext)messageContext;
			SOAPMessage message     = sctx.getMessage();
			SOAPPart sp             = message.getSOAPPart();
			SOAPEnvelope senv       = sp.getEnvelope();
			
			System.out.println("HEADER: " + senv.getHeader().toString());
			System.out.println("BPDY: " + senv.getBody().toString());
				
		  }
		  catch (Exception e)
		  {
			
			log.error(e, e.getCause());
			
		  }
		return true;
   }

	public QName[] getHeaders() {
		// TODO Auto-generated method stub
		  return handlerInfo.getHeaders();
	}

	public boolean handleFault(SOAPMessageContext smc) {
		 log.error("<<<INSIDE SECOND HANDLE FAULT>>> ");
		return true;
	}
	
	
}
