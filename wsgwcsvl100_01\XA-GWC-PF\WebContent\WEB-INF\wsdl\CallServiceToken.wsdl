<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it">
  <wsdl:types>
    <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="callServiceToken">
    <complexType>
     <sequence>
      <element name="service" nillable="true" type="xsd:string"/>
      <element name="params" nillable="true" type="xsd:string"/>
      <element name="sep1" nillable="true" type="xsd:string"/>
      <element name="sep2" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="callServiceTokenResponse">
    <complexType>
     <sequence>
      <element name="callServiceTokenReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
  </wsdl:types>
  <wsdl:message name="callServiceTokenRequest">
    <wsdl:part name="parameters" element="intf:callServiceToken"/>
  </wsdl:message>
  <wsdl:message name="callServiceTokenResponse">
    <wsdl:part name="parameters" element="intf:callServiceTokenResponse"/>
  </wsdl:message>
  <wsdl:portType name="CallServiceToken">
    <wsdl:operation name="callServiceToken">
      <wsdl:input name="callServiceTokenRequest" message="intf:callServiceTokenRequest"/>
      <wsdl:output name="callServiceTokenResponse" message="intf:callServiceTokenResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="CallServiceTokenSoapBinding" type="intf:CallServiceToken">
    <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="callServiceToken">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="callServiceTokenRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="callServiceTokenResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="CallServiceTokenService">
    <wsdl:port name="CallServiceToken" binding="intf:CallServiceTokenSoapBinding">
      <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/CallServiceToken"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
