/*
 * Created on June 4, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfimpl.pm;

import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import it.usi.xframe.system.errors.XFRException;
import it.usi.xframe.utl.bfutil.DataValue;
import it.usi.xframe.utl.bfutil.pm.PersistenceManager;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */




public class DB2_Table_Records_PersistenceManager extends PersistenceManager {
	
	private static DB2_Table_Records_PersistenceManager instance = null;
	
	/**	Logger for debugging */
	private Log logger = LogFactory.getLog(this.getClass());
			
	private DB2_Table_Records_PersistenceManager() 
	{ 
	}			
	
	/**
	* @return singleton
	*/
	public static synchronized DB2_Table_Records_PersistenceManager getInstance() {

		if (instance == null)
			instance = new DB2_Table_Records_PersistenceManager();
			
		return instance;
	}
	
	protected DataValue load(Map record) {		
		return new DataValue(((String) record.get("NUM_RECORDS")).trim(),"");
	}

	public boolean record_presence(String owner,String table) throws XFRException 
	{
		String query = "SELECT COUNT(*) AS NUM_RECORDS FROM "+owner+"."+table;
						
		PersistenceManager.SqlParam[] params = new PersistenceManager.SqlParam[0];						
										
		setSqlList(query);
		
		List dvl = (List) executeList(params);

		int num_records = Integer.parseInt(	((DataValue) dvl.get(0)).getCode()	);

		logger.info(query);		
		logger.info("Owner = #"+owner+"#");
		logger.info("Name = #"+ table+"#");
		logger.info("Table " + table + " has " + num_records + " records");
		
		if (num_records>0)
			return true;
		else
			return false;
	}	


}