package it.usi.xframe.gwc.bfutil.base;

import it.usi.xframe.gwc.bfutil.base.IBaseConstDefinitions;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;


public class BaseWarning implements Serializable, IBaseConstDefinitions {

	private String code;
	private ArrayList descriptions;
	private HashMap special_warning_codes; // Containds a list of special warnings

	public BaseWarning() {
		this.code = "";
		this.descriptions = new ArrayList();
		this.special_warning_codes = new HashMap();
		this.special_warning_codes.put("S",new Integer(2));
		this.special_warning_codes.put("R",new Integer(2));
		this.special_warning_codes.put("U",new Integer(2));
		this.special_warning_codes.put("D",new Integer(2));
		this.special_warning_codes.put("W",new Integer(3));
	}

	public void disable()
	{
		setCode("");
	}

	public boolean warning_presence()
	{
		return (!code.equals("") && descriptions.size()>0);
	}

	public boolean is_special()
	{
		return special_warning_codes.containsKey(code);
	}

	/**
	 * @return
	 */
	public String getCode() {
		return code;
	}

	/**
	 * @return
	 */
	public ArrayList getDescriptions() {
		return descriptions;
	}

	/**
	 * @param string
	 */
	public void setCode(String string) {
		code = string;
	}

	/**
	 * @param collection
	 */
	public void setDescriptions(ArrayList collection) {
		descriptions = collection;
	}
	
	public void addDescription(String warning) {
		descriptions.add(warning);
	}

	public int getPriority()
	{
		return ((Integer) special_warning_codes.get(getCode())).intValue();
	}

}
