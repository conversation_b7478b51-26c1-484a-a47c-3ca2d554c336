<?xml version='1.0'?>

<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" policy="internet">
<route appOwner="GWC" serviceName="ADALYAListaProdottiFinR" active="true"/>

    <dispenser>

        <road>

            <request>

                <input>

                    <param name="INP-BANCA"/>

                    <param name="INP-CONV-TIPO"/>

                    <param name="INP-CONV-COD"/>

                    <param name="INP-ATTRIB-01"/>

                    <param name="INP-ATTRIB-02"/>

                    <param name="INP-ATTRIB-03"/>

                    <param name="INP-ATTRIB-04"/>

                    <param name="INP-ATTRIB-05"/>

                    <param name="INP-ATTRIB-06"/>

                    <param name="INP-ATTRIB-07"/>

                    <param name="INP-ATTRIB-08"/>

                    <param name="INP-ATTRIB-09"/>

                    <param name="INP-ATTRIB-10"/>

                    <param name="INP-ATTRIB-11"/>

                    <param name="INP-ATTRIB-12"/>

                    <param name="INP-ATTRIB-13"/>

                    <param name="INP-ATTRIB-14"/>

                    <param name="INP-ATTRIB-15"/>

                    <param name="INP-VALORE-DA-01"/>

                    <param name="INP-VALORE-DA-02"/>

                    <param name="INP-VALORE-DA-03"/>

                    <param name="INP-VALORE-DA-04"/>

                    <param name="INP-VALORE-DA-05"/>

                    <param name="INP-VALORE-DA-06"/>

                    <param name="INP-VALORE-DA-07"/>

                    <param name="INP-VALORE-DA-08"/>

                    <param name="INP-VALORE-DA-09"/>

                    <param name="INP-VALORE-DA-10"/>

                    <param name="INP-VALORE-DA-11"/>

                    <param name="INP-VALORE-DA-12"/>

                    <param name="INP-VALORE-DA-13"/>

                    <param name="INP-VALORE-DA-14"/>

                    <param name="INP-VALORE-DA-15"/>

                    <param name="INP-VALORE-A-01"/>

                    <param name="INP-VALORE-A-02"/>

                    <param name="INP-VALORE-A-03"/>

                    <param name="INP-VALORE-A-04"/>

                    <param name="INP-VALORE-A-05"/>

                    <param name="INP-VALORE-A-06"/>

                    <param name="INP-VALORE-A-07"/>

                    <param name="INP-VALORE-A-08"/>

                    <param name="INP-VALORE-A-09"/>

                    <param name="INP-VALORE-A-10"/>

                    <param name="INP-VALORE-A-11"/>

                    <param name="INP-VALORE-A-12"/>

                    <param name="INP-VALORE-A-13"/>

                    <param name="INP-VALORE-A-14"/>

                    <param name="INP-VALORE-A-15"/>

                    <param name="SW-DEBUG"/>

                    <param name="STATO"/>
			
			<param name="REC-DA-SCARTARE"/>
			
			<param name="FILIALE-UTENTE"/>



			

	

                </input>

                <output>

                    <xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:xgen="http://namespaces.uniteam.it/xmlgenerator/request" xmlns:myxsl="http://www.w3.org/1999/XSL/Transform" version="1.0">

                        <xsl:output method="xml"/>

                        <xsl:template match="/">

                            <XP0C030>

                                <ROCH-03-HEADER>

                                    <ROCH-03-STRUCID>ROCH</ROCH-03-STRUCID>

                                    <ROCH-03-VERSION>0003</ROCH-03-VERSION>

                                    <ROCH-03-BSNAME>RBS-XX-XP0-LISTA-PRODOTTI</ROCH-03-BSNAME>

                                    <ROCH-03-RETURNCODE>0000</ROCH-03-RETURNCODE>

                                    <ROCH-03-UOWCONTROL>0000</ROCH-03-UOWCONTROL>

                                    <ROCH-03-ABEND-CODE/>

                                    <ROCH-03-MSG-LENGHT/>

                                    <ROCH-03-MSG-FORMAT/>

                                    <ROCH-03-REQID/>

                                    <ROCH-03-ERROR-MSG/>

                                    <ROCH-03-TOKEN/>

                                </ROCH-03-HEADER>

                                <XP0R30-INPUT>

                                    <XP0R30-INP-BANCA>

                                        <xsl:value-of select="xgen:request/xgen:param[@name='INP-BANCA']/text()"/>

                                    </XP0R30-INP-BANCA>

                                    <XP0R30-INP-CONV>

                                        <XP0R30-INP-CONV-TIPO>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-CONV-TIPO']/text()"/>

                                        </XP0R30-INP-CONV-TIPO>

                                        <XP0R30-INP-CONV-COD>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-CONV-COD']/text()"/>

                                        </XP0R30-INP-CONV-COD>

                                    </XP0R30-INP-CONV>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-01']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-01']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-01']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-02']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-02']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-02']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-03']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-03']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-03']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-04']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-04']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-04']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-05']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-05']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-05']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-06']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-06']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-06']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-07']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-07']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-07']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-08']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-08']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-08']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-09']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-09']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-09']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-10']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-10']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-10']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-11']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-11']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-11']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-12']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-12']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-12']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-13']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-13']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-13']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-14']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-14']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-14']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-INP-FILTRI>

                                        <XP0R30-INP-ATTRIB>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-ATTRIB-15']/text()"/>

                                        </XP0R30-INP-ATTRIB>

                                        <XP0R30-INP-VALORE-DA>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-DA-15']/text()"/>

                                        </XP0R30-INP-VALORE-DA>

                                        <XP0R30-INP-VALORE-A>

                                            <xsl:value-of select="xgen:request/xgen:param[@name='INP-VALORE-A-15']/text()"/>

                                        </XP0R30-INP-VALORE-A>

                                    </XP0R30-INP-FILTRI>

                                    <XP0R30-SW-DEBUG>

                                        <xsl:value-of select="xgen:request/xgen:param[@name='SW-DEBUG']/text()"/>

                                    </XP0R30-SW-DEBUG>

                                    <XP0R30-STATO>

                                        <xsl:value-of select="xgen:request/xgen:param[@name='STATO']/text()"/>

                                    </XP0R30-STATO>
							
						
						<XP0R30-REC-DA-SCARTARE>
									
                                        <xsl:value-of select="xgen:request/xgen:param[@name='REC-DA-SCARTARE']/text()"/>

                                    </XP0R30-REC-DA-SCARTARE>
						

						<XP0R30-FILIALE-UTENTE>
									
                                        <xsl:value-of select="xgen:request/xgen:param[@name='FILIALE-UTENTE']/text()"/>

                                    </XP0R30-FILIALE-UTENTE>

                                </XP0R30-INPUT>

                                <XP0R30-OUTPUT>

                                    <XP0R30-RC>1</XP0R30-RC>

                                    <XP0R30-MESS>_XP0R30-MESS_</XP0R30-MESS>

                                    <XP0R30-OUT-NUM-ELE>1</XP0R30-OUT-NUM-ELE>

						

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

                                    <XP0R30-OUT-PROD>

                                        <XP0R30-OUT-PRODOTTO>_SZ0</XP0R30-OUT-PRODOTTO>

                                        <XP0R30-OUT-PROD-DESC>_XP0R30-OUT-PROD-DESC_</XP0R30-OUT-PROD-DESC>

                                    </XP0R30-OUT-PROD>

						<XP0R30-SW-DA-RIC>1</XP0R30-SW-DA-RIC>


                                </XP0R30-OUTPUT>

                            </XP0C030>

                        </xsl:template>

                    </xsl:stylesheet>

                </output>

            </request>

            <response>

                <input/>

                <output/>

            </response>

        </road>

        <providerclass>it.usi.webfactory.provider.RomaProvider</providerclass>

    </dispenser>

    <provider>

        <protocolclass/>

        <channelclass>it.usi.webfactory.channels.RomaStandardChannel</channelclass>

    </provider>

    <protocol>

        <bridge>

            <request>

                <input/>

                <output/>

            </request>

            <response>

                <input/>

                <output/>

            </response>

        </bridge>

        <params/>

    </protocol>

    <channel>

        <params>

            <client>Client_SZ0_XX</client>

            <service>RBS-XX-XP0-LISTA-PRODOTTI</service>

            <format>XP0C030-XML</format>

            <timeout>999000</timeout>

        </params>

    </channel>

</service>