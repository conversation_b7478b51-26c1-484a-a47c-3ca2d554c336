<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:apachesoap="http://xml.apache.org/xml-soap">
  <wsdl:types>
    <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:apachesoap="http://xml.apache.org/xml-soap" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
   <element name="callService">
    <complexType>
     <sequence>
      <element name="service" nillable="true" type="xsd:string"/>
      <element name="params" nillable="true" type="xsd:string"/>
      <element name="sep1" nillable="true" type="xsd:string"/>
      <element name="sep2" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="callServiceResponse">
    <complexType>
     <sequence>
      <element name="callServiceReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
  </wsdl:types>
  <wsdl:message name="callServiceRequest">
    <wsdl:part name="parameters" element="intf:callService"/>
  </wsdl:message>
  <wsdl:message name="callServiceResponse">
    <wsdl:part name="parameters" element="intf:callServiceResponse"/>
  </wsdl:message>
  <wsdl:portType name="CallService">
    <wsdl:operation name="callService">
      <wsdl:input name="callServiceRequest" message="intf:callServiceRequest"/>
      <wsdl:output name="callServiceResponse" message="intf:callServiceResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="CallServiceSoapBinding" type="intf:CallService">
    <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="callService">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="callServiceRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="callServiceResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="CallServiceService">
    <wsdl:port name="CallService" binding="intf:CallServiceSoapBinding">
      <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/CallService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
