<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Autorizzazione_Negativa">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x15" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_Autorizzazione_NegativaResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_Autorizzazione_NegativaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_Autorizzazione_NegativaRequest">

      <wsdl:part element="impl:nprFidi_Autorizzazione_Negativa" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_Autorizzazione_NegativaResponse">

      <wsdl:part element="impl:nprFidi_Autorizzazione_NegativaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Autorizzazione_Negativa_SEI">

      <wsdl:operation name="nprFidi_Autorizzazione_Negativa">

         <wsdl:input message="impl:nprFidi_Autorizzazione_NegativaRequest" name="nprFidi_Autorizzazione_NegativaRequest"/>

         <wsdl:output message="impl:nprFidi_Autorizzazione_NegativaResponse" name="nprFidi_Autorizzazione_NegativaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_Autorizzazione_NegativaSoapBinding" type="impl:NPR_Fidi_Autorizzazione_Negativa_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Autorizzazione_Negativa">

         <wsdlsoap:operation soapAction="nprFidi_Autorizzazione_Negativa"/>

         <wsdl:input name="nprFidi_Autorizzazione_NegativaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_Autorizzazione_NegativaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_Autorizzazione_NegativaService">

      <wsdl:port binding="impl:NPR_Fidi_Autorizzazione_NegativaSoapBinding" name="NPR_Fidi_Autorizzazione_Negativa">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Autorizzazione_Negativa"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
