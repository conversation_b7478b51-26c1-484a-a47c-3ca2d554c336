package it.usi.xframe.gwc.pfstruts.actions;

import it.usi.xframe.gwc.wsutil.CallServiceFreeProxy;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class TestCallServiceFree extends TestCallServiceBase {

	private Log logger = LogFactory.getLog(this.getClass());

	// Execute Implementation
	public ActionForward execute(
		ActionMapping mapping,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {

		logger.info("executing = [" + this.getClass() + "]");

		request.getSession().setAttribute("testMode", "FREE");
		
		String endPoint = getLocatorString(request, "testCallServiceFree.do", "CallServiceFree");		
		logger.info(this.getClass() + " Web Service URL = " + endPoint);

		CallServiceFreeProxy proxy = new CallServiceFreeProxy();
		proxy.setEndpoint(endPoint);
		
		request.getSession().setAttribute("CallServiceFreeProxyid", proxy);

		return mapping.findForward("ok");
	}
	
}
