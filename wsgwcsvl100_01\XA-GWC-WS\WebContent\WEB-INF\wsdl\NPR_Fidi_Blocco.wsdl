<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Blocco">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
      <element name="x06" nillable="true" type="xsd:string"/>
      <element name="x07" nillable="true" type="xsd:string"/>
      <element name="x08" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_BloccoResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_BloccoReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_BloccoRequest">

      <wsdl:part element="impl:nprFidi_Blocco" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_BloccoResponse">

      <wsdl:part element="impl:nprFidi_BloccoResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Blocco_SEI">

      <wsdl:operation name="nprFidi_Blocco">

         <wsdl:input message="impl:nprFidi_BloccoRequest" name="nprFidi_BloccoRequest"/>

         <wsdl:output message="impl:nprFidi_BloccoResponse" name="nprFidi_BloccoResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_BloccoSoapBinding" type="impl:NPR_Fidi_Blocco_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Blocco">

         <wsdlsoap:operation soapAction="nprFidi_Blocco"/>

         <wsdl:input name="nprFidi_BloccoRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_BloccoResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_BloccoService">

      <wsdl:port binding="impl:NPR_Fidi_BloccoSoapBinding" name="NPR_Fidi_Blocco">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Blocco"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
