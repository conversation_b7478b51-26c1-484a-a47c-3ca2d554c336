<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE webservicesclient PUBLIC "-//IBM Corporation, Inc.//DTD J2EE Web services client 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_web_services_client_1_0.dtd">

   <webservicesclient id="WebServicesClient_1284038579735">
      <service-ref id="ServiceRef_1284038579735">
         <description>WSDL Service CallServiceService</description>
         <service-ref-name>service/CallServiceService</service-ref-name>
         <service-interface>it.usi.xframe.gwc.wsutil.CallServiceService</service-interface>
         <wsdl-file>WEB-INF/wsdl/CallService.wsdl</wsdl-file>
         <jaxrpc-mapping-file>WEB-INF/CallService_mapping.xml</jaxrpc-mapping-file>
         <service-qname id="ServiceQname_1284038579735">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceService</localpart>
         </service-qname>
         <port-component-ref id="PortComponentRef_1284038579735">
            <service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallService</service-endpoint-interface>
         </port-component-ref>
         <handler id="Handler_1284038579735">
            <handler-name>CallService.SoapRequestResponseHandler</handler-name>
            <handler-class>it.usi.xframe.gwc.wsutil.SoapRequestResponseHandler</handler-class>
         </handler>
      </service-ref>
      <service-ref id="ServiceRef_1284378909239">
         <description>WSDL Service CallServiceTokenService</description>
         <service-ref-name>service/CallServiceTokenService</service-ref-name>
         <service-interface>it.usi.xframe.gwc.wsutil.CallServiceTokenService</service-interface>
         <wsdl-file>WEB-INF/wsdl/CallServiceToken.wsdl</wsdl-file>
         <jaxrpc-mapping-file>WEB-INF/CallServiceToken_mapping.xml</jaxrpc-mapping-file>
         <service-qname id="ServiceQname_1284378909239">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceTokenService</localpart>
         </service-qname>
         <port-component-ref id="PortComponentRef_1284378909239">
            <service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallServiceToken</service-endpoint-interface>
         </port-component-ref>
         <handler id="Handler_1284467999028">
            <handler-name>CallServiceToken.SoapRequestResponseHandler</handler-name>
            <handler-class>it.usi.xframe.gwc.wsutil.SoapRequestResponseHandler</handler-class>
         </handler>
      </service-ref>
      <service-ref id="ServiceRef_1284544347198">
         <description>WSDL Service CallServiceFreeService</description>
         <service-ref-name>service/CallServiceFreeService</service-ref-name>
         <service-interface>it.usi.xframe.gwc.wsutil.CallServiceFreeService</service-interface>
         <wsdl-file>WEB-INF/wsdl/CallServiceFree.wsdl</wsdl-file>
         <jaxrpc-mapping-file>WEB-INF/CallServiceFree_mapping.xml</jaxrpc-mapping-file>
         <service-qname id="ServiceQname_1284544347198">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>CallServiceFreeService</localpart>
         </service-qname>
         <port-component-ref id="PortComponentRef_1284544474404">
            <service-endpoint-interface>it.usi.xframe.gwc.wsutil.CallServiceFree</service-endpoint-interface>
         </port-component-ref>
         <handler id="Handler_1284544474404">
            <handler-name>CallServiceFree.SoapFreeRequestResponseHandler</handler-name>
            <handler-class>it.usi.xframe.gwc.wsutil.SoapFreeRequestResponseHandler</handler-class>
         </handler>
      </service-ref>
   </webservicesclient>
