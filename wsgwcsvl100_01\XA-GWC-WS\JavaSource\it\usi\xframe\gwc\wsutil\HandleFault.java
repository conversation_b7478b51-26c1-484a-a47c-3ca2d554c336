/**
 * HandleFault.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * cf190823.02 v62608112801
 */

package it.usi.xframe.gwc.wsutil;

public class HandleFault  implements java.io.Serializable {
    private java.lang.Object arg_0_1;
    private java.lang.Object smc;

    public HandleFault() {
    }

    public java.lang.Object getArg_0_1() {
        return arg_0_1;
    }

    public void setArg_0_1(java.lang.Object arg_0_1) {
        this.arg_0_1 = arg_0_1;
    }

    public java.lang.Object getSmc() {
        return smc;
    }

    public void setSmc(java.lang.Object smc) {
        this.smc = smc;
    }

    private transient java.lang.ThreadLocal __history;
    public boolean equals(java.lang.Object obj) {
        if (obj == null) { return false; }
        if (obj.getClass() != this.getClass()) { return false;}
        HandleFault other = (HandleFault) obj;
        boolean _equals;
        _equals = true
            && ((this.arg_0_1==null && other.getArg_0_1()==null) || 
             (this.arg_0_1!=null &&
              this.arg_0_1.equals(other.getArg_0_1())))
            && ((this.smc==null && other.getSmc()==null) || 
             (this.smc!=null &&
              this.smc.equals(other.getSmc())));
        if (!_equals) { return false; }
        if (__history == null) {
            synchronized (this) {
                if (__history == null) {
                    __history = new java.lang.ThreadLocal();
                }
            }
        }
        HandleFault history = (HandleFault) __history.get();
        if (history != null) { return (history == obj); }
        if (this == obj) return true;
        __history.set(obj);
        __history.set(null);
        return true;
    }

    private transient java.lang.ThreadLocal __hashHistory;
    public int hashCode() {
        if (__hashHistory == null) {
            synchronized (this) {
                if (__hashHistory == null) {
                    __hashHistory = new java.lang.ThreadLocal();
                }
            }
        }
        HandleFault history = (HandleFault) __hashHistory.get();
        if (history != null) { return 0; }
        __hashHistory.set(this);
        int _hashCode = 1;
        if (getArg_0_1() != null) {
            _hashCode += getArg_0_1().hashCode();
        }
        if (getSmc() != null) {
            _hashCode += getSmc().hashCode();
        }
        __hashHistory.set(null);
        return _hashCode;
    }

}
