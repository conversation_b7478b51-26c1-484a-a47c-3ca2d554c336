package it.usi.xframe.gwc.pfstruts.actions;

import it.usi.xframe.gwc.wsutil.CallServiceTokenProxy;
import it.usi.xframe.ifg.bfutil.IfgServiceFactory;
import it.usi.xframe.ifg.bfutil.wpro.UserData;
import it.usi.xframe.system.errors.XFRSevereException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.struts.action.ActionForm;
import org.apache.struts.action.ActionForward;
import org.apache.struts.action.ActionMapping;

/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class TestCallServiceToken extends TestCallServiceBase {

	private Log logger = LogFactory.getLog(this.getClass());

	public static final String LOGIN_ERROR = "error.login";
	public static final String REQUESTPARAM_NOTVALID = "error.params.invalid";

	private Object[] getArrayFromString(String s) {
		Object[] a = new Object[1];
		if (s==null) s="???";
		a[0] = s; 
		return a;
	}

	// Execute Implementation
	public ActionForward execute(
		ActionMapping mapping,
		ActionForm form,
		HttpServletRequest request,
		HttpServletResponse response)
		throws Exception {

		logger.info("executing = [" + this.getClass() + "]");

		UserData userData;

		try {
			userData = IfgServiceFactory.getInstance().getIfgServiceFacade().getUserInfo();
		} catch (Exception e) {
			throw new XFRSevereException(LOGIN_ERROR, getArrayFromString(e.getMessage()));
		}

		if (userData == null)
			throw new XFRSevereException(REQUESTPARAM_NOTVALID, getArrayFromString("[userData]"));

		logger.info("Utente = [" + userData.getCodOperatore() + "]");

		String localForward = "ok";
		String path = mapping.findForward(localForward).getPath();

		path = path.replaceFirst("var_user", userData.getCodOperatore());
		logger.info("executing " + this.getClass() + " path = [" + path + "]");

		ActionForward forward = new ActionForward();
		
		forward.setName(mapping.findForward(localForward).getName());
		forward.setRedirect(mapping.findForward(localForward).getRedirect());
		forward.setPath(path);

		request.getSession().setAttribute("testMode", "TOKEN");

		String endPoint = getLocatorString(request, "testCallServiceToken.do", "CallServiceToken");		
		logger.info(this.getClass() + " Web Service URL = " + endPoint);

		CallServiceTokenProxy proxy = new CallServiceTokenProxy();
		proxy.setEndpoint(endPoint);
		
		request.getSession().setAttribute("CallServiceTokenProxyid", proxy);

		return forward;		
	}
	
}
