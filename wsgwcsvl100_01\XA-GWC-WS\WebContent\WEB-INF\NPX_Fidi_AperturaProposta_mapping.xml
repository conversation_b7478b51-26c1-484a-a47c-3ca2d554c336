<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1412077352032">
      <package-mapping id="PackageMapping_1412077352032">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1412077352032">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1412077352032">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1412077352032">
         <service-interface>it.usi.xframe.gwc.wsutil.NPX_Fidi_AperturaPropostaService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1412077352032">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPX_Fidi_AperturaPropostaService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1412077352032">
            <port-name>NPX_Fidi_AperturaProposta</port-name>
            <java-port-name>NPX_Fidi_AperturaProposta</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1412077352032">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPX_Fidi_AperturaProposta_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1412077352032">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPX_Fidi_AperturaProposta_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1412077352032">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPX_Fidi_AperturaPropostaSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1412077352032">
            <java-method-name>npxFidi_AperturaProposta</java-method-name>
            <wsdl-operation>npxFidi_AperturaProposta</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1412077352032">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1412077352032">
                  <wsdl-message id="WSDLMessage_1412077352032">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>npxFidi_AperturaPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x00</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1412077352033">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1412077352033">
                  <wsdl-message id="WSDLMessage_1412077352033">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>npxFidi_AperturaPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x01</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1412077352034">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1412077352034">
                  <wsdl-message id="WSDLMessage_1412077352034">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>npxFidi_AperturaPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x02</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1412077352035">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1412077352035">
                  <wsdl-message id="WSDLMessage_1412077352035">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>npxFidi_AperturaPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x03</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1412077352036">
               <param-position>4</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1412077352036">
                  <wsdl-message id="WSDLMessage_1412077352036">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>npxFidi_AperturaPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x05</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1412077352037">
               <param-position>5</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1412077352037">
                  <wsdl-message id="WSDLMessage_1412077352037">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>npxFidi_AperturaPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x06</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1412077352038">
               <param-position>6</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1412077352038">
                  <wsdl-message id="WSDLMessage_1412077352038">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>npxFidi_AperturaPropostaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x08</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1412077352032">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1412077352039">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>npxFidi_AperturaPropostaResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>npxFidi_AperturaPropostaReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
