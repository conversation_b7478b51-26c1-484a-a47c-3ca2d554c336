<?xml version="1.0" encoding="UTF-8"?>






<!DOCTYPE taglib PUBLIC "-//Sun Microsystems, Inc.//DTD JSP Tag Library 1.1//EN" "http://java.sun.com/j2ee/dtds/web-jsptaglibrary_1_1.dtd">
<taglib>
  <tlibversion>1.0.1</tlibversion>
  <jspversion>1.1</jspversion>
  <shortname>string</shortname>
  <uri>http://jakarta.apache.org/taglibs/string-1.0.1</uri>
  <info>
    The String taglibrary provides a host of tags for manipulating 
    java.lang.Strings. The style is that the String to act upon is the 
    body of the tag, and attributes are used as parameters for the 
    manipulation.
  </info>
  <tag>
    <name>capitalize</name>
    <tagclass>org.apache.taglibs.string.CapitalizeTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>uncapitalize</name>
    <tagclass>org.apache.taglibs.string.UncapitalizeTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>upperCase</name>
    <tagclass>org.apache.taglibs.string.UpperCaseTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>lowerCase</name>
    <tagclass>org.apache.taglibs.string.LowerCaseTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>trim</name>
    <tagclass>org.apache.taglibs.string.TrimTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>chop</name>
    <tagclass>org.apache.taglibs.string.ChopTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>chopNewline</name>
    <tagclass>org.apache.taglibs.string.ChopNewlineTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>escape</name>
    <tagclass>org.apache.taglibs.string.EscapeTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>reverse</name>
    <tagclass>org.apache.taglibs.string.ReverseTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>swapCase</name>
    <tagclass>org.apache.taglibs.string.SwapCaseTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>soundex</name>
    <tagclass>org.apache.taglibs.string.SoundexTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>metaphone</name>
    <tagclass>org.apache.taglibs.string.MetaphoneTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>quoteRegexp</name>
    <tagclass>org.apache.taglibs.string.QuoteRegexpTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>capitalizeAllWords</name>
    <tagclass>org.apache.taglibs.string.CapitalizeAllWordsTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>removeXml</name>
    <tagclass>org.apache.taglibs.string.RemoveXmlTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>encodeUrl</name>
    <tagclass>org.apache.taglibs.string.EncodeUrlTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>decodeUrl</name>
    <tagclass>org.apache.taglibs.string.DecodeUrlTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>count</name>
    <tagclass>org.apache.taglibs.string.CountTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>set</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>delete</name>
    <tagclass>org.apache.taglibs.string.DeleteTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>set</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>squeeze</name>
    <tagclass>org.apache.taglibs.string.SqueezeTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>set</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>center</name>
    <tagclass>org.apache.taglibs.string.CenterTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>width</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>rightPad</name>
    <tagclass>org.apache.taglibs.string.RightPadTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>width</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>leftPad</name>
    <tagclass>org.apache.taglibs.string.LeftPadTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>width</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>chomp</name>
    <tagclass>org.apache.taglibs.string.ChompTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>getChomp</name>
    <tagclass>org.apache.taglibs.string.GetChompTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>prechomp</name>
    <tagclass>org.apache.taglibs.string.PrechompTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>getPrechomp</name>
    <tagclass>org.apache.taglibs.string.GetPrechompTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>strip</name>
    <tagclass>org.apache.taglibs.string.StripTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>stripEnd</name>
    <tagclass>org.apache.taglibs.string.StripEndTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>stripStart</name>
    <tagclass>org.apache.taglibs.string.StripStartTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>reverseDelimitedString</name>
    <tagclass>org.apache.taglibs.string.ReverseDelimitedStringTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>overlay</name>
    <tagclass>org.apache.taglibs.string.OverlayTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>with</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>start</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>end</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>substring</name>
    <tagclass>org.apache.taglibs.string.SubstringTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>start</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>end</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>repeat</name>
    <tagclass>org.apache.taglibs.string.RepeatTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>count</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>wordWrap</name>
    <tagclass>org.apache.taglibs.string.WordWrapTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>width</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>delimiter</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>split</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>nestedString</name>
    <tagclass>org.apache.taglibs.string.NestedStringTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>open</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>close</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>countMatches</name>
    <tagclass>org.apache.taglibs.string.CountMatchesTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>substring</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>default</name>
    <tagclass>org.apache.taglibs.string.DefaultTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>value</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>default</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>replace</name>
    <tagclass>org.apache.taglibs.string.ReplaceTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>replace</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>with</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>count</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>newlineToken</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>carriageToken</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>randomString</name>
    <tagclass>org.apache.taglibs.string.RandomStringTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>count</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>start</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>end</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>type</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>left</name>
    <tagclass>org.apache.taglibs.string.LeftTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>count</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>right</name>
    <tagclass>org.apache.taglibs.string.RightTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>count</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>mid</name>
    <tagclass>org.apache.taglibs.string.MidTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>start</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>count</name>
      <required>true</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
  <tag>
    <name>truncateNicely</name>
    <tagclass>org.apache.taglibs.string.TruncateNicelyTag</tagclass>
    <bodycontent>JSP</bodycontent>
    <attribute>
      <name>var</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>lower</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>upper</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
    <attribute>
      <name>appendToEnd</name>
      <required>false</required>
      <rtexprvalue>true</rtexprvalue>
    </attribute>
  </tag>
</taglib>






