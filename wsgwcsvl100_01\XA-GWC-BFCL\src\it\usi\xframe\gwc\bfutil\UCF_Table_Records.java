package it.usi.xframe.gwc.bfutil;

import java.io.Serializable;
import java.util.HashMap;

public class UCF_Table_Records implements Serializable {

	HashMap table_records = new HashMap();
	HashMap table_columns = new HashMap();
		
	public UCF_Table_Records() 
	{	}

	/**
	 * @return
	 */
	public HashMap getTable_columns() {
		return table_columns;
	}

	/**
	 * @return
	 */
	public HashMap getTable_records() {
		return table_records;
	}

	/**
	 * @param map
	 */
	public void setTable_columns(HashMap map) {
		table_columns = map;
	}

	/**
	 * @param map
	 */
	public void setTable_records(HashMap map) {
		table_records = map;
	}

}