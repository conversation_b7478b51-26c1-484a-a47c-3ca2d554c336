package it.usi.xframe.gwc.pfstruts.actions;

import it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade;
import it.usi.xframe.gwc.bfutil.GwcServiceFactory;
import it.usi.xframe.gwc.bfutil.base.BaseCabinBag;
import it.usi.xframe.gwc.bfutil.base.BaseDataStorage;
import it.usi.xframe.gwc.bfutil.base.BaseResponseClass;
import it.usi.xframe.gwc.bfutil.base.BaseStandardAction;
import it.usi.xframe.system.errors.XFRException;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import org.apache.struts.action.ActionForm;

/**
 * @version 	1.0
 * <AUTHOR>
 */
public class GaussCallAction extends BaseStandardAction {

	// Gauss 1
	private static String BIGDECIMALNULLVALUE9_9 = "-999999999.999999999"; 
	private static String BIGDECIMALNULLVALUE6_9 = "-999999.999999999"; 
	private static String BIGDECIMALNULLVALUE1_6 = "-9.999999999"; 

	// Gauss 2
	private static String BIGDECIMALNULLVALUE9_9_GAUSS2 = "-999999999,999999999"; 
	private static String BIGDECIMALNULLVALUE6_9_GAUSS2 = "-999999,999999999"; 
	private static String BIGDECIMALNULLVALUE1_6_GAUSS2 = "-9,999999999"; 

	private String formatDate(String s)
	{
		return s; 
	}

	private String formatNumber(String s, String fmt, String gauss_type)
	{
		if ("1".equals(gauss_type))
			if (fmt.equals("19") && s.equals("")) 
				return BIGDECIMALNULLVALUE9_9;
			else if (fmt.equals("16") && s.equals(""))
				return BIGDECIMALNULLVALUE6_9;
			else if (fmt.equals("8") && s.equals(""))
				return BIGDECIMALNULLVALUE1_6;
				
		if ("2".equals(gauss_type))
			if (fmt.equals("19") && s.equals("")) 
				return BIGDECIMALNULLVALUE9_9_GAUSS2;
			else if (fmt.equals("16") && s.equals(""))
				return BIGDECIMALNULLVALUE6_9_GAUSS2;
			else if (fmt.equals("8") && s.equals(""))
				return BIGDECIMALNULLVALUE1_6_GAUSS2;

		return s;
	}
	
	private String formatString(String s)
	{
		String out = s.trim();
		return out; 
	}

	public BaseResponseClass buildResponseClass(
		BaseCabinBag cabinBag,
		BaseDataStorage dataStorage,
		ActionForm form,
		HttpServletRequest inRequest)
		throws Exception {
		
		String serviceName = inRequest.getParameter("serviceName");
		Map params = new HashMap();
		Map map = inRequest.getParameterMap();
		System.out.println("Params # = " + map.size());
		System.out.println("Service Name = " + serviceName);

		Set set = map.keySet();
		
		String gauss_type = inRequest.getParameter("gauss_type");
		
		Iterator iter = set.iterator();
		while(iter.hasNext())
		{
			String key = (String) iter.next();
			String value = (String) inRequest.getParameter(key);
			System.out.println("\tParams[" + key + "]=" + value);	
			
			if (key.endsWith("_formato") || key.endsWith("_len") 
				|| key.equals("appLang")
				|| key.equals("CALL")
				|| key.equals("transaction")
				|| key.equals("serviceName")
				|| key.equals("service")
				|| key.equals("gauss_type")
			) continue;
			
			String formato = (String) inRequest.getParameter(key+"_formato");
			String len = (String) inRequest.getParameter(key+"_len");	
			
			if (formato.equals("A") && len.equals("10")) // Date
				value = formatDate(value);
			else if (formato.equals("N")) // Number
				value = formatNumber(value,len,gauss_type);
			else  // String
				value = formatString(value);
				
			params.put(key,value);
		}
		
		GwcServiceFactory factory = GwcServiceFactory.getInstance();
		IGwcMainServiceFacade facade = factory.getGwcMainServiceFacade();
		try {
			String xml = facade.call_gauss(serviceName, params);
			inRequest.setAttribute("xml",xml);
		} catch (Exception e) {	throw new XFRException(e); }
		finally { 	factory.dispose(facade);	}
			
		return null;
	}


}