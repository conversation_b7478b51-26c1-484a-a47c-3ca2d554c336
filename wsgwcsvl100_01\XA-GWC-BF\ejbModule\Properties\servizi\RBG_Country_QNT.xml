<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="R24I-FUNZIONE"/>
					<param name="R24I-COD-PRATICA"/>
					<param name="R24I-MATRICOLA"/>
					<param name="R24I-LINK"/>
					<param name="R24I-ACC-BAL-M"/>
					<param name="R24I-ACC-BAL-A"/>
					<param name="R24I-ACC-BAL-C"/>
					<param name="R24I-ACC-BAL-CM"/>
					<param name="R24I-ACC-BAL-T"/>
					<param name="R24I-NOM-GDP-M"/>
					<param name="R24I-NOM-GDP-A"/>
					<param name="R24I-NOM-GDP-C"/>
					<param name="R24I-NOM-GDP-CM"/>
					<param name="R24I-NOM-GDP-T"/>
					<param name="R24I-NOM-GDP-1"/>
					<param name="R24I-GDP-HEAD-M"/>
					<param name="R24I-GDP-HEAD-A"/>
					<param name="R24I-GDP-HEAD-C"/>
					<param name="R24I-GDP-HEAD-CM"/>
					<param name="R24I-GDP-HEAD-T"/>
					<param name="R24I-INT-MARK-M"/>
					<param name="R24I-INT-MARK-A"/>
					<param name="R24I-INT-MARK-C"/>
					<param name="R24I-INT-MARK-CM"/>
					<param name="R24I-INT-MARK-T"/>
					<param name="R24I-DEB-PUBL-M"/>
					<param name="R24I-DEB-PUBL-A"/>
					<param name="R24I-DEB-PUBL-C"/>
					<param name="R24I-DEB-PUBL-CM"/>
					<param name="R24I-DEB-PUBL-T"/>
					<param name="R24I-ASSET-T1-M"/>
					<param name="R24I-ASSET-T1-A"/>
					<param name="R24I-ASSET-T1-C"/>
					<param name="R24I-ASSET-T1-CM"/>
					<param name="R24I-ASSET-T1-T"/>
					<param name="R24I-ASSET-T0-M"/>
					<param name="R24I-ASSET-T0-A"/>
					<param name="R24I-ASSET-T0-C"/>
					<param name="R24I-ASSET-T0-CM"/>
					<param name="R24I-ASSET-T0-T"/>
					<param name="R24I-DATA-SOURCE-C"/>
					<param name="R24I-DATA-SOURCE-CM"/>
					<param name="R24I-GDP-CALC"/>
					<param name="R24I-GDP-HEAD-CALC"/>
					<param name="R24I-INT-MARK-CALC"/>
					<param name="R24I-DEB-PUBL-CALC"/>
					<param name="R24I-ASSET-CALC"/>
					<param name="R24I-TOT-SCORE-CALC"/>
					<param name="R24I-DESC-GDP"/>
					<param name="R24I-REASON-DATA-M"/>
					<param name="R24I-REASON-DATA-A"/>
					<param name="R24I-NOTE"/>
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>RBG024-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>RB24</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>