// Stub class generated by rmic, do not edit.
// Contents subject to change without notice.

package it.usi.xframe.gwc.bfintf;

import it.usi.xframe.gwc.bfutil.params.FieldsParams;
import it.usi.xframe.gwc.bfutil.rc.FieldsResponseClass;
import it.usi.xframe.gwc.bfutil.rc.TransactionResponseClass;
import it.usi.xframe.gwc.bfutil.rc.XmlWebServicesResponseClass;
import it.usi.xframe.gwc.bfutil.vo.BanksList;
import java.io.Serializable;
import java.lang.Exception;
import java.lang.Object;
import java.lang.String;
import java.lang.Throwable;
import java.rmi.Remote;
import java.rmi.UnexpectedException;
import java.util.Map;
import javax.rmi.CORBA.Stub;
import javax.rmi.CORBA.Util;
import org.omg.CORBA.SystemException;
import org.omg.CORBA.portable.ApplicationException;
import org.omg.CORBA.portable.OutputStream;
import org.omg.CORBA.portable.RemarshalException;
import org.omg.CORBA.portable.ServantObject;
import org.omg.CORBA_2_3.portable.InputStream;

public class _IGwcMainServiceFacade_Stub extends Stub implements IGwcMainServiceFacade,
Remote {
    
    private static final String[] _type_ids = {
        "RMI:it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade:****************"
    };
    
    public String[] _ids() { 
        return (String [] )  _type_ids.clone();
    }
    
    public String getFieldFromResponse(String arg0, String arg1, String arg2, int arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("getFieldFromResponse", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_long(arg3);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("getFieldFromResponse",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).getFieldFromResponse(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String callService(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("callService", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("callService",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).callService(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprAnagrafica(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprAnagrafica", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprAnagrafica",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprAnagrafica(arg0, arg1, arg2, arg3, arg4, arg5);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprAnagraficaAce6(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8, String arg9) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprAnagraficaAce6", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        out.write_value(arg9,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprAnagraficaAce6",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprAnagraficaAce6(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprDatiFidiPratica(String arg0, String arg1, String arg2, String arg3, String arg4) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprDatiFidiPratica", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprDatiFidiPratica",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprDatiFidiPratica(arg0, arg1, arg2, arg3, arg4);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprDossier(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8, String arg9) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprDossier", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        out.write_value(arg9,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprDossier",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprDossier(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_AggiornaRiga(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_AggiornaRiga", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_AggiornaRiga",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_AggiornaRiga(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_AnnullamentoProposta(String arg0, String arg1, String arg2, String arg3, String arg4) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_AnnullamentoProposta", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_AnnullamentoProposta",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_AnnullamentoProposta(arg0, arg1, arg2, arg3, arg4);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Autorizzazione(String arg0, String arg1, String arg2, String arg3, String arg4) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Autorizzazione", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Autorizzazione",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Autorizzazione(arg0, arg1, arg2, arg3, arg4);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Autorizzazione_Negativa(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Autorizzazione_Negativa", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Autorizzazione_Negativa",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Autorizzazione_Negativa(arg0, arg1, arg2, arg3, arg4, arg5);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Avocatura(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Avocatura", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Avocatura",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Avocatura(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Blocco(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Blocco", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Blocco",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Blocco(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Cancella(String arg0, String arg1, String arg2, String arg3, String arg4) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Cancella", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Cancella",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Cancella(arg0, arg1, arg2, arg3, arg4);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Completamento(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Completamento", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Completamento",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Completamento(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String npxFidi_Completamento(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("npxFidi_Completamento", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("npxFidi_Completamento",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).npxFidi_Completamento(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Declino(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8, String arg9, String arg10, String arg11) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Declino", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        out.write_value(arg9,String.class);
                        out.write_value(arg10,String.class);
                        out.write_value(arg11,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Declino",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Declino(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Delibera(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Delibera", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Delibera",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Delibera(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_IterProposta(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_IterProposta", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_IterProposta",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_IterProposta(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Logon(String arg0, String arg1, String arg2) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Logon", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Logon",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Logon(arg0, arg1, arg2);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String npxFidi_Logon(String arg0, String arg1, String arg2) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("npxFidi_Logon", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("npxFidi_Logon",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).npxFidi_Logon(arg0, arg1, arg2);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Pennino(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Pennino", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Pennino",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Pennino(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_PreCompletamento(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_PreCompletamento", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_PreCompletamento",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_PreCompletamento(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String npxFidi_PreCompletamento(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("npxFidi_PreCompletamento", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("npxFidi_PreCompletamento",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).npxFidi_PreCompletamento(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Proposta(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Proposta", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Proposta",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Proposta(arg0, arg1, arg2, arg3, arg4, arg5);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_RecuperoNdg(String arg0, String arg1, String arg2) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_RecuperoNdg", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_RecuperoNdg",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_RecuperoNdg(arg0, arg1, arg2);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidiIterPropostaNew(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidiIterPropostaNew", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidiIterPropostaNew",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidiIterPropostaNew(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprSinteticaPGASemplice(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprSinteticaPGASemplice", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprSinteticaPGASemplice",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprSinteticaPGASemplice(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprSinteticaPGA(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprSinteticaPGA", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprSinteticaPGA",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprSinteticaPGA(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprEsitoPGA(String arg0) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprEsitoPGA", true);
                        out.write_value(arg0,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprEsitoPGA",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprEsitoPGA(arg0);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprBigliettoPGA(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8, String arg9, String arg10, String arg11) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprBigliettoPGA", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        out.write_value(arg9,String.class);
                        out.write_value(arg10,String.class);
                        out.write_value(arg11,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprBigliettoPGA",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprBigliettoPGA(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_PerfezionamentoGaranzia(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8, String arg9, String arg10, String arg11, String arg12, String arg13, String arg14, String arg15, String arg16, String arg17, String arg18, String arg19) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_PerfezionamentoGaranzia", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        out.write_value(arg9,String.class);
                        out.write_value(arg10,String.class);
                        out.write_value(arg11,String.class);
                        out.write_value(arg12,String.class);
                        out.write_value(arg13,String.class);
                        out.write_value(arg14,String.class);
                        out.write_value(arg15,String.class);
                        out.write_value(arg16,String.class);
                        out.write_value(arg17,String.class);
                        out.write_value(arg18,String.class);
                        out.write_value(arg19,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_PerfezionamentoGaranzia",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_PerfezionamentoGaranzia(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14, arg15, arg16, arg17, arg18, arg19);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFin_Dettaglio(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFin_Dettaglio", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFin_Dettaglio",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFin_Dettaglio(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFin_Tasso(String arg0, String arg1, String arg2, String arg3, String arg4) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFin_Tasso", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFin_Tasso",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFin_Tasso(arg0, arg1, arg2, arg3, arg4);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprListaFidi(String arg0, String arg1, String arg2, String arg3) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprListaFidi", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprListaFidi",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprListaFidi(arg0, arg1, arg2, arg3);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprLogin(String arg0, String arg1, String arg2, String arg3, String arg4) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprLogin", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprLogin",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprLogin(arg0, arg1, arg2, arg3, arg4);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprPfa(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprPfa", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprPfa",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprPfa(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprRating(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8, String arg9, String arg10) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprRating", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        out.write_value(arg9,String.class);
                        out.write_value(arg10,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprRating",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprRating(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprSemZivno(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8, String arg9, String arg10, String arg11, String arg12, String arg13, String arg14, String arg15, String arg16, String arg17, String arg18, String arg19, String arg20, String arg21, String arg22, String arg23, String arg24, String arg25, String arg26, String arg27, String arg28, String arg29, String arg30, String arg31, String arg32, String arg33, String arg34, String arg35, String arg36, String arg37, String arg38, String arg39, String arg40, String arg41, String arg42, String arg43, String arg44, String arg45, String arg46, String arg47, String arg48, String arg49, String arg50, String arg51, String arg52, String arg53, String arg54, String arg55, String arg56, String arg57, String arg58, String arg59, String arg60) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprSemZivno", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        out.write_value(arg9,String.class);
                        out.write_value(arg10,String.class);
                        out.write_value(arg11,String.class);
                        out.write_value(arg12,String.class);
                        out.write_value(arg13,String.class);
                        out.write_value(arg14,String.class);
                        out.write_value(arg15,String.class);
                        out.write_value(arg16,String.class);
                        out.write_value(arg17,String.class);
                        out.write_value(arg18,String.class);
                        out.write_value(arg19,String.class);
                        out.write_value(arg20,String.class);
                        out.write_value(arg21,String.class);
                        out.write_value(arg22,String.class);
                        out.write_value(arg23,String.class);
                        out.write_value(arg24,String.class);
                        out.write_value(arg25,String.class);
                        out.write_value(arg26,String.class);
                        out.write_value(arg27,String.class);
                        out.write_value(arg28,String.class);
                        out.write_value(arg29,String.class);
                        out.write_value(arg30,String.class);
                        out.write_value(arg31,String.class);
                        out.write_value(arg32,String.class);
                        out.write_value(arg33,String.class);
                        out.write_value(arg34,String.class);
                        out.write_value(arg35,String.class);
                        out.write_value(arg36,String.class);
                        out.write_value(arg37,String.class);
                        out.write_value(arg38,String.class);
                        out.write_value(arg39,String.class);
                        out.write_value(arg40,String.class);
                        out.write_value(arg41,String.class);
                        out.write_value(arg42,String.class);
                        out.write_value(arg43,String.class);
                        out.write_value(arg44,String.class);
                        out.write_value(arg45,String.class);
                        out.write_value(arg46,String.class);
                        out.write_value(arg47,String.class);
                        out.write_value(arg48,String.class);
                        out.write_value(arg49,String.class);
                        out.write_value(arg50,String.class);
                        out.write_value(arg51,String.class);
                        out.write_value(arg52,String.class);
                        out.write_value(arg53,String.class);
                        out.write_value(arg54,String.class);
                        out.write_value(arg55,String.class);
                        out.write_value(arg56,String.class);
                        out.write_value(arg57,String.class);
                        out.write_value(arg58,String.class);
                        out.write_value(arg59,String.class);
                        out.write_value(arg60,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprSemZivno",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprSemZivno(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8, arg9, arg10, arg11, arg12, arg13, arg14, arg15, arg16, arg17, arg18, arg19, arg20, arg21, arg22, arg23, arg24, arg25, arg26, arg27, arg28, arg29, arg30, arg31, arg32, arg33, arg34, arg35, arg36, arg37, arg38, arg39, arg40, arg41, arg42, arg43, arg44, arg45, arg46, arg47, arg48, arg49, arg50, arg51, arg52, arg53, arg54, arg55, arg56, arg57, arg58, arg59, arg60);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String nprFidi_Inquiry_DatoVario(String arg0, String arg1, String arg2, String arg3, String arg4) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("nprFidi_Inquiry_DatoVario", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("nprFidi_Inquiry_DatoVario",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).nprFidi_Inquiry_DatoVario(arg0, arg1, arg2, arg3, arg4);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String npxFidi_AperturaProposta(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("npxFidi_AperturaProposta", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("npxFidi_AperturaProposta",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).npxFidi_AperturaProposta(arg0, arg1, arg2, arg3, arg4, arg5, arg6);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String npxFidi_InserimentoFidi(String arg0, String arg1, String arg2, String arg3, String arg4, String arg5, String arg6, String arg7, String arg8) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("npxFidi_InserimentoFidi", true);
                        out.write_value(arg0,String.class);
                        out.write_value(arg1,String.class);
                        out.write_value(arg2,String.class);
                        out.write_value(arg3,String.class);
                        out.write_value(arg4,String.class);
                        out.write_value(arg5,String.class);
                        out.write_value(arg6,String.class);
                        out.write_value(arg7,String.class);
                        out.write_value(arg8,String.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("npxFidi_InserimentoFidi",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).npxFidi_InserimentoFidi(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7, arg8);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public BanksList getBanks() throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        OutputStream out = _request("getBanks", true);
                        in = (InputStream)_invoke(out);
                        return (BanksList) in.read_value(BanksList.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("getBanks",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    BanksList result = ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).getBanks();
                    return (BanksList)Util.copyObject(result,_orb());
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public boolean UCF_db2_oracle_copy() throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        OutputStream out = _request("UCF_db2_oracle_copy", true);
                        in = (InputStream)_invoke(out);
                        return in.read_boolean();
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("UCF_db2_oracle_copy",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).UCF_db2_oracle_copy();
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public TransactionResponseClass transactions() throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        OutputStream out = _request("transactions", true);
                        in = (InputStream)_invoke(out);
                        return (TransactionResponseClass) in.read_value(TransactionResponseClass.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("transactions",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    TransactionResponseClass result = ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).transactions();
                    return (TransactionResponseClass)Util.copyObject(result,_orb());
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public FieldsResponseClass fields(FieldsParams arg0) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("fields", true);
                        out.write_value(arg0,FieldsParams.class);
                        in = (InputStream)_invoke(out);
                        return (FieldsResponseClass) in.read_value(FieldsResponseClass.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("fields",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    FieldsParams arg0Copy = (FieldsParams) Util.copyObject(arg0,_orb());
                    FieldsResponseClass result = ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).fields(arg0Copy);
                    return (FieldsResponseClass)Util.copyObject(result,_orb());
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public XmlWebServicesResponseClass xmlWebServices() throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        OutputStream out = _request("xmlWebServices", true);
                        in = (InputStream)_invoke(out);
                        return (XmlWebServicesResponseClass) in.read_value(XmlWebServicesResponseClass.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("xmlWebServices",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    XmlWebServicesResponseClass result = ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).xmlWebServices();
                    return (XmlWebServicesResponseClass)Util.copyObject(result,_orb());
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
    
    public String call_gauss(String arg0, Map arg1) throws Exception {
        while(true) {
            if (!Util.isLocal(this)) {
                InputStream in = null;
                try {
                    try {
                        org.omg.CORBA_2_3.portable.OutputStream out = 
                            (org.omg.CORBA_2_3.portable.OutputStream)
                            _request("call_gauss", true);
                        out.write_value(arg0,String.class);
                        out.write_value((Serializable)arg1,Map.class);
                        in = (InputStream)_invoke(out);
                        return (String) in.read_value(String.class);
                    } catch (ApplicationException ex) {
                        in = (InputStream) ex.getInputStream();
                        String id = in.read_string();
                        if (id.equals("IDL:java/lang/Ex:1.0")) {
                            throw (Exception) in.read_value(Exception.class);
                        }
                        throw new UnexpectedException(id);
                    } catch (RemarshalException ex) {
                        continue;
                    }
                } catch (SystemException ex) {
                    throw Util.mapSystemException(ex);
                } finally {
                    _releaseReply(in);
                }
            } else {
                ServantObject so = _servant_preinvoke("call_gauss",it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade.class);
                if (so == null) {
                    continue;
                }
                try {
                    Object[] copies = Util.copyObjects(new Object[]{arg0,arg1},_orb());
                    String arg0Copy = (String) copies[0];
                    Map arg1Copy = (Map) copies[1];
                    return ((it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade)so.servant).call_gauss(arg0Copy, arg1Copy);
                } catch (Throwable ex) {
                    Throwable exCopy = (Throwable)Util.copyObject(ex,_orb());
                    if (exCopy instanceof Exception) {
                        throw (Exception)exCopy;
                    }
                    throw Util.wrapException(exCopy);
                } finally {
                    _servant_postinvoke(so);
                }
            }
        }
    }
}
