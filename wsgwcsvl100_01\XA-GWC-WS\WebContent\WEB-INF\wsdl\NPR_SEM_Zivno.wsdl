<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprSemZivno">
    <complexType>
     <sequence>
      <element name="n001" nillable="true" type="xsd:string"/>
      <element name="n002" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="n003" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="n004" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="n005" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="n006" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
      <element name="n007" nillable="true" type="xsd:string"/>
      <element name="x06" nillable="true" type="xsd:string"/>
      <element name="n008" nillable="true" type="xsd:string"/>
      <element name="x07" nillable="true" type="xsd:string"/>
      <element name="n009" nillable="true" type="xsd:string"/>
      <element name="x08" nillable="true" type="xsd:string"/>
      <element name="n010" nillable="true" type="xsd:string"/>
      <element name="x09" nillable="true" type="xsd:string"/>
      <element name="n011" nillable="true" type="xsd:string"/>
      <element name="x10" nillable="true" type="xsd:string"/>
      <element name="n012" nillable="true" type="xsd:string"/>
      <element name="x11" nillable="true" type="xsd:string"/>
      <element name="n013" nillable="true" type="xsd:string"/>
      <element name="x12" nillable="true" type="xsd:string"/>
      <element name="n014" nillable="true" type="xsd:string"/>
      <element name="x13" nillable="true" type="xsd:string"/>
      <element name="n015" nillable="true" type="xsd:string"/>
      <element name="n016" nillable="true" type="xsd:string"/>
      <element name="n017" nillable="true" type="xsd:string"/>
      <element name="x14" nillable="true" type="xsd:string"/>
      <element name="x15" nillable="true" type="xsd:string"/>
      <element name="x16" nillable="true" type="xsd:string"/>
      <element name="x17" nillable="true" type="xsd:string"/>
      <element name="n018" nillable="true" type="xsd:string"/>
      <element name="n019" nillable="true" type="xsd:string"/>
      <element name="n020" nillable="true" type="xsd:string"/>
      <element name="n021" nillable="true" type="xsd:string"/>
      <element name="n022" nillable="true" type="xsd:string"/>
      <element name="x18" nillable="true" type="xsd:string"/>
      <element name="x19" nillable="true" type="xsd:string"/>
      <element name="n023" nillable="true" type="xsd:string"/>
      <element name="x20" nillable="true" type="xsd:string"/>
      <element name="x21" nillable="true" type="xsd:string"/>
      <element name="n024" nillable="true" type="xsd:string"/>
      <element name="x22" nillable="true" type="xsd:string"/>
      <element name="x23" nillable="true" type="xsd:string"/>
      <element name="n025" nillable="true" type="xsd:string"/>
      <element name="x24" nillable="true" type="xsd:string"/>
      <element name="n026" nillable="true" type="xsd:string"/>
      <element name="n027" nillable="true" type="xsd:string"/>
      <element name="n028" nillable="true" type="xsd:string"/>
      <element name="x25" nillable="true" type="xsd:string"/>
      <element name="x26" nillable="true" type="xsd:string"/>
      <element name="x27" nillable="true" type="xsd:string"/>
      <element name="x28" nillable="true" type="xsd:string"/>
      <element name="x29" nillable="true" type="xsd:string"/>
      <element name="x30" nillable="true" type="xsd:string"/>
      <element name="n029" nillable="true" type="xsd:string"/>
      <element name="x31" nillable="true" type="xsd:string"/>
      <element name="n030" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprSemZivnoResponse">
    <complexType>
     <sequence>
      <element name="nprSemZivnoReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprSemZivnoResponse">

      <wsdl:part element="impl:nprSemZivnoResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprSemZivnoRequest">

      <wsdl:part element="impl:nprSemZivno" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_SEM_Zivno_SEI">

      <wsdl:operation name="nprSemZivno">

         <wsdl:input message="impl:nprSemZivnoRequest" name="nprSemZivnoRequest"/>

         <wsdl:output message="impl:nprSemZivnoResponse" name="nprSemZivnoResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_SEM_ZivnoSoapBinding" type="impl:NPR_SEM_Zivno_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprSemZivno">

         <wsdlsoap:operation soapAction="nprSemZivno"/>

         <wsdl:input name="nprSemZivnoRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprSemZivnoResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_SEM_ZivnoService">

      <wsdl:port binding="impl:NPR_SEM_ZivnoSoapBinding" name="NPR_SEM_Zivno">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_SEM_Zivno"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
