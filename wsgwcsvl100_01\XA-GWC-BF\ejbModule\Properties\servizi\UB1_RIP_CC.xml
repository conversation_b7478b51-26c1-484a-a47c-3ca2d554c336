<?xml version="1.0"?>
<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform">
    <dispenser>
        <road>
            <request>
                <input>
					<param name="X61I-CODICE-APPLICAZIONE"/>
					<param name="X61I-CODICE-BANCA"/>
					<param name="X61I-DATA-CRE-PRA"/>
					<param name="X61I-DT-INI-PROP"/>
					<param name="X61I-FLAG-CALCOLO"/>
					<param name="X61I-IMPORTO-FIDO1"/>
					<param name="X61I-IMPORTO-FIDO2"/>
					<param name="X61I-IMPORTO-FIDO3"/>
					<param name="X61I-IMPORTO-FIDO4"/>
					<param name="X61I-IMPORTO-FIDO5"/>
					<param name="X61I-IMPORTO-FIDO6"/>
					<param name="X61I-IMPORTO-FIDO7"/>
					<param name="X61I-IMPORTO-FIDO8"/>
					<param name="X61I-IMPORTO-FIDO9"/>
					<param name="X61I-IMPORTO-FIDO10"/>
					<param name="X61I-MATRICOLA"/>
					<param name="X61I-NUMERO-PROPOSTA"/>
					<param name="X61I-POS-ANOMALA"/>
					<param name="X61I-PROGR-FIDO1"/>
					<param name="X61I-PROGR-FIDO2"/>
					<param name="X61I-PROGR-FIDO3"/>
					<param name="X61I-PROGR-FIDO4"/>
					<param name="X61I-PROGR-FIDO5"/>
					<param name="X61I-PROGR-FIDO6"/>
					<param name="X61I-PROGR-FIDO7"/>
					<param name="X61I-PROGR-FIDO8"/>
					<param name="X61I-PROGR-FIDO9"/>
					<param name="X61I-PROGR-FIDO10"/>
					<param name="X61I-PROPOSTA-FIDO"/>
					<param name="X61I-ALTRI-REDDITI-1"/>
					<param name="X61I-ALTRI-REDDITI-2"/>
					<param name="X61I-ALTRI-REDDITI-3"/>
					<param name="X61I-ALTRI-REDDITI-4"/>
					<param name="X61I-ALTRI-REDDITI-5"/>
					<param name="X61I-ALTRI-REDDITI-6"/>
					<param name="X61I-ALTRI-REDDITI-7"/>
					<param name="X61I-ALTRI-REDDITI-8"/>
					<param name="X61I-ALTRI-REDDITI-9"/>
					<param name="X61I-ALTRI-REDDITI-10"/>
					<param name="X61I-ALTRI-REDDITI-11"/>
					<param name="X61I-ANNI-INDIRIZZO-1"/>
					<param name="X61I-ANNI-INDIRIZZO-2"/>
					<param name="X61I-ANNI-INDIRIZZO-3"/>
					<param name="X61I-ANNI-INDIRIZZO-4"/>
					<param name="X61I-ANNI-INDIRIZZO-5"/>
					<param name="X61I-ANNI-INDIRIZZO-6"/>
					<param name="X61I-ANNI-INDIRIZZO-7"/>
					<param name="X61I-ANNI-INDIRIZZO-8"/>
					<param name="X61I-ANNI-INDIRIZZO-9"/>
					<param name="X61I-ANNI-INDIRIZZO-10"/>
					<param name="X61I-ANNI-INDIRIZZO-11"/>
					<param name="X61I-ANTITERR-1"/>
					<param name="X61I-ANTITERR-2"/>
					<param name="X61I-ANTITERR-3"/>
					<param name="X61I-ANTITERR-4"/>
					<param name="X61I-ANTITERR-5"/>
					<param name="X61I-ANTITERR-6"/>
					<param name="X61I-ANTITERR-7"/>
					<param name="X61I-ANTITERR-8"/>
					<param name="X61I-ANTITERR-9"/>
					<param name="X61I-ANTITERR-10"/>
					<param name="X61I-ANTITERR-11"/>
					<param name="X61I-ANZIANITA-CC-1"/>
					<param name="X61I-ANZIANITA-CC-2"/>
					<param name="X61I-ANZIANITA-CC-3"/>
					<param name="X61I-ANZIANITA-CC-4"/>
					<param name="X61I-ANZIANITA-CC-5"/>
					<param name="X61I-ANZIANITA-CC-6"/>
					<param name="X61I-ANZIANITA-CC-7"/>
					<param name="X61I-ANZIANITA-CC-8"/>
					<param name="X61I-ANZIANITA-CC-9"/>
					<param name="X61I-ANZIANITA-CC-10"/>
					<param name="X61I-ANZIANITA-CC-11"/>
					<param name="X61I-APP-STESSO-NUC-1"/>
					<param name="X61I-APP-STESSO-NUC-2"/>
					<param name="X61I-APP-STESSO-NUC-3"/>
					<param name="X61I-APP-STESSO-NUC-4"/>
					<param name="X61I-APP-STESSO-NUC-5"/>
					<param name="X61I-APP-STESSO-NUC-6"/>
					<param name="X61I-APP-STESSO-NUC-7"/>
					<param name="X61I-APP-STESSO-NUC-8"/>
					<param name="X61I-APP-STESSO-NUC-9"/>
					<param name="X61I-APP-STESSO-NUC-10"/>
					<param name="X61I-APP-STESSO-NUC-11"/>
					<param name="X61I-BLOCCO-CARTE-1"/>
					<param name="X61I-BLOCCO-CARTE-2"/>
					<param name="X61I-BLOCCO-CARTE-3"/>
					<param name="X61I-BLOCCO-CARTE-4"/>
					<param name="X61I-BLOCCO-CARTE-5"/>
					<param name="X61I-BLOCCO-CARTE-6"/>
					<param name="X61I-BLOCCO-CARTE-7"/>
					<param name="X61I-BLOCCO-CARTE-8"/>
					<param name="X61I-BLOCCO-CARTE-9"/>
					<param name="X61I-BLOCCO-CARTE-10"/>
					<param name="X61I-BLOCCO-CARTE-11"/>
					<param name="X61I-COD-FISCALE-1"/>
					<param name="X61I-COD-FISCALE-2"/>
					<param name="X61I-COD-FISCALE-3"/>
					<param name="X61I-COD-FISCALE-4"/>
					<param name="X61I-COD-FISCALE-5"/>
					<param name="X61I-COD-FISCALE-6"/>
					<param name="X61I-COD-FISCALE-7"/>
					<param name="X61I-COD-FISCALE-8"/>
					<param name="X61I-COD-FISCALE-9"/>
					<param name="X61I-COD-FISCALE-10"/>
					<param name="X61I-COD-FISCALE-11"/>
					<param name="X61I-CONTR-CHIUS-1"/>
					<param name="X61I-CONTR-CHIUS-2"/>
					<param name="X61I-CONTR-CHIUS-3"/>
					<param name="X61I-CONTR-CHIUS-4"/>
					<param name="X61I-CONTR-CHIUS-5"/>
					<param name="X61I-CONTR-CHIUS-6"/>
					<param name="X61I-CONTR-CHIUS-7"/>
					<param name="X61I-CONTR-CHIUS-8"/>
					<param name="X61I-CONTR-CHIUS-9"/>
					<param name="X61I-CONTR-CHIUS-10"/>
					<param name="X61I-CONTR-CHIUS-11"/>
					<param name="X61I-CREDIT-RATING-A1-1"/>
					<param name="X61I-CREDIT-RATING-A1-2"/>
					<param name="X61I-CREDIT-RATING-A1-3"/>
					<param name="X61I-CREDIT-RATING-A1-4"/>
					<param name="X61I-CREDIT-RATING-A1-5"/>
					<param name="X61I-CREDIT-RATING-A1-6"/>
					<param name="X61I-CREDIT-RATING-A1-7"/>
					<param name="X61I-CREDIT-RATING-A1-8"/>
					<param name="X61I-CREDIT-RATING-A1-9"/>
					<param name="X61I-CREDIT-RATING-A1-10"/>
					<param name="X61I-CREDIT-RATING-A1-11"/>
					<param name="X61I-CREDIT-RATING-A2-1"/>
					<param name="X61I-CREDIT-RATING-A2-2"/>
					<param name="X61I-CREDIT-RATING-A2-3"/>
					<param name="X61I-CREDIT-RATING-A2-4"/>
					<param name="X61I-CREDIT-RATING-A2-5"/>
					<param name="X61I-CREDIT-RATING-A2-6"/>
					<param name="X61I-CREDIT-RATING-A2-7"/>
					<param name="X61I-CREDIT-RATING-A2-8"/>
					<param name="X61I-CREDIT-RATING-A2-9"/>
					<param name="X61I-CREDIT-RATING-A2-10"/>
					<param name="X61I-CREDIT-RATING-A2-11"/>
					<param name="X61I-CREDIT-RATING-C1-1"/>
					<param name="X61I-CREDIT-RATING-C1-2"/>
					<param name="X61I-CREDIT-RATING-C1-3"/>
					<param name="X61I-CREDIT-RATING-C1-4"/>
					<param name="X61I-CREDIT-RATING-C1-5"/>
					<param name="X61I-CREDIT-RATING-C1-6"/>
					<param name="X61I-CREDIT-RATING-C1-7"/>
					<param name="X61I-CREDIT-RATING-C1-8"/>
					<param name="X61I-CREDIT-RATING-C1-9"/>
					<param name="X61I-CREDIT-RATING-C1-10"/>
					<param name="X61I-CREDIT-RATING-C1-11"/>
					<param name="X61I-CREDIT-RATING-C2-1"/>
					<param name="X61I-CREDIT-RATING-C2-2"/>
					<param name="X61I-CREDIT-RATING-C2-3"/>
					<param name="X61I-CREDIT-RATING-C2-4"/>
					<param name="X61I-CREDIT-RATING-C2-5"/>
					<param name="X61I-CREDIT-RATING-C2-6"/>
					<param name="X61I-CREDIT-RATING-C2-7"/>
					<param name="X61I-CREDIT-RATING-C2-8"/>
					<param name="X61I-CREDIT-RATING-C2-9"/>
					<param name="X61I-CREDIT-RATING-C2-10"/>
					<param name="X61I-CREDIT-RATING-C2-11"/>
					<param name="X61I-DATA-INI-ATT-1"/>
					<param name="X61I-DATA-INI-ATT-2"/>
					<param name="X61I-DATA-INI-ATT-3"/>
					<param name="X61I-DATA-INI-ATT-4"/>
					<param name="X61I-DATA-INI-ATT-5"/>
					<param name="X61I-DATA-INI-ATT-6"/>
					<param name="X61I-DATA-INI-ATT-7"/>
					<param name="X61I-DATA-INI-ATT-8"/>
					<param name="X61I-DATA-INI-ATT-9"/>
					<param name="X61I-DATA-INI-ATT-10"/>
					<param name="X61I-DATA-INI-ATT-11"/>
					<param name="X61I-DATA-NASCITA-1"/>
					<param name="X61I-DATA-NASCITA-2"/>
					<param name="X61I-DATA-NASCITA-3"/>
					<param name="X61I-DATA-NASCITA-4"/>
					<param name="X61I-DATA-NASCITA-5"/>
					<param name="X61I-DATA-NASCITA-6"/>
					<param name="X61I-DATA-NASCITA-7"/>
					<param name="X61I-DATA-NASCITA-8"/>
					<param name="X61I-DATA-NASCITA-9"/>
					<param name="X61I-DATA-NASCITA-10"/>
					<param name="X61I-DATA-NASCITA-11"/>
					<param name="X61I-EVIDENZE-NEGATIVE-1"/>
					<param name="X61I-EVIDENZE-NEGATIVE-2"/>
					<param name="X61I-EVIDENZE-NEGATIVE-3"/>
					<param name="X61I-EVIDENZE-NEGATIVE-4"/>
					<param name="X61I-EVIDENZE-NEGATIVE-5"/>
					<param name="X61I-EVIDENZE-NEGATIVE-6"/>
					<param name="X61I-EVIDENZE-NEGATIVE-7"/>
					<param name="X61I-EVIDENZE-NEGATIVE-8"/>
					<param name="X61I-EVIDENZE-NEGATIVE-9"/>
					<param name="X61I-EVIDENZE-NEGATIVE-10"/>
					<param name="X61I-EVIDENZE-NEGATIVE-11"/>
					<param name="X61I-GAP-1-1"/>
					<param name="X61I-GAP-1-2"/>
					<param name="X61I-GAP-1-3"/>
					<param name="X61I-GAP-1-4"/>
					<param name="X61I-GAP-1-5"/>
					<param name="X61I-GAP-1-6"/>
					<param name="X61I-GAP-1-7"/>
					<param name="X61I-GAP-1-8"/>
					<param name="X61I-GAP-1-9"/>
					<param name="X61I-GAP-1-10"/>
					<param name="X61I-GAP-1-11"/>
					<param name="X61I-GAP-10-1"/>
					<param name="X61I-GAP-10-2"/>
					<param name="X61I-GAP-10-3"/>
					<param name="X61I-GAP-10-4"/>
					<param name="X61I-GAP-10-5"/>
					<param name="X61I-GAP-10-6"/>
					<param name="X61I-GAP-10-7"/>
					<param name="X61I-GAP-10-8"/>
					<param name="X61I-GAP-10-9"/>
					<param name="X61I-GAP-10-10"/>
					<param name="X61I-GAP-10-11"/>
					<param name="X61I-GAP-2-1"/>
					<param name="X61I-GAP-2-2"/>
					<param name="X61I-GAP-2-3"/>
					<param name="X61I-GAP-2-4"/>
					<param name="X61I-GAP-2-5"/>
					<param name="X61I-GAP-2-6"/>
					<param name="X61I-GAP-2-7"/>
					<param name="X61I-GAP-2-8"/>
					<param name="X61I-GAP-2-9"/>
					<param name="X61I-GAP-2-10"/>
					<param name="X61I-GAP-2-11"/>
					<param name="X61I-GAP-3-1"/>
					<param name="X61I-GAP-3-2"/>
					<param name="X61I-GAP-3-3"/>
					<param name="X61I-GAP-3-4"/>
					<param name="X61I-GAP-3-5"/>
					<param name="X61I-GAP-3-6"/>
					<param name="X61I-GAP-3-7"/>
					<param name="X61I-GAP-3-8"/>
					<param name="X61I-GAP-3-9"/>
					<param name="X61I-GAP-3-10"/>
					<param name="X61I-GAP-3-11"/>
					<param name="X61I-GAP-4-1"/>
					<param name="X61I-GAP-4-2"/>
					<param name="X61I-GAP-4-3"/>
					<param name="X61I-GAP-4-4"/>
					<param name="X61I-GAP-4-5"/>
					<param name="X61I-GAP-4-6"/>
					<param name="X61I-GAP-4-7"/>
					<param name="X61I-GAP-4-8"/>
					<param name="X61I-GAP-4-9"/>
					<param name="X61I-GAP-4-10"/>
					<param name="X61I-GAP-4-11"/>
					<param name="X61I-GAP-5-1"/>
					<param name="X61I-GAP-5-2"/>
					<param name="X61I-GAP-5-3"/>
					<param name="X61I-GAP-5-4"/>
					<param name="X61I-GAP-5-5"/>
					<param name="X61I-GAP-5-6"/>
					<param name="X61I-GAP-5-7"/>
					<param name="X61I-GAP-5-8"/>
					<param name="X61I-GAP-5-9"/>
					<param name="X61I-GAP-5-10"/>
					<param name="X61I-GAP-5-11"/>
					<param name="X61I-GAP-6-1"/>
					<param name="X61I-GAP-6-2"/>
					<param name="X61I-GAP-6-3"/>
					<param name="X61I-GAP-6-4"/>
					<param name="X61I-GAP-6-5"/>
					<param name="X61I-GAP-6-6"/>
					<param name="X61I-GAP-6-7"/>
					<param name="X61I-GAP-6-8"/>
					<param name="X61I-GAP-6-9"/>
					<param name="X61I-GAP-6-10"/>
					<param name="X61I-GAP-6-11"/>
					<param name="X61I-GAP-7-1"/>
					<param name="X61I-GAP-7-2"/>
					<param name="X61I-GAP-7-3"/>
					<param name="X61I-GAP-7-4"/>
					<param name="X61I-GAP-7-5"/>
					<param name="X61I-GAP-7-6"/>
					<param name="X61I-GAP-7-7"/>
					<param name="X61I-GAP-7-8"/>
					<param name="X61I-GAP-7-9"/>
					<param name="X61I-GAP-7-10"/>
					<param name="X61I-GAP-7-11"/>
					<param name="X61I-GAP-8-1"/>
					<param name="X61I-GAP-8-2"/>
					<param name="X61I-GAP-8-3"/>
					<param name="X61I-GAP-8-4"/>
					<param name="X61I-GAP-8-5"/>
					<param name="X61I-GAP-8-6"/>
					<param name="X61I-GAP-8-7"/>
					<param name="X61I-GAP-8-8"/>
					<param name="X61I-GAP-8-9"/>
					<param name="X61I-GAP-8-10"/>
					<param name="X61I-GAP-8-11"/>
					<param name="X61I-GAP-9-1"/>
					<param name="X61I-GAP-9-2"/>
					<param name="X61I-GAP-9-3"/>
					<param name="X61I-GAP-9-4"/>
					<param name="X61I-GAP-9-5"/>
					<param name="X61I-GAP-9-6"/>
					<param name="X61I-GAP-9-7"/>
					<param name="X61I-GAP-9-8"/>
					<param name="X61I-GAP-9-9"/>
					<param name="X61I-GAP-9-10"/>
					<param name="X61I-GAP-9-11"/>
					<param name="X61I-GRADO-RISCHIO-1"/>
					<param name="X61I-GRADO-RISCHIO-2"/>
					<param name="X61I-GRADO-RISCHIO-3"/>
					<param name="X61I-GRADO-RISCHIO-4"/>
					<param name="X61I-GRADO-RISCHIO-5"/>
					<param name="X61I-GRADO-RISCHIO-6"/>
					<param name="X61I-GRADO-RISCHIO-7"/>
					<param name="X61I-GRADO-RISCHIO-8"/>
					<param name="X61I-GRADO-RISCHIO-9"/>
					<param name="X61I-GRADO-RISCHIO-10"/>
					<param name="X61I-GRADO-RISCHIO-11"/>
					<param name="X61I-IMP-MENS-AI-1"/>
					<param name="X61I-IMP-MENS-AI-2"/>
					<param name="X61I-IMP-MENS-AI-3"/>
					<param name="X61I-IMP-MENS-AI-4"/>
					<param name="X61I-IMP-MENS-AI-5"/>
					<param name="X61I-IMP-MENS-AI-6"/>
					<param name="X61I-IMP-MENS-AI-7"/>
					<param name="X61I-IMP-MENS-AI-8"/>
					<param name="X61I-IMP-MENS-AI-9"/>
					<param name="X61I-IMP-MENS-AI-10"/>
					<param name="X61I-IMP-MENS-AI-11"/>
					<param name="X61I-IMP-MENS-CRIF-1"/>
					<param name="X61I-IMP-MENS-CRIF-2"/>
					<param name="X61I-IMP-MENS-CRIF-3"/>
					<param name="X61I-IMP-MENS-CRIF-4"/>
					<param name="X61I-IMP-MENS-CRIF-5"/>
					<param name="X61I-IMP-MENS-CRIF-6"/>
					<param name="X61I-IMP-MENS-CRIF-7"/>
					<param name="X61I-IMP-MENS-CRIF-8"/>
					<param name="X61I-IMP-MENS-CRIF-9"/>
					<param name="X61I-IMP-MENS-CRIF-10"/>
					<param name="X61I-IMP-MENS-CRIF-11"/>
					<param name="X61I-INS-ALBO-PROF-1"/>
					<param name="X61I-INS-ALBO-PROF-2"/>
					<param name="X61I-INS-ALBO-PROF-3"/>
					<param name="X61I-INS-ALBO-PROF-4"/>
					<param name="X61I-INS-ALBO-PROF-5"/>
					<param name="X61I-INS-ALBO-PROF-6"/>
					<param name="X61I-INS-ALBO-PROF-7"/>
					<param name="X61I-INS-ALBO-PROF-8"/>
					<param name="X61I-INS-ALBO-PROF-9"/>
					<param name="X61I-INS-ALBO-PROF-10"/>
					<param name="X61I-INS-ALBO-PROF-11"/>
					<param name="X61I-LAUT1-IMP-NET-1"/>
					<param name="X61I-LAUT1-IMP-NET-2"/>
					<param name="X61I-LAUT1-IMP-NET-3"/>
					<param name="X61I-LAUT1-IMP-NET-4"/>
					<param name="X61I-LAUT1-IMP-NET-5"/>
					<param name="X61I-LAUT1-IMP-NET-6"/>
					<param name="X61I-LAUT1-IMP-NET-7"/>
					<param name="X61I-LAUT1-IMP-NET-8"/>
					<param name="X61I-LAUT1-IMP-NET-9"/>
					<param name="X61I-LAUT1-IMP-NET-10"/>
					<param name="X61I-LAUT1-IMP-NET-11"/>
					<param name="X61I-LAUT1-RED-COMP-1"/>
					<param name="X61I-LAUT1-RED-COMP-2"/>
					<param name="X61I-LAUT1-RED-COMP-3"/>
					<param name="X61I-LAUT1-RED-COMP-4"/>
					<param name="X61I-LAUT1-RED-COMP-5"/>
					<param name="X61I-LAUT1-RED-COMP-6"/>
					<param name="X61I-LAUT1-RED-COMP-7"/>
					<param name="X61I-LAUT1-RED-COMP-8"/>
					<param name="X61I-LAUT1-RED-COMP-9"/>
					<param name="X61I-LAUT1-RED-COMP-10"/>
					<param name="X61I-LAUT1-RED-COMP-11"/>
					<param name="X61I-LDIP1-IMP-NET-1"/>
					<param name="X61I-LDIP1-IMP-NET-2"/>
					<param name="X61I-LDIP1-IMP-NET-3"/>
					<param name="X61I-LDIP1-IMP-NET-4"/>
					<param name="X61I-LDIP1-IMP-NET-5"/>
					<param name="X61I-LDIP1-IMP-NET-6"/>
					<param name="X61I-LDIP1-IMP-NET-7"/>
					<param name="X61I-LDIP1-IMP-NET-8"/>
					<param name="X61I-LDIP1-IMP-NET-9"/>
					<param name="X61I-LDIP1-IMP-NET-10"/>
					<param name="X61I-LDIP1-IMP-NET-11"/>
					<param name="X61I-LDIP1-RED-COMP-1"/>
					<param name="X61I-LDIP1-RED-COMP-2"/>
					<param name="X61I-LDIP1-RED-COMP-3"/>
					<param name="X61I-LDIP1-RED-COMP-4"/>
					<param name="X61I-LDIP1-RED-COMP-5"/>
					<param name="X61I-LDIP1-RED-COMP-6"/>
					<param name="X61I-LDIP1-RED-COMP-7"/>
					<param name="X61I-LDIP1-RED-COMP-8"/>
					<param name="X61I-LDIP1-RED-COMP-9"/>
					<param name="X61I-LDIP1-RED-COMP-10"/>
					<param name="X61I-LDIP1-RED-COMP-11"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-1"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-2"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-3"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-4"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-5"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-6"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-7"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-8"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-9"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-10"/>
					<param name="X61I-MOTIVO-RIFIUTO-C1-11"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-1"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-2"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-3"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-4"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-5"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-6"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-7"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-8"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-9"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-10"/>
					<param name="X61I-MOTIVO-RIFIUTO-C2-11"/>
					<param name="X61I-NAZIONALITA-1"/>
					<param name="X61I-NAZIONALITA-2"/>
					<param name="X61I-NAZIONALITA-3"/>
					<param name="X61I-NAZIONALITA-4"/>
					<param name="X61I-NAZIONALITA-5"/>
					<param name="X61I-NAZIONALITA-6"/>
					<param name="X61I-NAZIONALITA-7"/>
					<param name="X61I-NAZIONALITA-8"/>
					<param name="X61I-NAZIONALITA-9"/>
					<param name="X61I-NAZIONALITA-10"/>
					<param name="X61I-NAZIONALITA-11"/>
					<param name="X61I-NDG-1"/>
					<param name="X61I-NDG-2"/>
					<param name="X61I-NDG-3"/>
					<param name="X61I-NDG-4"/>
					<param name="X61I-NDG-5"/>
					<param name="X61I-NDG-6"/>
					<param name="X61I-NDG-7"/>
					<param name="X61I-NDG-8"/>
					<param name="X61I-NDG-9"/>
					<param name="X61I-NDG-10"/>
					<param name="X61I-NDG-11"/>
					<param name="X61I-NUMERO-CARTE-1"/>
					<param name="X61I-NUMERO-CARTE-2"/>
					<param name="X61I-NUMERO-CARTE-3"/>
					<param name="X61I-NUMERO-CARTE-4"/>
					<param name="X61I-NUMERO-CARTE-5"/>
					<param name="X61I-NUMERO-CARTE-6"/>
					<param name="X61I-NUMERO-CARTE-7"/>
					<param name="X61I-NUMERO-CARTE-8"/>
					<param name="X61I-NUMERO-CARTE-9"/>
					<param name="X61I-NUMERO-CARTE-10"/>
					<param name="X61I-NUMERO-CARTE-11"/>
					<param name="X61I-OCCUPAZIONE-1"/>
					<param name="X61I-OCCUPAZIONE-2"/>
					<param name="X61I-OCCUPAZIONE-3"/>
					<param name="X61I-OCCUPAZIONE-4"/>
					<param name="X61I-OCCUPAZIONE-5"/>
					<param name="X61I-OCCUPAZIONE-6"/>
					<param name="X61I-OCCUPAZIONE-7"/>
					<param name="X61I-OCCUPAZIONE-8"/>
					<param name="X61I-OCCUPAZIONE-9"/>
					<param name="X61I-OCCUPAZIONE-10"/>
					<param name="X61I-OCCUPAZIONE-11"/>
					<param name="X61I-PD-RIGA3-FIDI-B-1"/>
					<param name="X61I-PD-RIGA3-FIDI-B-2"/>
					<param name="X61I-PD-RIGA3-FIDI-B-3"/>
					<param name="X61I-PD-RIGA3-FIDI-B-4"/>
					<param name="X61I-PD-RIGA3-FIDI-B-5"/>
					<param name="X61I-PD-RIGA3-FIDI-B-6"/>
					<param name="X61I-PD-RIGA3-FIDI-B-7"/>
					<param name="X61I-PD-RIGA3-FIDI-B-8"/>
					<param name="X61I-PD-RIGA3-FIDI-B-9"/>
					<param name="X61I-PD-RIGA3-FIDI-B-10"/>
					<param name="X61I-PD-RIGA3-FIDI-B-11"/>
					<param name="X61I-PD-RIGA3-FIDI-G-1"/>
					<param name="X61I-PD-RIGA3-FIDI-G-2"/>
					<param name="X61I-PD-RIGA3-FIDI-G-3"/>
					<param name="X61I-PD-RIGA3-FIDI-G-4"/>
					<param name="X61I-PD-RIGA3-FIDI-G-5"/>
					<param name="X61I-PD-RIGA3-FIDI-G-6"/>
					<param name="X61I-PD-RIGA3-FIDI-G-7"/>
					<param name="X61I-PD-RIGA3-FIDI-G-8"/>
					<param name="X61I-PD-RIGA3-FIDI-G-9"/>
					<param name="X61I-PD-RIGA3-FIDI-G-10"/>
					<param name="X61I-PD-RIGA3-FIDI-G-11"/>
					<param name="X61I-PD-RIGA3-RISK-B-1"/>
					<param name="X61I-PD-RIGA3-RISK-B-2"/>
					<param name="X61I-PD-RIGA3-RISK-B-3"/>
					<param name="X61I-PD-RIGA3-RISK-B-4"/>
					<param name="X61I-PD-RIGA3-RISK-B-5"/>
					<param name="X61I-PD-RIGA3-RISK-B-6"/>
					<param name="X61I-PD-RIGA3-RISK-B-7"/>
					<param name="X61I-PD-RIGA3-RISK-B-8"/>
					<param name="X61I-PD-RIGA3-RISK-B-9"/>
					<param name="X61I-PD-RIGA3-RISK-B-10"/>
					<param name="X61I-PD-RIGA3-RISK-B-11"/>
					<param name="X61I-PD-RIGA3-RISK-G-1"/>
					<param name="X61I-PD-RIGA3-RISK-G-2"/>
					<param name="X61I-PD-RIGA3-RISK-G-3"/>
					<param name="X61I-PD-RIGA3-RISK-G-4"/>
					<param name="X61I-PD-RIGA3-RISK-G-5"/>
					<param name="X61I-PD-RIGA3-RISK-G-6"/>
					<param name="X61I-PD-RIGA3-RISK-G-7"/>
					<param name="X61I-PD-RIGA3-RISK-G-8"/>
					<param name="X61I-PD-RIGA3-RISK-G-9"/>
					<param name="X61I-PD-RIGA3-RISK-G-10"/>
					<param name="X61I-PD-RIGA3-RISK-G-11"/>
					<param name="X61I-PEG-STATO-CRIF-12-1"/>
					<param name="X61I-PEG-STATO-CRIF-12-2"/>
					<param name="X61I-PEG-STATO-CRIF-12-3"/>
					<param name="X61I-PEG-STATO-CRIF-12-4"/>
					<param name="X61I-PEG-STATO-CRIF-12-5"/>
					<param name="X61I-PEG-STATO-CRIF-12-6"/>
					<param name="X61I-PEG-STATO-CRIF-12-7"/>
					<param name="X61I-PEG-STATO-CRIF-12-8"/>
					<param name="X61I-PEG-STATO-CRIF-12-9"/>
					<param name="X61I-PEG-STATO-CRIF-12-10"/>
					<param name="X61I-PEG-STATO-CRIF-12-11"/>
					<param name="X61I-PEG-STATO-CRIF-I12-1"/>
					<param name="X61I-PEG-STATO-CRIF-I12-2"/>
					<param name="X61I-PEG-STATO-CRIF-I12-3"/>
					<param name="X61I-PEG-STATO-CRIF-I12-4"/>
					<param name="X61I-PEG-STATO-CRIF-I12-5"/>
					<param name="X61I-PEG-STATO-CRIF-I12-6"/>
					<param name="X61I-PEG-STATO-CRIF-I12-7"/>
					<param name="X61I-PEG-STATO-CRIF-I12-8"/>
					<param name="X61I-PEG-STATO-CRIF-I12-9"/>
					<param name="X61I-PEG-STATO-CRIF-I12-10"/>
					<param name="X61I-PEG-STATO-CRIF-I12-11"/>
					<param name="X61I-PREGIUDIZIEVOLI-1"/>
					<param name="X61I-PREGIUDIZIEVOLI-2"/>
					<param name="X61I-PREGIUDIZIEVOLI-3"/>
					<param name="X61I-PREGIUDIZIEVOLI-4"/>
					<param name="X61I-PREGIUDIZIEVOLI-5"/>
					<param name="X61I-PREGIUDIZIEVOLI-6"/>
					<param name="X61I-PREGIUDIZIEVOLI-7"/>
					<param name="X61I-PREGIUDIZIEVOLI-8"/>
					<param name="X61I-PREGIUDIZIEVOLI-9"/>
					<param name="X61I-PREGIUDIZIEVOLI-10"/>
					<param name="X61I-PREGIUDIZIEVOLI-11"/>
					<param name="X61I-PROTESTI-1"/>
					<param name="X61I-PROTESTI-2"/>
					<param name="X61I-PROTESTI-3"/>
					<param name="X61I-PROTESTI-4"/>
					<param name="X61I-PROTESTI-5"/>
					<param name="X61I-PROTESTI-6"/>
					<param name="X61I-PROTESTI-7"/>
					<param name="X61I-PROTESTI-8"/>
					<param name="X61I-PROTESTI-9"/>
					<param name="X61I-PROTESTI-10"/>
					<param name="X61I-PROTESTI-11"/>
					<param name="X61I-RAPP-ESS-ESTINTI-1"/>
					<param name="X61I-RAPP-ESS-ESTINTI-2"/>
					<param name="X61I-RAPP-ESS-ESTINTI-3"/>
					<param name="X61I-RAPP-ESS-ESTINTI-4"/>
					<param name="X61I-RAPP-ESS-ESTINTI-5"/>
					<param name="X61I-RAPP-ESS-ESTINTI-6"/>
					<param name="X61I-RAPP-ESS-ESTINTI-7"/>
					<param name="X61I-RAPP-ESS-ESTINTI-8"/>
					<param name="X61I-RAPP-ESS-ESTINTI-9"/>
					<param name="X61I-RAPP-ESS-ESTINTI-10"/>
					<param name="X61I-RAPP-ESS-ESTINTI-11"/>
					<param name="X61I-RATE-MOROSE-1"/>
					<param name="X61I-RATE-MOROSE-2"/>
					<param name="X61I-RATE-MOROSE-3"/>
					<param name="X61I-RATE-MOROSE-4"/>
					<param name="X61I-RATE-MOROSE-5"/>
					<param name="X61I-RATE-MOROSE-6"/>
					<param name="X61I-RATE-MOROSE-7"/>
					<param name="X61I-RATE-MOROSE-8"/>
					<param name="X61I-RATE-MOROSE-9"/>
					<param name="X61I-RATE-MOROSE-10"/>
					<param name="X61I-RATE-MOROSE-11"/>
					<param name="X61I-RN14-1"/>
					<param name="X61I-RN14-2"/>
					<param name="X61I-RN14-3"/>
					<param name="X61I-RN14-4"/>
					<param name="X61I-RN14-5"/>
					<param name="X61I-RN14-6"/>
					<param name="X61I-RN14-7"/>
					<param name="X61I-RN14-8"/>
					<param name="X61I-RN14-9"/>
					<param name="X61I-RN14-10"/>
					<param name="X61I-RN14-11"/>
					<param name="X61I-SIT-ABIT-ATT-1"/>
					<param name="X61I-SIT-ABIT-ATT-2"/>
					<param name="X61I-SIT-ABIT-ATT-3"/>
					<param name="X61I-SIT-ABIT-ATT-4"/>
					<param name="X61I-SIT-ABIT-ATT-5"/>
					<param name="X61I-SIT-ABIT-ATT-6"/>
					<param name="X61I-SIT-ABIT-ATT-7"/>
					<param name="X61I-SIT-ABIT-ATT-8"/>
					<param name="X61I-SIT-ABIT-ATT-9"/>
					<param name="X61I-SIT-ABIT-ATT-10"/>
					<param name="X61I-SIT-ABIT-ATT-11"/>
					<param name="X61I-SOFFERENZE-1"/>
					<param name="X61I-SOFFERENZE-2"/>
					<param name="X61I-SOFFERENZE-3"/>
					<param name="X61I-SOFFERENZE-4"/>
					<param name="X61I-SOFFERENZE-5"/>
					<param name="X61I-SOFFERENZE-6"/>
					<param name="X61I-SOFFERENZE-7"/>
					<param name="X61I-SOFFERENZE-8"/>
					<param name="X61I-SOFFERENZE-9"/>
					<param name="X61I-SOFFERENZE-10"/>
					<param name="X61I-SOFFERENZE-11"/>
					<param name="X61I-STIP-PENS-CAN-1"/>
					<param name="X61I-STIP-PENS-CAN-2"/>
					<param name="X61I-STIP-PENS-CAN-3"/>
					<param name="X61I-STIP-PENS-CAN-4"/>
					<param name="X61I-STIP-PENS-CAN-5"/>
					<param name="X61I-STIP-PENS-CAN-6"/>
					<param name="X61I-STIP-PENS-CAN-7"/>
					<param name="X61I-STIP-PENS-CAN-8"/>
					<param name="X61I-STIP-PENS-CAN-9"/>
					<param name="X61I-STIP-PENS-CAN-10"/>
					<param name="X61I-STIP-PENS-CAN-11"/>
					<param name="X61I-TIPO-LAV-DIP-1"/>
					<param name="X61I-TIPO-LAV-DIP-2"/>
					<param name="X61I-TIPO-LAV-DIP-3"/>
					<param name="X61I-TIPO-LAV-DIP-4"/>
					<param name="X61I-TIPO-LAV-DIP-5"/>
					<param name="X61I-TIPO-LAV-DIP-6"/>
					<param name="X61I-TIPO-LAV-DIP-7"/>
					<param name="X61I-TIPO-LAV-DIP-8"/>
					<param name="X61I-TIPO-LAV-DIP-9"/>
					<param name="X61I-TIPO-LAV-DIP-10"/>
					<param name="X61I-TIPO-LAV-DIP-11"/>
					<param name="X61I-TIPO-PROFESSIONE-1"/>
					<param name="X61I-TIPO-PROFESSIONE-2"/>
					<param name="X61I-TIPO-PROFESSIONE-3"/>
					<param name="X61I-TIPO-PROFESSIONE-4"/>
					<param name="X61I-TIPO-PROFESSIONE-5"/>
					<param name="X61I-TIPO-PROFESSIONE-6"/>
					<param name="X61I-TIPO-PROFESSIONE-7"/>
					<param name="X61I-TIPO-PROFESSIONE-8"/>
					<param name="X61I-TIPO-PROFESSIONE-9"/>
					<param name="X61I-TIPO-PROFESSIONE-10"/>
					<param name="X61I-TIPO-PROFESSIONE-11"/>
					<param name="X61I-TIPO-SOGGETTO-1"/>
					<param name="X61I-TIPO-SOGGETTO-2"/>
					<param name="X61I-TIPO-SOGGETTO-3"/>
					<param name="X61I-TIPO-SOGGETTO-4"/>
					<param name="X61I-TIPO-SOGGETTO-5"/>
					<param name="X61I-TIPO-SOGGETTO-6"/>
					<param name="X61I-TIPO-SOGGETTO-7"/>
					<param name="X61I-TIPO-SOGGETTO-8"/>
					<param name="X61I-TIPO-SOGGETTO-9"/>
					<param name="X61I-TIPO-SOGGETTO-10"/>
					<param name="X61I-TIPO-SOGGETTO-11"/>
					<param name="X61I-TIPOLOGIA-CLI-1"/>
					<param name="X61I-TIPOLOGIA-CLI-2"/>
					<param name="X61I-TIPOLOGIA-CLI-3"/>
					<param name="X61I-TIPOLOGIA-CLI-4"/>
					<param name="X61I-TIPOLOGIA-CLI-5"/>
					<param name="X61I-TIPOLOGIA-CLI-6"/>
					<param name="X61I-TIPOLOGIA-CLI-7"/>
					<param name="X61I-TIPOLOGIA-CLI-8"/>
					<param name="X61I-TIPOLOGIA-CLI-9"/>
					<param name="X61I-TIPOLOGIA-CLI-10"/>
					<param name="X61I-TIPOLOGIA-CLI-11"/>
					<param name="X61I-TITOLO-STUDIO-1"/>
					<param name="X61I-TITOLO-STUDIO-2"/>
					<param name="X61I-TITOLO-STUDIO-3"/>
					<param name="X61I-TITOLO-STUDIO-4"/>
					<param name="X61I-TITOLO-STUDIO-5"/>
					<param name="X61I-TITOLO-STUDIO-6"/>
					<param name="X61I-TITOLO-STUDIO-7"/>
					<param name="X61I-TITOLO-STUDIO-8"/>
					<param name="X61I-TITOLO-STUDIO-9"/>
					<param name="X61I-TITOLO-STUDIO-10"/>
					<param name="X61I-TITOLO-STUDIO-11"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-1"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-2"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-3"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-4"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-5"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-6"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-7"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-8"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-9"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-10"/>
					<param name="X61I-VALUTAZ-PRELIM-A1-11"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-1"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-2"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-3"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-4"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-5"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-6"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-7"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-8"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-9"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-10"/>
					<param name="X61I-VALUTAZ-PRELIM-A2-11"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-1"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-2"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-3"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-4"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-5"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-6"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-7"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-8"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-9"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-10"/>
					<param name="X61I-VALUTAZ-PRELIM-C1-11"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-1"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-2"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-3"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-4"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-5"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-6"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-7"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-8"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-9"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-10"/>
					<param name="X61I-VALUTAZ-PRELIM-C2-11"/>
					<param name="X61I-RESID-STRANIERO-1"/>
					<param name="X61I-RESID-STRANIERO-2"/>
					<param name="X61I-RESID-STRANIERO-3"/>
					<param name="X61I-RESID-STRANIERO-4"/>
					<param name="X61I-RESID-STRANIERO-5"/>
					<param name="X61I-RESID-STRANIERO-6"/>
					<param name="X61I-RESID-STRANIERO-7"/>
					<param name="X61I-RESID-STRANIERO-8"/>
					<param name="X61I-RESID-STRANIERO-9"/>
					<param name="X61I-RESID-STRANIERO-10"/>
					<param name="X61I-RESID-STRANIERO-11"/>
					<param name="X61I-TIPO-PROD1"/>
					<param name="X61I-TIPO-PROD2"/>
					<param name="X61I-TIPO-PROD3"/>
					<param name="X61I-TIPO-PROD4"/>
					<param name="X61I-TIPO-PROD5"/>
					<param name="X61I-NUM-CC-APP"/>
					<param name="X61I-NDG-APP"/>
					<param name="X61I-IMP-FIDO-APP"/>
					<param name="X61I-IMP-UTI-APP"/>
					<param name="X61I-PAST-DUE-1"/>
					<param name="X61I-PAST-DUE-2"/>
					<param name="X61I-PAST-DUE-3"/>
					<param name="X61I-PAST-DUE-4"/>
					<param name="X61I-PAST-DUE-5"/>
					<param name="X61I-PAST-DUE-6"/>
					<param name="X61I-PAST-DUE-7"/>
					<param name="X61I-PAST-DUE-8"/>
					<param name="X61I-PAST-DUE-9"/>
					<param name="X61I-PAST-DUE-10"/>
					<param name="X61I-PAST-DUE-11"/>
					<param name="X61I-NDG-MM"/>
					<param name="X61I-TP-NDG-MM"/>
					<param name="X61I-STIP-CAN-MM"/>
					<param name="X61I-APP-NUC-MM"/>
					<param name="X61I-DT-INI-ATT-MM"/>
					<param name="X61I-INS-ALBO-MM"/>
					<param name="X61I-DT-CC-MM"/>
					<param name="X61I-REDDITO"/>
					<param name="X61I-FIDO-PROP"/>
					<param name="X61I-SCORE-AND"/>
					<param name="X61I-TP-RAT-NPR"/>
					<param name="X61I-TP-PROD"/>
					<param name="X61I-TP-LAV-MM"/>
					<param name="X61I-CTV-ESSERE"/>
					<param name="X61I-CTV-PROPOSTA"/>
					<param name="X61I-CAI"/>
					<param name="X61I-REDD-MENS-NETTO-1"/>
					<param name="X61I-REDD-MENS-NETTO-2"/>
					<param name="X61I-REDD-MENS-NETTO-3"/>
					<param name="X61I-REDD-MENS-NETTO-4"/>
					<param name="X61I-REDD-MENS-NETTO-5"/>
					<param name="X61I-REDD-MENS-NETTO-6"/>
					<param name="X61I-REDD-MENS-NETTO-7"/>
					<param name="X61I-REDD-MENS-NETTO-8"/>
					<param name="X61I-REDD-MENS-NETTO-9"/>
					<param name="X61I-REDD-MENS-NETTO-10"/>
					<param name="X61I-REDD-MENS-NETTO-11"/>
					
				</input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.CPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.GaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.SecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>XPO061-INPUT</hostService>
            <applBankNumber>99</applBankNumber>
            <servBankNumber>99</servBankNumber>
            <version>0001</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>XP61</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>