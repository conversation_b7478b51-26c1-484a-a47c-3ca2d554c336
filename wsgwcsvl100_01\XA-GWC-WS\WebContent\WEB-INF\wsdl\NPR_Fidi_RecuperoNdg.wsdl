<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_RecuperoNdg">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_RecuperoNdgResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_RecuperoNdgReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_RecuperoNdgRequest">

      <wsdl:part element="impl:nprFidi_RecuperoNdg" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_RecuperoNdgResponse">

      <wsdl:part element="impl:nprFidi_RecuperoNdgResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_RecuperoNdg_SEI">

      <wsdl:operation name="nprFidi_RecuperoNdg">

         <wsdl:input message="impl:nprFidi_RecuperoNdgRequest" name="nprFidi_RecuperoNdgRequest"/>

         <wsdl:output message="impl:nprFidi_RecuperoNdgResponse" name="nprFidi_RecuperoNdgResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_RecuperoNdgSoapBinding" type="impl:NPR_Fidi_RecuperoNdg_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_RecuperoNdg">

         <wsdlsoap:operation soapAction="nprFidi_RecuperoNdg"/>

         <wsdl:input name="nprFidi_RecuperoNdgRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_RecuperoNdgResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_RecuperoNdgService">

      <wsdl:port binding="impl:NPR_Fidi_RecuperoNdgSoapBinding" name="NPR_Fidi_RecuperoNdg">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_RecuperoNdg"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
