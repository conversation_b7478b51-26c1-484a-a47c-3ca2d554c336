<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_IterProposta">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_IterPropostaResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_IterPropostaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_IterPropostaRequest">

      <wsdl:part element="impl:nprFidi_IterProposta" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_IterPropostaResponse">

      <wsdl:part element="impl:nprFidi_IterPropostaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_IterProposta_SEI">

      <wsdl:operation name="nprFidi_IterProposta">

         <wsdl:input message="impl:nprFidi_IterPropostaRequest" name="nprFidi_IterPropostaRequest"/>

         <wsdl:output message="impl:nprFidi_IterPropostaResponse" name="nprFidi_IterPropostaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_IterPropostaSoapBinding" type="impl:NPR_Fidi_IterProposta_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_IterProposta">

         <wsdlsoap:operation soapAction="nprFidi_IterProposta"/>

         <wsdl:input name="nprFidi_IterPropostaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_IterPropostaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_IterPropostaService">

      <wsdl:port binding="impl:NPR_Fidi_IterPropostaSoapBinding" name="NPR_Fidi_IterProposta">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_IterProposta"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
