package it.usi.xframe.gwc.pfutil;


import it.usi.xframe.gwc.bfintf.IGwcConstDefinitions;
import it.usi.xframe.gwc.bfintf.IGwcMainServiceFacade;
import it.usi.xframe.gwc.bfutil.GwcServiceFactory;
import it.usi.xframe.gwc.bfutil.ResultResponseClass;
import it.usi.xframe.gwc.bfutil.params.FieldsParams;
import it.usi.xframe.gwc.bfutil.rc.FieldsResponseClass;
import it.usi.xframe.gwc.bfutil.rc.TransactionResponseClass;
import it.usi.xframe.gwc.bfutil.rc.XmlWebServicesResponseClass;
import it.usi.xframe.gwc.bfutil.vo.BanksList;
import it.usi.xframe.system.errors.XFRException;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class GwcDelegate implements IGwcConstDefinitions {
	private GwcServiceFactory factory = null;
	private IGwcMainServiceFacade facade = null;

	private Log logger = LogFactory.getLog(this.getClass());
	
	private void createFacade() throws XFRException {
		if (facade == null)
		{
			if (factory == null)
				factory = GwcServiceFactory.getInstance();
			facade = factory.getGwcMainServiceFacade();
		}
	}

	private void disposeFacade() throws XFRException {
		factory.dispose(facade);
		facade = null;
	}

    //************** all Tables involved in RECALIBRATION PROJECT (DB transaction atomically esecuted) ****************
	public BanksList getBanks() throws XFRException {
		logger.info("executing GwcDelegate.getBanks()...-START");

		BanksList dataRet = null;
		Object obj = BaseObjectsCacher.getInstance().getFromCache(BANKS_LIST_CACHENAME);

		if (obj == null) {
			try {
				this.createFacade();
				logger.info("executing GwcDelegate.getBanks() from DB");
				dataRet = facade.getBanks();
				logger.info("saving Banks List in cache");
				BaseObjectsCacher.getInstance().addToCache(BANKS_LIST_CACHENAME, dataRet);
			} catch (Exception e) {
				throw new XFRException(e);
			}
			finally {
				this.disposeFacade();
			}
		}
		else {
			logger.info("getting Banks from cache. Obj = " + BANKS_LIST_CACHENAME);
			dataRet = (BanksList) obj;
		}

		logger.info("executed GwcDelegate.getBanks()...-END");
		return dataRet;
	}
	
	public ResultResponseClass callService(String service, String params, String sep1, String sep2) throws XFRException {
		logger.info("executing GwcDelegate.callService()... START");
		try {
			this.createFacade();
			logger.info("executing GwcDelegate.callService()... END");
			ResultResponseClass rc = new ResultResponseClass();
			rc.setXmlResponse(facade.callService(service, params, sep1, sep2));
			return rc;
		} catch (Exception e) {
			throw new XFRException(e);
		}
		finally {
			this.disposeFacade();
		}
	}

	public String getFieldFromResponse(String xmlResponse, String tagFrom, String tag) throws XFRException {
		logger.info("executing GwcDelegate.getFieldFromResponse()... START");
		try {
			this.createFacade();
			logger.info("executing GwcDelegate.getFieldFromResponse()... END");
			String s = facade.getFieldFromResponse(xmlResponse, tagFrom, tag, 0);
			return s;
		} catch (Exception e) {
			throw new XFRException(e);
		}
		finally {
			this.disposeFacade();
		}
	}

	public String getFieldFromResponse(String xmlResponse, String tagFrom, String tag, int record) throws XFRException {
		logger.info("executing GwcDelegate.getFieldFromResponse()... START");
		try {
			this.createFacade();
			logger.info("executing GwcDelegate.getFieldFromResponse()... END");
			String s = facade.getFieldFromResponse(xmlResponse, tagFrom, tag, record);
			return s;
		} catch (Exception e) {
			throw new XFRException(e);
		}
		finally {
			this.disposeFacade();
		}
	}
	
	public boolean UCF_db2_oracle_copy() throws XFRException {
		logger.info("executing GwcDelegate.NPF_db2_oracle_copy()... START");
		try {
			this.createFacade();
			logger.info("executing GwcDelegate.NPF_db2_oracle_copy()... END");
			boolean s = facade.UCF_db2_oracle_copy();
			return s;
		} catch (Exception e) {
			throw new XFRException(e);
		}
		finally {
			this.disposeFacade();
		}
	}	
	
	public TransactionResponseClass transactions() throws XFRException {
		logger.info("executing RBGDelegate.transactions()...");
		try 
		{
			this.createFacade();
			return facade.transactions();
		} 
		catch (Exception e)	{ 	throw new XFRException(e); }
		finally 			{	this.disposeFacade();	}
	}
	
	public FieldsResponseClass fields(FieldsParams par) throws XFRException {
		logger.info("executing RBGDelegate.fields()...");
		try 
		{
			this.createFacade();
			return facade.fields(par);
		} 
		catch (Exception e)	{ 	throw new XFRException(e); }
		finally 			{	this.disposeFacade();	}
	}
	
	public XmlWebServicesResponseClass xmlWebServices() throws XFRException {
		logger.info("executing GWCDelegate.xmlWebServices()...");
		try 
		{
			this.createFacade();
			return facade.xmlWebServices();
		} 
		catch (Exception e)	{ 	throw new XFRException(e); }
		finally 			{	this.disposeFacade();	}
	}	
			
}