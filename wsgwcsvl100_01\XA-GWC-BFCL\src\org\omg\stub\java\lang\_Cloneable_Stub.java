// Stub class generated by rmic, do not edit.
// Contents subject to change without notice.

package org.omg.stub.java.lang;

import java.lang.Cloneable;
import java.lang.String;
import java.rmi.Remote;
import javax.rmi.CORBA.Stub;

public class _Cloneable_Stub extends Stub implements Cloneable,
Remote {
    
    private static final String[] _type_ids = {
        "RMI:java.lang.Cloneable:0000000000000000"
    };
    
    public String[] _ids() { 
        return (String [] )  _type_ids.clone();
    }
}
