<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns:impl="http://wsutil.gwc.xframe.usi.it" xmlns:intf="http://wsutil.gwc.xframe.usi.it" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
 <wsdl:types>
  <schema elementFormDefault="qualified" targetNamespace="http://wsutil.gwc.xframe.usi.it" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
   <element name="nprFidi_Delibera">
    <complexType>
     <sequence>
      <element name="x00" nillable="true" type="xsd:string"/>
      <element name="x01" nillable="true" type="xsd:string"/>
      <element name="x02" nillable="true" type="xsd:string"/>
      <element name="x03" nillable="true" type="xsd:string"/>
      <element name="x04" nillable="true" type="xsd:string"/>
      <element name="x05" nillable="true" type="xsd:string"/>
      <element name="x06" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
   <element name="nprFidi_DeliberaResponse">
    <complexType>
     <sequence>
      <element name="nprFidi_DeliberaReturn" nillable="true" type="xsd:string"/>
     </sequence>
    </complexType>
   </element>
  </schema>
 </wsdl:types>

   <wsdl:message name="nprFidi_DeliberaRequest">

      <wsdl:part element="impl:nprFidi_Delibera" name="parameters"/>

   </wsdl:message>

   <wsdl:message name="nprFidi_DeliberaResponse">

      <wsdl:part element="impl:nprFidi_DeliberaResponse" name="parameters"/>

   </wsdl:message>

   <wsdl:portType name="NPR_Fidi_Delibera_SEI">

      <wsdl:operation name="nprFidi_Delibera">

         <wsdl:input message="impl:nprFidi_DeliberaRequest" name="nprFidi_DeliberaRequest"/>

         <wsdl:output message="impl:nprFidi_DeliberaResponse" name="nprFidi_DeliberaResponse"/>

      </wsdl:operation>

   </wsdl:portType>

   <wsdl:binding name="NPR_Fidi_DeliberaSoapBinding" type="impl:NPR_Fidi_Delibera_SEI">

      <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>

      <wsdl:operation name="nprFidi_Delibera">

         <wsdlsoap:operation soapAction="nprFidi_Delibera"/>

         <wsdl:input name="nprFidi_DeliberaRequest">

            <wsdlsoap:body use="literal"/>

         </wsdl:input>

         <wsdl:output name="nprFidi_DeliberaResponse">

            <wsdlsoap:body use="literal"/>

         </wsdl:output>

      </wsdl:operation>

   </wsdl:binding>

   <wsdl:service name="NPR_Fidi_DeliberaService">

      <wsdl:port binding="impl:NPR_Fidi_DeliberaSoapBinding" name="NPR_Fidi_Delibera">

         <wsdlsoap:address location="http://localhost:9080/XA-GWC-WS/services/NPR_Fidi_Delibera"/>

      </wsdl:port>

   </wsdl:service>

</wsdl:definitions>
