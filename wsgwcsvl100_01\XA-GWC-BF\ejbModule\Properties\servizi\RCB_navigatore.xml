<?xml version="1.0" encoding="UTF-8"?>
<!-- Generated by XMLGEN Versione="4.14.285" utente="chavan" Timestamp="30/01/2004 9.33.19" -->
<service  xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet" >
    <dispenser>
        <road>
            <request>
                <input>
					<param name="X80I-NUM-QUEST"/>
					<param name="X80I-FUNZIONE"/>
					<param name="X80I-CODICE-SCREEN"/>
					<param name="X80I-USER-ID"/>
					<param name="X80I-NDG"/>
					<param name="X80I-CODFISC"/>
					<param name="X80I-PIVA"/>
					<param name="X80I-MOT-DELETE"/>
					<param name="X80I-SERVER"/>
					<param name="X80I-BANCA"/>
					<param name="X80I-USER-INS"/>
					<param name="X80I-USER-VAL"/>
					<param name="X80I-DT-INS-QUEST"/>              
                </input>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </road>
        <providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
    </dispenser>
    <provider>
        <protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol</protocolclass>
        <channelclass>it.usi.webfactory.channels.I18NSecureCPICTransaction</channelclass>
    </provider>
    <protocol>
        <bridge>
            <request>
                <input/>
                <output/>
            </request>
            <response>
                <input/>
                <output/>
            </response>
        </bridge>
        <params>
            <hostService>XPO080-INPUT</hostService>
            <applBankNumber>01</applBankNumber>
            <servBankNumber>01</servBankNumber>
            <version>0100</version>
        </params>
    </protocol>
    <channel>
        <params>
            <transaction>XP80</transaction>
            <timeout>30000</timeout>
            <applid>CICS</applid>
        </params>
    </channel>
</service>
