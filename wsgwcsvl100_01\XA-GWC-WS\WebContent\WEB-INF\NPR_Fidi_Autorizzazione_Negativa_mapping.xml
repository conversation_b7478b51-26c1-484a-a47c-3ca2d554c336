<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411998878411">
      <package-mapping id="PackageMapping_1411998878411">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411998878411">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411998878411">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411998878411">
         <service-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Autorizzazione_NegativaService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411998878411">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_Autorizzazione_NegativaService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411998878411">
            <port-name>NPR_Fidi_Autorizzazione_Negativa</port-name>
            <java-port-name>NPR_Fidi_Autorizzazione_Negativa</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411998878411">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_Fidi_Autorizzazione_Negativa_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411998878411">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_Autorizzazione_Negativa_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411998878411">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_Fidi_Autorizzazione_NegativaSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411998878411">
            <java-method-name>nprFidi_Autorizzazione_Negativa</java-method-name>
            <wsdl-operation>nprFidi_Autorizzazione_Negativa</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998878411">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998878411">
                  <wsdl-message id="WSDLMessage_1411998878411">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_Autorizzazione_NegativaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x00</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998878412">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998878412">
                  <wsdl-message id="WSDLMessage_1411998878412">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_Autorizzazione_NegativaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x01</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998878413">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998878413">
                  <wsdl-message id="WSDLMessage_1411998878413">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_Autorizzazione_NegativaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x02</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998878414">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998878414">
                  <wsdl-message id="WSDLMessage_1411998878414">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_Autorizzazione_NegativaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x03</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998878415">
               <param-position>4</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998878415">
                  <wsdl-message id="WSDLMessage_1411998878415">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_Autorizzazione_NegativaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x04</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411998878416">
               <param-position>5</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411998878416">
                  <wsdl-message id="WSDLMessage_1411998878416">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprFidi_Autorizzazione_NegativaRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x15</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411998878411">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411998878417">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>nprFidi_Autorizzazione_NegativaResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>nprFidi_Autorizzazione_NegativaReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
