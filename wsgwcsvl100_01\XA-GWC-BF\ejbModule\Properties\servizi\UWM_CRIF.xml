<service xmlns:xsl="http://www.w3.org/1999/XSL/Transform" security="intranet">
	<dispenser>
		<road>
			<request>
				<input>
					<param name="UWMC042I_BANCA_X"/>
					<param name="UWMC042I_UFFICIO_5"/>
					<param name="UWMC042I_NDG"/>
					<param name="UWMC042I_CODI_RAPP"/>
					<param name="UWMC042I_LIRE_EURO_INPUT"/>
					<param name="UWMC042I_DATA_FINA"/>
					<param name="UWMC042I_PROV_RICH"/>
					<param name="UWMC042I_FASE_OPER"/>
					<param name="UWMC042I_TIPO_OPER"/>
					<param name="UWMC042I_NUM_RATE"/>
					<param name="UWMC042I_PERI_RATE"/>
					<param name="UWMC042I_CAPI_FINA"/>
					<param name="UWMC042I_IMPO_RATA"/>
					<param name="UWMC042I_CODI_LEGA"/>
					<param name="UWMC042I_NUME_RICO"/>
					<param name="UWMC042I_CODI_ANAG"/>
					<param name="UWMC042I_TIPO_RICHIESTA"/>
					<param name="UWMC042I_CRIF_CONT"/>
					<param name="UWMC042I_STAT_OPER"/>
					<param name="UWMC042I_FLAG_ANNULLAM"/>
					<param name="UWMC042I_LIRE_EURO_OUTPUT"/>
					<param name="UWMC042I_BANCA"/>
					<param name="UWMC042I_INTESTAZIONE_A"/>
					<param name="UWMC042I_INTESTAZIONE_B"/>
					<param name="UWMC042I_CODI_FISC"/>
					<param name="UWMC042I_VIA_RES"/>
					<param name="UWMC042I_CAP_RES"/>
					<param name="UWMC042I_COMUNE_RES"/>
					<param name="UWMC042I_PROV_RES"/>
					<param name="UWMC042I_SESSO"/>
					<param name="UWMC042I_DT_NASC"/>
					<param name="UWMC042I_COMUNE_NASC"/>
					<param name="UWMC042I_PROV_NASC"/>
					<param name="UWMC042I_SERVIZIO"/>
					<param name="UWMC042I_FLAG_FORZ_CRIF"/>
					<param name="UWMC042I_APPL_CHIAMANTE"/>
					<param name="XF_GAUSS_ID"/>
				</input>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</road>
		<providerclass>it.usi.webfactory.provider.I18NCPICProvider</providerclass>
	</dispenser>
	<provider>
		<protocolclass>it.usi.webfactory.protocols.I18NGaussProtocol4JCA</protocolclass>
		<channelclass>it.usi.webfactory.channels.I18NSecureJCATransaction</channelclass>
	</provider>
	<protocol>
		<bridge>
			<request>
				<input/>
				<output/>
			</request>
			<response>
				<input/>
				<output/>
			</response>
		</bridge>
		<params>
			<hostService>UWM_CRIF</hostService>
			<applBankNumber>01</applBankNumber>
			<servBankNumber>99</servBankNumber>
			<version>0001</version>
		</params>
	</protocol>
	<channel>
		<params>
			<transaction>WUW1</transaction>
			<program>PC00WUW1</program>
			<timeout>15000</timeout>
			<applid>CICS</applid>
		</params>
	</channel>
</service>