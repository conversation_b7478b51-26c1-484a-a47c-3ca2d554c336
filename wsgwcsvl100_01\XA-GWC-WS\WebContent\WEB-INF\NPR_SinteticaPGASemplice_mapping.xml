<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE java-wsdl-mapping PUBLIC "-//IBM Corporation, Inc.//DTD J2EE JAX-RPC mapping 1.0//EN" "http://www.ibm.com/webservices/dtd/j2ee_jaxrpc_mapping_1_0.dtd">

   <java-wsdl-mapping id="JavaWSDLMapping_1411999692850">
      <package-mapping id="PackageMapping_1411999692850">
         <package-type>it.usi.xframe.gwc.wsutil</package-type>
         <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
      </package-mapping>
      <java-xml-type-mapping id="JavaXMLTypeMapping_1411999692850">
         <class-type>java.lang.String</class-type>
         <root-type-qname id="RootTypeQname_1411999692850">
            <namespaceURI>http://www.w3.org/2001/XMLSchema</namespaceURI>
            <localpart>string</localpart>
         </root-type-qname>
         <qname-scope>simpleType</qname-scope>
      </java-xml-type-mapping>
      <service-interface-mapping id="ServiceInterfaceMapping_1411999692850">
         <service-interface>it.usi.xframe.gwc.wsutil.NPR_SinteticaPGASempliceService</service-interface>
         <wsdl-service-name id="WSDLServiceName_1411999692850">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_SinteticaPGASempliceService</localpart>
         </wsdl-service-name>
         <port-mapping id="PortMapping_1411999692850">
            <port-name>NPR_SinteticaPGASemplice</port-name>
            <java-port-name>NPR_SinteticaPGASemplice</java-port-name>
         </port-mapping>
      </service-interface-mapping>
      <service-endpoint-interface-mapping id="ServiceEndpointInterfaceMapping_1411999692850">
         <service-endpoint-interface>it.usi.xframe.gwc.wsutil.NPR_SinteticaPGASemplice_SEI</service-endpoint-interface>
         <wsdl-port-type id="WSDLPortType_1411999692850">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_SinteticaPGASemplice_SEI</localpart>
         </wsdl-port-type>
         <wsdl-binding id="WSDLBinding_1411999692850">
            <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
            <localpart>NPR_SinteticaPGASempliceSoapBinding</localpart>
         </wsdl-binding>
         <service-endpoint-method-mapping id="ServiceEndpointMethodMapping_1411999692850">
            <java-method-name>nprSinteticaPGASemplice</java-method-name>
            <wsdl-operation>nprSinteticaPGASemplice</wsdl-operation>
            <wrapped-element></wrapped-element>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692850">
               <param-position>0</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692850">
                  <wsdl-message id="WSDLMessage_1411999692850">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x01</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692851">
               <param-position>1</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692851">
                  <wsdl-message id="WSDLMessage_1411999692851">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x02</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692852">
               <param-position>2</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692852">
                  <wsdl-message id="WSDLMessage_1411999692852">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x03</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692853">
               <param-position>3</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692853">
                  <wsdl-message id="WSDLMessage_1411999692853">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x04</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692854">
               <param-position>4</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692854">
                  <wsdl-message id="WSDLMessage_1411999692854">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x05</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692855">
               <param-position>5</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692855">
                  <wsdl-message id="WSDLMessage_1411999692855">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x07</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692856">
               <param-position>6</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692856">
                  <wsdl-message id="WSDLMessage_1411999692856">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x97</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <method-param-parts-mapping id="MethodParamPartsMapping_1411999692857">
               <param-position>7</param-position>
               <param-type>java.lang.String</param-type>
               <wsdl-message-mapping id="WSDLMessageMapping_1411999692857">
                  <wsdl-message id="WSDLMessage_1411999692857">
                     <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                     <localpart>nprSinteticaPGASempliceRequest</localpart>
                  </wsdl-message>
                  <wsdl-message-part-name>x98</wsdl-message-part-name>
                  <parameter-mode>IN</parameter-mode>
               </wsdl-message-mapping>
            </method-param-parts-mapping>
            <wsdl-return-value-mapping id="WSDLReturnValueMapping_1411999692850">
               <method-return-value>java.lang.String</method-return-value>
               <wsdl-message id="WSDLMessage_1411999692858">
                  <namespaceURI>http://wsutil.gwc.xframe.usi.it</namespaceURI>
                  <localpart>nprSinteticaPGASempliceResponse</localpart>
               </wsdl-message>
               <wsdl-message-part-name>nprSinteticaPGASempliceReturn</wsdl-message-part-name>
            </wsdl-return-value-mapping>
         </service-endpoint-method-mapping>
      </service-endpoint-interface-mapping>
   </java-wsdl-mapping>
