/*
 * Created on Dec 20, 2007
 *
 * To change the template for this generated file go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
package it.usi.xframe.gwc.bfutil.params;

import java.util.HashMap;


/**
 * <AUTHOR>
 *
 * To change the template for this generated type comment go to
 * Window&gt;Preferences&gt;Java&gt;Code Generation&gt;Code and Comments
 */
public class Oracle_row_params extends Oracle_params {

	private HashMap fields = new HashMap(); // key=FIELD_NAME, value=FIELD_VALUE
	
	public Oracle_row_params()
	{
	}

	/**
	 * @return
	 */
	public HashMap getFields() {
		return fields;
	}

	/**
	 * @param map
	 */
	public void setFields(HashMap map) {
		fields = map;
	}

}
